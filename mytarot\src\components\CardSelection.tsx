'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import TarotCard from './TarotCard';
import { TarotCard as TarotCardType } from '@/types/tarot';
import { generateAllCards } from '@/data/generate-cards';
import { useTranslations } from 'next-intl';

interface CardSelectionProps {
  onCardsSelected: (cards: TarotCardType[]) => void;
}

export default function CardSelection({ onCardsSelected }: CardSelectionProps) {
  const [allCards, setAllCards] = useState<TarotCardType[]>([]);
  const [selectedCards, setSelectedCards] = useState<TarotCardType[]>([]);
  const [shuffledPositions, setShuffledPositions] = useState<Array<{x: number, y: number, rotation: number}>>([]);
  const t = useTranslations('reading');

  useEffect(() => {
    // 生成所有78张牌
    const cards = generateAllCards();
    setAllCards(cards);

    // 创建更美观的卡牌布局 - 多层螺旋分布
    const positions = cards.map((_, index) => {
      // 使用多层螺旋算法，确保卡牌不超出屏幕
      const layer = Math.floor(index / 12); // 每层12张卡
      const angleStep = 30; // 每张卡间隔30度
      const baseAngle = (index % 12) * angleStep + layer * 15; // 每层错开15度
      const radius = Math.min(25 + layer * 8, 35); // 限制最大半径，确保不超出屏幕

      // 添加一些随机偏移，但保持在安全范围内
      const randomOffsetX = ((index * 17) % 21 - 10) * 0.5; // -5 to 5
      const randomOffsetY = ((index * 13) % 21 - 10) * 0.5; // -5 to 5

      const x = Math.cos(baseAngle * Math.PI / 180) * radius + randomOffsetX;
      const y = Math.sin(baseAngle * Math.PI / 180) * radius * 0.6 + randomOffsetY; // 压扁椭圆

      return {
        x: Math.max(-35, Math.min(35, x)), // 限制在 -35% 到 35% 范围内
        y: Math.max(-25, Math.min(25, y)), // 限制在 -25% 到 25% 范围内
        rotation: ((index * 23) % 60 - 30) * 0.7 // 减小旋转角度
      };
    });
    setShuffledPositions(positions);
  }, []);

  const handleCardClick = (card: TarotCardType) => {
    // 检查是否已达到最大选择数量
    if (selectedCards.length >= 3) return;

    // 检查卡牌是否已被选择，防止重复选择
    if (isCardSelected(card)) return;

    const newSelectedCards = [...selectedCards, card];
    setSelectedCards(newSelectedCards);

    if (newSelectedCards.length === 3) {
      // 延迟一下让用户看到第三张卡的选择效果
      setTimeout(() => {
        onCardsSelected(newSelectedCards);
      }, 500);
    }
  };

  const isCardSelected = (card: TarotCardType) => {
    return selectedCards.some(selected => selected.id === card.id);
  };

  return (
    <div className="relative w-full min-h-screen overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(147,51,234,0.1)_0%,transparent_70%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(59,130,246,0.1)_0%,transparent_50%)]"></div>

      {/* 选择进度指示器 */}
      <div className="absolute top-4 md:top-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="bg-white/10 backdrop-blur-sm rounded-full px-4 md:px-6 py-2 md:py-3 border border-white/20">
          <p className="text-white text-sm md:text-lg font-semibold">
            {t('selectedCards', { count: selectedCards.length })} / 3
          </p>
        </div>
      </div>

      {/* 选中的卡牌预览 */}
      <div className="absolute top-16 md:top-20 left-1/2 transform -translate-x-1/2 z-20 flex gap-2 md:gap-4">
        {[0, 1, 2].map((index) => (
          <div
            key={index}
            className={`w-12 h-18 md:w-16 md:h-24 rounded-lg border-2 border-dashed ${
              selectedCards[index]
                ? 'border-purple-400 bg-purple-400/20'
                : 'border-purple-600/50 bg-purple-600/10'
            } flex items-center justify-center`}
          >
            {selectedCards[index] && (
              <motion.div
                initial={{ scale: 0, rotate: 180 }}
                animate={{ scale: 1, rotate: 0 }}
                className="w-full h-full"
              >
                <TarotCard
                  card={selectedCards[index]}
                  isRevealed={false}
                  size="small"
                />
              </motion.div>
            )}
          </div>
        ))}
      </div>

      {/* 卡牌散布区域 */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="relative w-full max-w-6xl h-full">
          <AnimatePresence>
            {allCards.map((card, index) => {
              const position = shuffledPositions[index];
              if (!position) return null;

              const isSelected = isCardSelected(card);
              const isDisabled = selectedCards.length >= 3 && !isSelected;

              return (
                <motion.div
                  key={card.id}
                  className={`absolute ${isSelected ? 'cursor-default' : (isDisabled ? 'cursor-not-allowed' : 'cursor-pointer')}`}
                  style={{
                    left: `${50 + position.x}%`,
                    top: `${50 + position.y}%`,
                    transform: `translate(-50%, -50%) rotate(${position.rotation}deg)`,
                    zIndex: isSelected ? 15 : (10 - Math.floor(index / 12)), // 分层z-index，后面的层级更低
                  }}
                  initial={{
                    scale: 0,
                    opacity: 0,
                    rotate: position.rotation + 180
                  }}
                  animate={{
                    scale: isSelected ? 1.1 : (isDisabled ? 0.7 : 1),
                    opacity: isSelected ? 1 : (isDisabled ? 0.3 : 1),
                    rotate: position.rotation
                  }}
                  transition={{
                    duration: 0.4,
                    delay: index * 0.01,
                    type: "tween",
                    ease: "easeOut"
                  }}
                  whileHover={!isDisabled && !isSelected ? {
                    scale: 1.05,
                    zIndex: 20
                  } : {}}
                  onClick={() => !isDisabled && !isSelected && handleCardClick(card)}
                >
                  <TarotCard
                    card={card}
                    isRevealed={false}
                    isSelected={isSelected}
                    size="medium"
                  />
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>
      </div>

      {/* 指导文本 */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <motion.div
          className="bg-white/10 backdrop-blur-sm rounded-2xl px-8 py-4 text-center"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2 }}
        >
          <p className="text-white text-lg mb-2">
            {selectedCards.length === 0 && t('selectFirstCard')}
            {selectedCards.length === 1 && t('selectSecondCard')}
            {selectedCards.length === 2 && t('selectThirdCard')}
            {selectedCards.length === 3 && t('cardsComplete')}
          </p>
          <p className="text-purple-200 text-sm">
            {t('selectCardsDescription')}
          </p>
        </motion.div>
      </div>
    </div>
  );
}
