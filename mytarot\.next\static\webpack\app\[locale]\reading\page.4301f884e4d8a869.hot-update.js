"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/hooks/useIPLanguage.ts":
/*!************************************!*\
  !*** ./src/hooks/useIPLanguage.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIPLanguage: () => (/* binding */ useIPLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useIPLanguage auto */ \nfunction useIPLanguage() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('en');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [ipInfo, setIpInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useIPLanguage.useEffect\": ()=>{\n            const detectLanguageFromIP = {\n                \"useIPLanguage.useEffect.detectLanguageFromIP\": async ()=>{\n                    try {\n                        // 优先使用我们自己的API（避免跨域问题）\n                        try {\n                            console.log('使用后端API检测地理位置...');\n                            const response = await fetch('/api/detect-location', {\n                                signal: AbortSignal.timeout(8000),\n                                headers: {\n                                    'Accept': 'application/json'\n                                }\n                            });\n                            if (response.ok) {\n                                const result = await response.json();\n                                console.log('后端API响应:', result);\n                                if (result.success && result.data) {\n                                    const { country, countryCode, ip } = result.data;\n                                    const info = {\n                                        country: country,\n                                        countryCode: countryCode,\n                                        ip: ip\n                                    };\n                                    setIpInfo(info);\n                                    // 判断是否为中国\n                                    const isChinese = country === '中国' || country === 'China' || countryCode === 'CN';\n                                    if (isChinese) {\n                                        setLanguage('zh');\n                                        console.log('🇨🇳 检测到中国IP，切换到中文');\n                                    } else {\n                                        setLanguage('en');\n                                        console.log('🌐 检测到海外IP，使用英文');\n                                    }\n                                    setIsLoading(false);\n                                    return; // 成功检测，直接返回\n                                }\n                            }\n                        } catch (error) {\n                            console.warn('后端API检测失败:', error);\n                        }\n                        // 后端API失败，尝试前端直接调用\n                        console.log('尝试前端直接IP检测...');\n                        const apis = [\n                            {\n                                url: 'https://ipapi.co/json/',\n                                parseCountry: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>data.country_code\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            },\n                            {\n                                url: 'https://ip-api.com/json/',\n                                parseCountry: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>data.countryCode\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            },\n                            {\n                                url: 'https://ipinfo.io/json',\n                                parseCountry: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>data.country\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            }\n                        ];\n                        let detected = false;\n                        for (const service of ipServices){\n                            try {\n                                console.log(\"尝试IP检测服务: \".concat(service.url));\n                                const response = await fetch(service.url, {\n                                    signal: AbortSignal.timeout(5000),\n                                    headers: {\n                                        'Accept': 'application/json'\n                                    }\n                                });\n                                if (!response.ok) {\n                                    console.warn(\"服务 \".concat(service.url, \" 返回错误: \").concat(response.status));\n                                    continue;\n                                }\n                                const data = await response.json();\n                                console.log(\"\".concat(service.url, \" 响应:\"), data);\n                                const parsed = service.parser(data);\n                                if (parsed.ip && parsed.country) {\n                                    const info = {\n                                        country: parsed.country,\n                                        countryCode: parsed.countryCode || '',\n                                        ip: parsed.ip\n                                    };\n                                    setIpInfo(info);\n                                    // 判断是否为中国\n                                    const isChinese = parsed.country === '中国' || parsed.country === 'China' || parsed.countryCode === 'CN';\n                                    if (isChinese) {\n                                        setLanguage('zh');\n                                        console.log('🇨🇳 检测到中国IP，切换到中文');\n                                    } else {\n                                        setLanguage('en');\n                                        console.log('🌐 检测到海外IP，使用英文');\n                                    }\n                                    detected = true;\n                                    break;\n                                }\n                            } catch (error) {\n                                console.warn(\"IP检测服务 \".concat(service.url, \" 失败:\"), error);\n                                continue;\n                            }\n                        }\n                        // 如果所有IP检测服务都失败，使用浏览器语言\n                        if (!detected) {\n                            var _navigator_languages;\n                            console.log('所有IP检测服务都失败，使用浏览器语言检测');\n                            const browserLang = navigator.language || ((_navigator_languages = navigator.languages) === null || _navigator_languages === void 0 ? void 0 : _navigator_languages[0]) || 'en';\n                            if (browserLang.startsWith('zh')) {\n                                setLanguage('zh');\n                                console.log('🌐 根据浏览器语言使用中文');\n                            } else {\n                                setLanguage('en');\n                                console.log('🌐 根据浏览器语言使用英文');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('语言检测失败:', error);\n                        // 默认使用英文\n                        setLanguage('en');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useIPLanguage.useEffect.detectLanguageFromIP\"];\n            detectLanguageFromIP();\n        }\n    }[\"useIPLanguage.useEffect\"], []);\n    return {\n        language,\n        isLoading,\n        ipInfo,\n        setLanguage\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useIPLanguage.ts\n"));

/***/ })

});