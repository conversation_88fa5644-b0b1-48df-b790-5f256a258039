<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Gradient -->
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e1b4b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e1b4b;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="glow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#a855f7;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg)"/>
  <rect width="1200" height="630" fill="url(#glow)"/>
  
  <!-- Stars -->
  <circle cx="150" cy="100" r="2" fill="#fbbf24" opacity="0.8"/>
  <circle cx="300" cy="150" r="1.5" fill="#fbbf24" opacity="0.6"/>
  <circle cx="450" cy="80" r="2.5" fill="#fbbf24" opacity="0.9"/>
  <circle cx="750" cy="120" r="1.8" fill="#fbbf24" opacity="0.7"/>
  <circle cx="900" cy="180" r="2.2" fill="#fbbf24" opacity="0.8"/>
  <circle cx="1050" cy="90" r="1.6" fill="#fbbf24" opacity="0.6"/>
  <circle cx="200" cy="500" r="1.8" fill="#fbbf24" opacity="0.7"/>
  <circle cx="400" cy="520" r="2.1" fill="#fbbf24" opacity="0.8"/>
  <circle cx="800" cy="480" r="1.9" fill="#fbbf24" opacity="0.7"/>
  <circle cx="1000" cy="550" r="2.3" fill="#fbbf24" opacity="0.9"/>
  
  <!-- Main Title -->
  <text x="600" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="72" font-weight="bold">
    Mystic Tarot
  </text>
  
  <!-- Subtitle -->
  <text x="600" y="340" text-anchor="middle" fill="#c084fc" font-family="Arial, sans-serif" font-size="36" font-weight="normal">
    Free AI Tarot Reading Online
  </text>
  
  <!-- Description -->
  <text x="600" y="400" text-anchor="middle" fill="#e9d5ff" font-family="Arial, sans-serif" font-size="24" font-weight="normal">
    Professional Psychic Tarot Reading with AI-Powered Insights
  </text>
  
  <!-- Decorative Elements -->
  <g transform="translate(100, 300)">
    <!-- Moon -->
    <circle cx="0" cy="0" r="25" fill="#fbbf24" opacity="0.8"/>
    <circle cx="8" cy="-8" r="20" fill="#1e1b4b"/>
  </g>
  
  <g transform="translate(1100, 300)">
    <!-- Star -->
    <polygon points="0,-20 6,-6 20,0 6,6 0,20 -6,6 -20,0 -6,-6" fill="#fbbf24" opacity="0.8"/>
  </g>
  
  <!-- Crystal Ball -->
  <g transform="translate(600, 480)">
    <circle cx="0" cy="0" r="40" fill="url(#glow)" opacity="0.6"/>
    <circle cx="0" cy="0" r="35" fill="#a855f7" opacity="0.3"/>
    <circle cx="-10" cy="-10" r="8" fill="white" opacity="0.4"/>
  </g>
</svg>
