# 🚀 Cloudflare Pages 部署指南

## 📋 部署前准备

### 1. 确保已安装 Wrangler CLI
```bash
npm install -g wrangler
```

### 2. 登录 Cloudflare
```bash
wrangler login
```

### 3. 检查登录状态
```bash
wrangler whoami
```

## 🗄️ 数据库配置

### 1. 创建 D1 数据库
```bash
# 创建生产数据库
wrangler d1 create mystic-tarot-db

# 创建预览数据库
wrangler d1 create mystic-tarot-db-preview
```

### 2. 更新 wrangler.toml 配置
将创建数据库时返回的 `database_id` 更新到 `wrangler.toml` 文件中：

```toml
# 生产数据库
[[d1_databases]]
binding = "DB"
database_name = "mystic-tarot-db"
database_id = "你的生产数据库ID"

# 预览数据库配置
[[env.preview.d1_databases]]
binding = "DB"
database_name = "mystic-tarot-db-preview"
database_id = "你的预览数据库ID"
```

### 3. 执行数据库迁移
```bash
# 生成 Prisma 客户端
npm run db:generate

# 迁移生产数据库
wrangler d1 execute mystic-tarot-db --file=./prisma/migrations/schema.sql --config=wrangler-db.toml

# 迁移预览数据库
wrangler d1 execute mystic-tarot-db-preview --file=./prisma/migrations/schema.sql --config=wrangler-db.toml
```

## 🔨 构建和部署

### 1. 构建项目
```bash
npm run build
```

### 2. 部署到 Cloudflare Pages
```bash
# 方式1: 使用自动化脚本
npm run deploy

# 方式2: 手动部署
wrangler pages deploy out --project-name=mystic-tarot
```

## ⚙️ 环境变量配置

### 在 Cloudflare Pages 控制台设置以下环境变量：

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 进入 Pages → 你的项目 → Settings → Environment variables
3. 添加以下环境变量：

#### 认证相关
```
NEXTAUTH_URL=https://your-domain.pages.dev
NEXTAUTH_SECRET=mystic-tarot-secret-key-2024
```

#### OAuth 配置
```
GOOGLE_CLIENT_ID=265729365221-er2q8kuk589k7hln7m8cb5m61okr4eiv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GITHUB_ID=********************
GITHUB_SECRET=****************************************
```

#### OpenAI API
```
OPENAI_API_KEY=sk-IslnGB05pQQBD9y9d8miirlBxBNVovnPNb2rHcg8MlYoYJzF
OPENAI_BASE_URL=https://api.chatanywhere.tech/v1
```

#### 邮件服务
```
EMAIL_SERVER_HOST=smtp.163.com
EMAIL_SERVER_PORT=465
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=FDk6ZzSiftf7XZ8K
EMAIL_FROM=<EMAIL>
```

#### 支付系统
```
CREEM_API_KEY=creem_test_3GlwT5RzjwOF2jCOsQMmGY
CREEM_PRODUCT_ID=prod_5f1A39IxIXP08P1qyLBoPK
NEXT_PUBLIC_CREEM_API_KEY=creem_test_3GlwT5RzjwOF2jCOsQMmGY
NEXT_PUBLIC_CREEM_PRODUCT_ID=prod_5f1A39IxIXP08P1qyLBoPK
```

## 🔧 常用命令

### 数据库操作
```bash
# 查看数据库列表
wrangler d1 list

# 查询数据库
wrangler d1 execute mystic-tarot-db --command="SELECT * FROM User LIMIT 5"

# 本地数据库操作（开发环境）
npm run db:push
npm run db:migrate
```

### 部署操作
```bash
# 快速部署
npm run cf:deploy

# 查看部署状态
wrangler pages deployment list --project-name=mystic-tarot
```

## 🌐 自定义域名配置

1. 在 Cloudflare Pages 控制台中点击 "Custom domains"
2. 添加你的域名
3. 按照提示配置 DNS 记录
4. 更新 `NEXTAUTH_URL` 环境变量为你的自定义域名

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `wrangler.toml` 中的 `database_id` 是否正确
   - 确保数据库迁移已成功执行

2. **环境变量未生效**
   - 确保在 Cloudflare Pages 控制台中正确设置了所有环境变量
   - 重新部署项目以应用新的环境变量

3. **构建失败**
   - 检查 `next.config.ts` 配置是否正确
   - 确保所有依赖都已安装

### 调试命令
```bash
# 查看构建日志
wrangler pages deployment tail --project-name=mystic-tarot

# 本地预览构建结果
npm run build && npx serve out
```

## 📝 部署检查清单

- [ ] Wrangler CLI 已安装并登录
- [ ] D1 数据库已创建
- [ ] 数据库迁移已执行
- [ ] `wrangler.toml` 配置正确
- [ ] 项目构建成功
- [ ] 环境变量已在 Cloudflare 控制台设置
- [ ] 部署成功
- [ ] 网站可正常访问
- [ ] 数据库功能正常
- [ ] 认证功能正常
- [ ] 支付功能正常

## 🎯 下一步

部署完成后，你可以：
1. 配置自定义域名
2. 设置 CDN 缓存策略
3. 配置监控和日志
4. 设置自动部署（通过 GitHub Actions）

---

**注意**: 请确保在生产环境中使用安全的密钥和凭据，不要在代码中硬编码敏感信息。
