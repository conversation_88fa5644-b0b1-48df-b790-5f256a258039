# Vercel 环境变量检查清单

## 必需的环境变量

请在 Vercel Dashboard → Settings → Environment Variables 中添加：

### 1. NextAuth 配置
```
NEXTAUTH_URL=https://your-actual-domain.vercel.app
NEXTAUTH_SECRET=mystic-tarot-secret-key-2024
```

### 2. Google OAuth
```
GOOGLE_CLIENT_ID=265729365221-er2q8kuk589k7hln7m8cb5m61okr4eiv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
```

### 3. GitHub OAuth
```
GITHUB_ID=********************
GITHUB_SECRET=****************************************
```

### 4. 邮件配置
```
EMAIL_SERVER_HOST=smtp.163.com
EMAIL_SERVER_PORT=465
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=FDk6ZzSiftf7XZ8K
EMAIL_FROM=<EMAIL>
```

### 5. OpenAI API
```
OPENAI_API_KEY=sk-IslnGB05pQQBD9y9d8miirlBxBNVovnPNb2rHcg8MlYoYJzF
OPENAI_BASE_URL=https://api.chatanywhere.tech/v1
```

### 6. Creem 支付
```
CREEM_API_KEY=creem_test_3GlwT5RzjwOF2jCOsQMmGY
CREEM_PRODUCT_ID=prod_5f1A39IxIXP08P1qyLBoPK
NEXT_PUBLIC_CREEM_API_KEY=creem_test_3GlwT5RzjwOF2jCOsQMmGY
NEXT_PUBLIC_CREEM_PRODUCT_ID=prod_5f1A39IxIXP08P1qyLBoPK
```

## 重要提醒

1. **NEXTAUTH_URL** 必须是你的实际 Vercel 域名
2. 所有环境变量都要设置为 Production, Preview, Development
3. 设置完环境变量后需要重新部署

## OAuth 回调 URL 更新

### Google Cloud Console
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 选择你的项目
3. 进入 APIs & Services → Credentials
4. 编辑 OAuth 2.0 客户端
5. 在 "Authorized redirect URIs" 中添加：
   ```
   https://your-actual-domain.vercel.app/api/auth/callback/google
   ```

### GitHub OAuth App
1. 访问 [GitHub Settings](https://github.com/settings/applications)
2. 选择你的 OAuth App
3. 更新 "Authorization callback URL" 为：
   ```
   https://your-actual-domain.vercel.app/api/auth/callback/github
   ```

## 数据库配置

确保在 Vercel 中添加了 Postgres 数据库，并且以下环境变量已自动生成：
- DATABASE_URL
- DIRECT_URL
