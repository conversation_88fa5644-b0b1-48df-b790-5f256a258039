"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/components/TarotCard.tsx":
/*!**************************************!*\
  !*** ./src/components/TarotCard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TarotCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TarotCard(param) {\n    let { card, isRevealed = false, isSelected = false, isReversed = false, size = 'large', onClick, className = '', style, showBack = true } = param;\n    const cardBackImage = '/images/card-back.webp';\n    // 根据尺寸设置样式\n    const sizeClasses = {\n        small: 'w-12 h-18',\n        medium: 'w-16 h-24',\n        large: 'w-24 h-36'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: \"relative cursor-pointer \".concat(sizeClasses[size], \" \").concat(className),\n        style: style,\n        onClick: onClick,\n        whileHover: {\n            scale: 1.05,\n            y: -10\n        },\n        whileTap: {\n            scale: 0.95\n        },\n        initial: {\n            opacity: 0,\n            y: 50\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-full \".concat(isSelected ? 'ring-4 ring-purple-400 ring-opacity-75' : '', \" rounded-lg overflow-hidden shadow-lg\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"absolute inset-0 backface-hidden\",\n                    initial: false,\n                    animate: {\n                        rotateY: isRevealed ? 180 : 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    style: {\n                        backfaceVisibility: 'hidden'\n                    },\n                    children: [\n                        showBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: cardBackImage,\n                            alt: \"Mystic tarot card back design - Free AI tarot reading\",\n                            fill: true,\n                            className: \"object-cover\",\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this),\n                        !showBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-indigo-700 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white text-4xl\",\n                                children: \"\\uD83D\\uDD2E\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                card && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"absolute inset-0 backface-hidden\",\n                    initial: false,\n                    animate: {\n                        rotateY: isRevealed ? 0 : -180\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    style: {\n                        backfaceVisibility: 'hidden'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full h-full \".concat(isReversed ? 'rotate-180' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: card.imagePath,\n                                alt: \"\".concat(card.nameEn, \" tarot card - Free AI tarot reading and psychic interpretation\"),\n                                fill: true,\n                                className: \"object-cover\",\n                                sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\",\n                                onError: ()=>{\n                                    console.error('Image failed to load:', card.imagePath);\n                                    console.error('Card data:', card);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold text-sm\",\n                                    children: [\n                                        card.nameEn,\n                                        isReversed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-yellow-400\",\n                                            children: \"↓\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-xs\",\n                                    children: isReversed ? 'Reversed' : 'Upright'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_c = TarotCard;\nvar _c;\n$RefreshReg$(_c, \"TarotCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TarotCard.tsx\n"));

/***/ })

});