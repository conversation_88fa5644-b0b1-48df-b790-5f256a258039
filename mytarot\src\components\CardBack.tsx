'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { memo } from 'react';

interface CardBackProps {
  isSelected?: boolean;
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

const CardBack = memo(function CardBack({
  isSelected = false,
  size = 'large',
  onClick,
  className = '',
  style
}: CardBackProps) {
  const cardBackImage = '/images/card-back.webp';

  // 根据尺寸设置样式
  const sizeClasses = {
    small: 'w-12 h-18',
    medium: 'w-16 h-24',
    large: 'w-24 h-36'
  };
  
  return (
    <motion.div
      className={`relative cursor-pointer ${sizeClasses[size]} ${className}`}
      style={style}
      onClick={onClick}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2 }}
    >
      <div className={`relative w-full h-full ${isSelected ? 'ring-4 ring-purple-400 ring-opacity-75' : ''} rounded-lg overflow-hidden shadow-lg`}>
        <Image
          src={cardBackImage}
          alt="Mystic tarot card back design - Free AI tarot reading"
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority={true}
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Kcp/9k="
        />
      </div>
    </motion.div>
  );
});

export default CardBack;
