import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  // 从环境变量获取域名，或使用默认值
  let baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://tarotgo.top'

  // 清理域名格式，移除引号并确保有协议
  baseUrl = baseUrl.replace(/['"]/g, '') // 移除引号
  if (!baseUrl.startsWith('http')) {
    baseUrl = `https://${baseUrl}`
  }
  
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',           // 禁止爬取API路由
          '/_next/',         // 禁止爬取Next.js内部文件
          '/admin/',         // 如果有管理后台，禁止爬取
        ],
      },
      {
        userAgent: 'GPTBot',  // 禁止OpenAI的爬虫
        disallow: '/',
      },
      {
        userAgent: 'ChatGPT-User',  // 禁止ChatGPT用户代理
        disallow: '/',
      },
      {
        userAgent: 'CCBot',   // 禁止Common Crawl
        disallow: '/',
      },
      {
        userAgent: 'anthropic-ai',  // 禁止Anthropic的爬虫
        disallow: '/',
      },
      {
        userAgent: 'Claude-Web',    // 禁止Claude的爬虫
        disallow: '/',
      }
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  }
}
