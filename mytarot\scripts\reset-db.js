const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

async function resetDatabase() {
  console.log('🔄 开始重置数据库...');
  
  // 删除现有数据库文件
  const dbPath = path.join(__dirname, '../prisma/dev.db');
  if (fs.existsSync(dbPath)) {
    fs.unlinkSync(dbPath);
    console.log('✅ 删除旧数据库文件');
  }
  
  // 创建新的Prisma客户端
  const prisma = new PrismaClient();
  
  try {
    // 这会自动创建数据库和表
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 验证表结构
    const result = await prisma.$queryRaw`PRAGMA table_info(User);`;
    console.log('📋 User表结构:', result);
    
    // 创建一个测试用户来验证默认值
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User'
      }
    });
    
    console.log('✅ 测试用户创建成功:', {
      id: testUser.id,
      email: testUser.email,
      freeReadingsUsed: testUser.freeReadingsUsed,
      freeReadingsLimit: testUser.freeReadingsLimit
    });
    
    // 删除测试用户
    await prisma.user.delete({
      where: { id: testUser.id }
    });
    
    console.log('🎉 数据库重置完成！');
    console.log('📊 免费次数设置: 默认3次，已使用0次');
    
  } catch (error) {
    console.error('❌ 数据库重置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetDatabase();
