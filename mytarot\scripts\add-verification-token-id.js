const { PrismaClient } = require('@prisma/client');

// 设置生产环境变量
process.env.DATABASE_URL = "prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19XNmY3Q1YxZ1VxZ2FEQm5LNDBEbmkiLCJhcGlfa2V5IjoiMDFLMUpEOFE1MDdOWjNETUVBRFZZUTFXNFYiLCJ0ZW5hbnRfaWQiOiI2NjAwMGJlMGVmMDFlYmQyZDNjMWY1NTVkMGFkODJhZjc3M2EyMzlkMmNkMzIzNWIzMjg3ODYzMDY0NmE3MDBiIiwiaW50ZXJuYWxfc2VjcmV0IjoiMTVlZDcwMmEtN2M2Mi00M2FmLTlmNjYtZmE3ODY2NmFmOGIwIn0.U5cXSRaBwwcO7W2JI1j5mSVa5etgOF1hD95sPh_LwFI";

process.env.DIRECT_URL = "postgres://66000be0ef01ebd2d3c1f555d0ad82af773a239d2cd3235b32878630646a700b:<EMAIL>:5432/?sslmode=require";

async function fixVerificationTokenReplicaIdentity() {
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DIRECT_URL
      }
    }
  });

  try {
    console.log('🔍 检查 VerificationToken 表...');

    // 检查表是否存在
    const tableExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'VerificationToken'
      );
    `;

    if (!tableExists || !(tableExists as any[])[0]?.exists) {
      console.log('❌ VerificationToken 表不存在');
      return;
    }

    console.log('✅ VerificationToken 表存在');

    // 检查当前的 replica identity 设置
    const replicaIdentity = await prisma.$queryRaw`
      SELECT relreplident
      FROM pg_class
      WHERE relname = 'VerificationToken';
    `;

    console.log('📋 当前 replica identity 设置:', replicaIdentity);

    // 设置 replica identity 为 FULL
    // 这允许删除操作而不需要主键
    console.log('🔧 设置 VerificationToken 表的 replica identity...');

    await prisma.$executeRaw`
      ALTER TABLE "VerificationToken" REPLICA IDENTITY FULL;
    `;

    console.log('✅ VerificationToken 表 replica identity 设置成功！');
    console.log('🎉 现在可以正常删除 VerificationToken 记录了');

    // 验证设置
    const newReplicaIdentity = await prisma.$queryRaw`
      SELECT relreplident
      FROM pg_class
      WHERE relname = 'VerificationToken';
    `;

    console.log('📋 新的 replica identity 设置:', newReplicaIdentity);

  } catch (error) {
    console.error('❌ 设置 replica identity 失败:', error);
    console.error('详细错误:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

fixVerificationTokenReplicaIdentity();
