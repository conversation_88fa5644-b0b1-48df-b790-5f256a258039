'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import TarotCard from './TarotCard';
import { TarotCard as TarotCardType } from '@/types/tarot';

interface LazyCardGridProps {
  cards: TarotCardType[];
  positions: Array<{ x: number; y: number; rotation: number }>;
  selectedCards: TarotCardType[];
  onCardSelect: (card: TarotCardType) => void;
  maxSelections: number;
}

export default function LazyCardGrid({
  cards,
  positions,
  selectedCards,
  onCardSelect,
  maxSelections
}: LazyCardGridProps) {
  const [visibleCards, setVisibleCards] = useState<Set<number>>(new Set());
  const containerRef = useRef<HTMLDivElement>(null);

  // 使用Intersection Observer来实现懒加载
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cardIndex = parseInt(entry.target.getAttribute('data-card-index') || '0');
            setVisibleCards(prev => new Set([...prev, cardIndex]));
          }
        });
      },
      {
        root: containerRef.current,
        rootMargin: '50px',
        threshold: 0.1
      }
    );

    // 观察所有卡牌占位符
    const cardElements = containerRef.current?.querySelectorAll('[data-card-index]');
    cardElements?.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, [cards]);

  const isCardSelected = (card: TarotCardType) => {
    return selectedCards.some(selected => selected.id === card.id);
  };

  const isCardDisabled = (card: TarotCardType) => {
    return selectedCards.length >= maxSelections && !isCardSelected(card);
  };

  return (
    <div ref={containerRef} className="absolute inset-0 flex items-center justify-center">
      <div className="relative w-full max-w-6xl h-full">
        <AnimatePresence>
          {cards.map((card, index) => {
            const position = positions[index];
            if (!position) return null;

            const isSelected = isCardSelected(card);
            const isDisabled = isCardDisabled(card);
            const isVisible = visibleCards.has(index);

            return (
              <motion.div
                key={card.id}
                data-card-index={index}
                className="absolute cursor-pointer"
                style={{
                  left: `${50 + position.x}%`,
                  top: `${50 + position.y}%`,
                  transform: `translate(-50%, -50%) rotate(${position.rotation}deg)`,
                  zIndex: isSelected ? 15 : 10,
                }}
                initial={{ 
                  scale: 0, 
                  opacity: 0,
                  rotate: position.rotation + 180
                }}
                animate={{ 
                  scale: isSelected ? 1.1 : (isDisabled ? 0.7 : 1),
                  opacity: isDisabled ? 0.3 : 1,
                  rotate: position.rotation
                }}
                transition={{ 
                  duration: 0.8,
                  delay: index * 0.02,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={!isDisabled ? { 
                  scale: isSelected ? 1.1 : 1.05,
                  rotate: 0,
                  zIndex: 12
                } : {}}
                onClick={() => !isDisabled && onCardSelect(card)}
              >
                {isVisible ? (
                  <TarotCard
                    card={card}
                    isRevealed={false}
                    isSelected={isSelected}
                    size="medium"
                    showBack={true}
                  />
                ) : (
                  // 占位符，用于触发Intersection Observer
                  <div className="w-16 h-24 bg-purple-900/30 rounded-lg animate-pulse" />
                )}
                
                {isSelected && (
                  <motion.div
                    className="absolute -top-2 -right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs font-bold"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    {selectedCards.findIndex(selected => selected.id === card.id) + 1}
                  </motion.div>
                )}
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
    </div>
  );
}
