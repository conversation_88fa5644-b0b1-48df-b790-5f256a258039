'use client';

import { useState, useRef, useEffect } from 'react';
import { Globe } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { useLocale } from 'next-intl';

export default function LanguageToggle() {
  const [isOpen, setIsOpen] = useState(false);
  const [isChanging, setIsChanging] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();

  const handleLanguageChange = async (newLocale: string) => {
    if (newLocale === locale) {
      setIsOpen(false);
      return;
    }

    // 设置加载状态
    setIsChanging(true);

    // 获取当前路径，移除语言前缀
    const pathWithoutLocale = pathname.replace(`/${locale}`, '') || '/';

    // 构建新的路径
    const newPath = `/${newLocale}${pathWithoutLocale}`;

    // 关闭下拉菜单
    setIsOpen(false);

    // 短暂延迟以显示加载状态
    await new Promise(resolve => setTimeout(resolve, 300));

    // 导航到新的语言路径
    router.push(newPath);
  };

  const getCurrentLanguageDisplay = () => {
    return locale === 'zh' ? '中文' : 'EN';
  };

  // 点击外部关闭下拉菜单和键盘导航
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => !isChanging && setIsOpen(!isOpen)}
        disabled={isChanging}
        className="flex items-center gap-2 px-3 py-2 text-white hover:text-purple-200 transition-all duration-200
                   min-h-[44px] min-w-[44px] justify-center md:justify-start
                   rounded-lg hover:bg-white/10 active:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="切换语言 / Switch Language"
      >
        {isChanging ? (
          <>
            {/* 加载动画 */}
            <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="text-sm md:text-base font-medium">
              {locale === 'zh' ? '切换中...' : 'Switching...'}
            </span>
          </>
        ) : (
          <>
            <Globe size={20} className="flex-shrink-0" />
            <span className="uppercase text-sm md:text-base font-medium">{getCurrentLanguageDisplay()}</span>
            {/* 下拉箭头指示器 */}
            <svg
              className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </>
        )}
      </button>

      {/* 语言选择菜单 - 优化的下拉动画和布局 */}
      {isOpen && (
        <>
          {/* 移动端背景遮罩 */}
          <div className="fixed inset-0 bg-black/20 z-40 md:hidden" onClick={() => setIsOpen(false)} />

          {/* 下拉菜单 */}
          <div className={`absolute right-0 top-full mt-2 bg-white rounded-xl shadow-2xl z-50
                          border border-gray-100 overflow-hidden
                          transform origin-top-right transition-all duration-200
                          ${isOpen ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}
                          min-w-[160px] md:min-w-[180px]`}>

            {/* 菜单标题 */}
            <div className="px-4 py-3 bg-gradient-to-r from-purple-50 to-blue-50 border-b border-gray-100">
              <p className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <Globe size={16} />
                {locale === 'zh' ? '选择语言' : 'Select Language'}
              </p>
            </div>

            {/* 语言选项 */}
            <div className="py-2">
              <button
                onClick={() => handleLanguageChange('en')}
                disabled={isChanging}
                className={`block w-full px-4 py-3 text-left hover:bg-purple-50 active:bg-purple-100
                           transition-all duration-150 text-base font-medium group disabled:opacity-50 disabled:cursor-not-allowed
                           ${locale === 'en' ? 'text-purple-600 bg-purple-50 border-r-4 border-purple-500' : 'text-gray-700'}`}
              >
                <div className="flex items-center gap-3">
                  <span className="text-xl">🇺🇸</span>
                  <div className="flex-1">
                    <div className="font-medium">English</div>
                    <div className="text-xs text-gray-500">English</div>
                  </div>
                  {locale === 'en' ? (
                    <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : isChanging ? (
                    <svg className="w-4 h-4 animate-spin text-gray-400" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : null}
                </div>
              </button>

              <button
                onClick={() => handleLanguageChange('zh')}
                disabled={isChanging}
                className={`block w-full px-4 py-3 text-left hover:bg-purple-50 active:bg-purple-100
                           transition-all duration-150 text-base font-medium group disabled:opacity-50 disabled:cursor-not-allowed
                           ${locale === 'zh' ? 'text-purple-600 bg-purple-50 border-r-4 border-purple-500' : 'text-gray-700'}`}
              >
                <div className="flex items-center gap-3">
                  <span className="text-xl">🇨🇳</span>
                  <div className="flex-1">
                    <div className="font-medium">中文</div>
                    <div className="text-xs text-gray-500">简体中文</div>
                  </div>
                  {locale === 'zh' ? (
                    <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : isChanging ? (
                    <svg className="w-4 h-4 animate-spin text-gray-400" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : null}
                </div>
              </button>
            </div>

            {/* 底部提示 */}
            <div className="px-4 py-2 bg-gray-50 border-t border-gray-100">
              <p className="text-xs text-gray-500 text-center">
                {locale === 'zh' ? '点击选择语言' : 'Click to select language'}
              </p>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
