import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { TarotCard } from '@/types/tarot';

// Vercel 默认使用 Node.js Runtime



// 根据IP地理位置获取OpenAI基础URL
function getOpenAIBaseURL(request: NextRequest): string {
  // 从环境变量获取默认配置

  // 获取客户端IP地址
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const clientIP = forwarded?.split(',')[0] || realIP || request.ip || '';
  console.log('🌍 地理位置检测:', {
    clientIP,
    headers: {
      'x-forwarded-for': forwarded,
      'x-real-ip': realIP,
      'x-vercel-ip-country': request.headers.get('x-vercel-ip-country')
    }
  });
  // 获取地理位置信息（Vercel提供的地理位置头）
  const country = request.headers.get('x-vercel-ip-country') ||
                  request.geo?.country || '';

  console.log('🌍 地理位置检测:', {
    clientIP,
    country,
    headers: {
      'x-forwarded-for': forwarded,
      'x-real-ip': realIP,
      'x-vercel-ip-country': request.headers.get('x-vercel-ip-country')
    }
  });

  // 如果是中国用户，使用国内镜像
  if (country === 'CN') {
    console.log('🇨🇳 检测到中国用户，使用国内API镜像');
    return process.env.OPENAI_BASE_URL_CN as string;
  } else {
    console.log('🌐 检测到海外用户，使用国际API镜像');
    return process.env.OPENAI_BASE_URL_OTHER as string;
  }
}

export async function POST(request: NextRequest) {
  try {
    // 先解析请求体获取locale
    const { cards, question, locale = 'en', stream = false } = await request.json();


    // 根据用户地理位置动态创建OpenAI客户端
    const baseURL = getOpenAIBaseURL(request);
    console.log('OpenAI Base URL:', baseURL);
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: baseURL,
    });

    // 获取模型配置，优先使用环境变量
    const model = process.env.OPENAI_MODEL || 'gpt-4o';
    console.log('使用模型:', model);

    // 构建提示词
    const prompt = buildTarotPrompt(cards, question, locale);
    const systemPrompt = getSystemPrompt(locale);

    if (stream) {
      // 流式响应
      const stream = await openai.chat.completions.create({
        model: model,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        stream: true,
        temperature: 0.7,
        max_tokens: 1500,
      });

      // 创建流式响应
      const encoder = new TextEncoder();
      const readable = new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of stream) {
              const content = chunk.choices[0]?.delta?.content || '';
              if (content) {
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({ content })}\n\n`));
              }
            }
            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            controller.close();
          } catch (error) {
            controller.error(error);
          }
        },
      });

      return new Response(readable, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    } else {
      // 非流式响应
      const completion = await openai.chat.completions.create({
        model: model,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1500,
      });

      const reading = completion.choices[0]?.message?.content || '';

      return NextResponse.json({
        reading,
        cards: cards.map((card: any, index: number) => ({
          ...card,
          position: ['Past', 'Present', 'Future'][index],
        })),
        question,
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    console.error('Error generating tarot reading:', error);
    
    if (error instanceof Error && error.message.includes('API key')) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to generate reading' },
      { status: 500 }
    );
  }
}

function getSystemPrompt(locale: string): string {
  if (locale === 'zh') {
    return '你是一位智慧而直觉敏锐的塔罗牌解读师，对塔罗牌的象征意义和含义有着深刻的理解。请提供富有洞察力、深思熟虑的解读，帮助人们获得清晰的认知和视角。请用中文回答。';
  } else {
    return 'You are a wise and intuitive tarot reader with deep knowledge of tarot symbolism and meanings. Provide insightful, thoughtful readings that help people gain clarity and perspective. Please respond in English.';
  }
}

function buildTarotPrompt(cards: any[], question: string, locale: string): string {
  if (locale === 'zh') {
    const cardDescriptions = cards.map((card: any, index: number) => {
      const position = ['过去', '现在', '未来'][index];
      const orientation = card.isReversed ? '逆位' : '正位';
      const meaning = card.isReversed ? card.reversedMeaning : card.meaning;

      return `${position}：${card.name}（${orientation}）
    关键词：${card.keywords.join('、')}
    含义：${meaning}`;
    }).join('\n\n');

    return `请为以下问题和卡牌提供全面的塔罗牌解读：

问题："${question}"

抽到的卡牌：
${cardDescriptions}

请提供：
1. 对三张牌整体布局的解读
2. 卡牌之间的联系和故事线
3. 基于解读的实用指导和建议

请用温暖、富有洞察力和鼓励性的语调书写,总体解读内容应约为80-100字。`;
  } else {
    const cardDescriptions = cards.map((card: any, index: number) => {
      const position = ['Past', 'Present', 'Future'][index];
      const orientation = card.isReversed ? 'Reversed' : 'Upright';
      const meaning = card.isReversed ? card.reversedMeaningEn : card.meaningEn;

      return `${position}: ${card.nameEn} (${orientation})
    Keywords: ${card.keywordsEn.join(', ')}
    Meaning: ${meaning}`;
    }).join('\n\n');

    return `Please provide a comprehensive tarot reading for the following question and cards:

Question: "${question}"

Cards drawn:
${cardDescriptions}

Please provide:
1. An overall interpretation of the three-card spread
2. The connections and story between the cards
3. Practical guidance and advice based on the reading


Write in a warm, insightful, and encouraging tone. The reading should be approximately 80-100 words.`;
  }
}
