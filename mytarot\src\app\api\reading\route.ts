import { NextRequest, NextResponse } from 'next/server';
import { TarotCard } from '@/types/tarot';

// Vercel 默认使用 Node.js Runtime

// 新的AI API配置
const AI_API_URL = 'https://api.pearktrue.cn/api/aichat/';
const AI_MODEL = 'deepseek-v3';

// 根据IP地理位置检测语言
function detectLanguageFromIP(request: NextRequest): string {
  // 获取地理位置信息（Vercel提供的地理位置头）
  const country = request.headers.get('x-vercel-ip-country') || '';

  // 获取客户端IP地址用于日志
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const clientIP = forwarded?.split(',')[0] || realIP || '';

  console.log('🌍 地理位置检测:', {
    clientIP,
    country,
    headers: {
      'x-forwarded-for': forwarded,
      'x-real-ip': realIP,
      'x-vercel-ip-country': request.headers.get('x-vercel-ip-country')
    }
  });

  // 如果是中国用户，使用中文
  if (country === 'CN') {
    console.log('🇨🇳 检测到中国用户，使用中文');
    return 'zh';
  } else {
    console.log('🌐 检测到海外用户，使用英文');
    return 'en';
  }
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const { cards, question, stream = true } = await request.json();

    // 根据IP地址自动检测语言
    const locale = detectLanguageFromIP(request);
    console.log('自动检测语言:', locale);

    // 构建提示词
    const prompt = buildTarotPrompt(cards, question, locale);
    const systemPrompt = getSystemPrompt(locale);

    console.log('使用模型:', AI_MODEL);

    if (stream) {
      // 调用新的AI API进行流式响应
      const response = await fetch(AI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: AI_MODEL,
          messages: [
            {
              role: 'user',
              content: `${systemPrompt}\n\n${prompt}`
            }
          ],
          stream: true
        }),
      });

      if (!response.ok) {
        throw new Error(`AI API请求失败: ${response.status}`);
      }

      // 创建流式响应
      const encoder = new TextEncoder();
      const readable = new ReadableStream({
        async start(controller) {
          try {
            const reader = response.body?.getReader();
            if (!reader) {
              throw new Error('无法获取响应流');
            }

            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              buffer += decoder.decode(value, { stream: true });
              const lines = buffer.split('\n');
              buffer = lines.pop() || '';

              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  const data = line.slice(6);

                  if (data === '[DONE]') {
                    controller.enqueue(encoder.encode('data: [DONE]\n\n'));
                    controller.close();
                    return;
                  }

                  try {
                    const parsed = JSON.parse(data);
                    const content = parsed.choices[0]?.delta?.content || '';
                    if (content) {
                      controller.enqueue(encoder.encode(`data: ${JSON.stringify({ content })}\n\n`));
                    }
                  } catch (e) {
                    // 忽略解析错误
                  }
                }
              }
            }

            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            controller.close();
          } catch (error) {
            controller.error(error);
          }
        },
      });

      return new Response(readable, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    } else {
      // 非流式响应（保留以防需要）
      const response = await fetch(AI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: AI_MODEL,
          messages: [
            {
              role: 'user',
              content: `${systemPrompt}\n\n${prompt}`
            }
          ],
          stream: false
        }),
      });

      if (!response.ok) {
        throw new Error(`AI API请求失败: ${response.status}`);
      }

      const data = await response.json();
      const reading = data.choices[0]?.message?.content || '';

      return NextResponse.json({
        reading,
        cards: cards.map((card: any, index: number) => ({
          ...card,
          position: ['Past', 'Present', 'Future'][index],
        })),
        question,
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    console.error('Error generating tarot reading:', error);
    
    if (error instanceof Error && error.message.includes('API key')) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to generate reading' },
      { status: 500 }
    );
  }
}

function getSystemPrompt(locale: string): string {
  if (locale === 'zh') {
    return '你是一位智慧而直觉敏锐的塔罗牌解读师，对塔罗牌的象征意义和含义有着深刻的理解。请提供富有洞察力、深思熟虑的解读，帮助人们获得清晰的认知和视角。请用中文回答。';
  } else {
    return 'You are a wise and intuitive tarot reader with deep knowledge of tarot symbolism and meanings. Provide insightful, thoughtful readings that help people gain clarity and perspective. Please respond in English.';
  }
}

function buildTarotPrompt(cards: any[], question: string, locale: string): string {
  if (locale === 'zh') {
    const cardDescriptions = cards.map((card: any, index: number) => {
      const position = ['过去', '现在', '未来'][index];
      const orientation = card.isReversed ? '逆位' : '正位';
      const meaning = card.isReversed ? card.reversedMeaning : card.meaning;

      return `${position}：${card.name}（${orientation}）
    关键词：${card.keywords.join('、')}
    含义：${meaning}`;
    }).join('\n\n');

    return `请为以下问题和卡牌提供全面的塔罗牌解读：

问题："${question}"

抽到的卡牌：
${cardDescriptions}

请提供：
1. 对三张牌整体布局的解读
2. 卡牌之间的联系和故事线
3. 基于解读的实用指导和建议

请用温暖、富有洞察力和鼓励性的语调书写,总体解读内容应约为80-100字。`;
  } else {
    const cardDescriptions = cards.map((card: any, index: number) => {
      const position = ['Past', 'Present', 'Future'][index];
      const orientation = card.isReversed ? 'Reversed' : 'Upright';
      const meaning = card.isReversed ? card.reversedMeaningEn : card.meaningEn;

      return `${position}: ${card.nameEn} (${orientation})
    Keywords: ${card.keywordsEn.join(', ')}
    Meaning: ${meaning}`;
    }).join('\n\n');

    return `Please provide a comprehensive tarot reading for the following question and cards:

Question: "${question}"

Cards drawn:
${cardDescriptions}

Please provide:
1. An overall interpretation of the three-card spread
2. The connections and story between the cards
3. Practical guidance and advice based on the reading


Write in a warm, insightful, and encouraging tone. The reading should be approximately 80-100 words.`;
  }
}
