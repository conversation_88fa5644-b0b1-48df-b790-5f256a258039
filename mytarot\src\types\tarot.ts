// 塔罗牌类型定义

export interface TarotCard {
  id: string;
  name: string;
  nameEn: string;
  type: 'major' | 'minor';
  suit?: 'cups' | 'pentacles' | 'swords' | 'wands';
  number?: number;
  imagePath: string;
  keywords: string[];
  keywordsEn: string[];
  meaning: string;
  meaningEn: string;
  reversedMeaning: string;
  reversedMeaningEn: string;
}

export interface SelectedCard {
  card: TarotCard;
  position: 'past' | 'present' | 'future';
  isReversed: boolean;
}

export interface TarotReading {
  cards: SelectedCard[];
  question: string;
  interpretation?: string;
  timestamp: Date;
}

export interface CardPosition {
  x: number;
  y: number;
  rotation: number;
  zIndex: number;
}

export type Language = 'en' | 'zh';

export interface User {
  id: string;
  name?: string;
  email?: string;
  image?: string;
  birthday?: Date;
  readings: TarotReading[];
}
