"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/hooks/useIPLanguage.ts":
/*!************************************!*\
  !*** ./src/hooks/useIPLanguage.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIPLanguage: () => (/* binding */ useIPLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useIPLanguage auto */ \nfunction useIPLanguage() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('en');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [ipInfo, setIpInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useIPLanguage.useEffect\": ()=>{\n            const detectLanguageFromIP = {\n                \"useIPLanguage.useEffect.detectLanguageFromIP\": async ()=>{\n                    try {\n                        // 优先使用我们自己的API（避免跨域问题）\n                        try {\n                            console.log('使用后端API检测地理位置...');\n                            const response = await fetch('/api/detect-location', {\n                                signal: AbortSignal.timeout(8000),\n                                headers: {\n                                    'Accept': 'application/json'\n                                }\n                            });\n                            if (response.ok) {\n                                const result = await response.json();\n                                console.log('后端API响应:', result);\n                                if (result.success && result.data) {\n                                    const { country, countryCode, ip } = result.data;\n                                    const info = {\n                                        country: country,\n                                        countryCode: countryCode,\n                                        ip: ip\n                                    };\n                                    setIpInfo(info);\n                                    // 判断是否为中国\n                                    const isChinese = country === '中国' || country === 'China' || countryCode === 'CN';\n                                    if (isChinese) {\n                                        setLanguage('zh');\n                                        console.log('🇨🇳 检测到中国IP，切换到中文');\n                                    } else {\n                                        setLanguage('en');\n                                        console.log('🌐 检测到海外IP，使用英文');\n                                    }\n                                    setIsLoading(false);\n                                    return; // 成功检测，直接返回\n                                }\n                            }\n                        } catch (error) {\n                            console.warn('后端API检测失败:', error);\n                        }\n                        // 后端API失败，尝试前端直接调用（可能有跨域问题）\n                        console.log('尝试前端直接IP检测...');\n                        const ipServices = [\n                            {\n                                url: 'https://ipapi.co/json/',\n                                parser: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>({\n                                            ip: data.ip,\n                                            country: data.country_name,\n                                            countryCode: data.country_code\n                                        })\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            },\n                            {\n                                url: 'https://ipinfo.io/json',\n                                parser: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>({\n                                            ip: data.ip,\n                                            country: data.country === 'CN' ? '中国' : data.country,\n                                            countryCode: data.country\n                                        })\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            }\n                        ];\n                        let detected = false;\n                        for (const service of ipServices){\n                            try {\n                                console.log(\"尝试IP检测服务: \".concat(service.url));\n                                const response = await fetch(service.url, {\n                                    signal: AbortSignal.timeout(5000),\n                                    headers: {\n                                        'Accept': 'application/json'\n                                    }\n                                });\n                                if (!response.ok) {\n                                    console.warn(\"服务 \".concat(service.url, \" 返回错误: \").concat(response.status));\n                                    continue;\n                                }\n                                const data = await response.json();\n                                console.log(\"\".concat(service.url, \" 响应:\"), data);\n                                const parsed = service.parser(data);\n                                if (parsed.ip && parsed.country) {\n                                    const info = {\n                                        country: parsed.country,\n                                        countryCode: parsed.countryCode || '',\n                                        ip: parsed.ip\n                                    };\n                                    setIpInfo(info);\n                                    // 判断是否为中国\n                                    const isChinese = parsed.country === '中国' || parsed.country === 'China' || parsed.countryCode === 'CN';\n                                    if (isChinese) {\n                                        setLanguage('zh');\n                                        console.log('🇨🇳 检测到中国IP，切换到中文');\n                                    } else {\n                                        setLanguage('en');\n                                        console.log('🌐 检测到海外IP，使用英文');\n                                    }\n                                    detected = true;\n                                    break;\n                                }\n                            } catch (error) {\n                                console.warn(\"IP检测服务 \".concat(service.url, \" 失败:\"), error);\n                                continue;\n                            }\n                        }\n                        // 如果所有IP检测服务都失败，使用浏览器语言\n                        if (!detected) {\n                            var _navigator_languages;\n                            console.log('所有IP检测服务都失败，使用浏览器语言检测');\n                            const browserLang = navigator.language || ((_navigator_languages = navigator.languages) === null || _navigator_languages === void 0 ? void 0 : _navigator_languages[0]) || 'en';\n                            if (browserLang.startsWith('zh')) {\n                                setLanguage('zh');\n                                console.log('🌐 根据浏览器语言使用中文');\n                            } else {\n                                setLanguage('en');\n                                console.log('🌐 根据浏览器语言使用英文');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('语言检测失败:', error);\n                        // 默认使用英文\n                        setLanguage('en');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useIPLanguage.useEffect.detectLanguageFromIP\"];\n            detectLanguageFromIP();\n        }\n    }[\"useIPLanguage.useEffect\"], []);\n    return {\n        language,\n        isLoading,\n        ipInfo,\n        setLanguage\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useIPLanguage.ts\n"));

/***/ })

});