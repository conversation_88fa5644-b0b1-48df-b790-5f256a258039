"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/components/CardResults.tsx":
/*!****************************************!*\
  !*** ./src/components/CardResults.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CardResults)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,RotateCcw,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,RotateCcw,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,RotateCcw,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,RotateCcw,Send,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _TarotCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TarotCard */ \"(app-pages-browser)/./src/components/TarotCard.tsx\");\n/* harmony import */ var _hooks_useTarotReading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useTarotReading */ \"(app-pages-browser)/./src/hooks/useTarotReading.ts\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CardResults(param) {\n    let { selectedCards, onNewReading } = param;\n    _s();\n    const [question, setQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isRevealed, setIsRevealed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cardOrientations, setCardOrientations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentRevealIndex, setCurrentRevealIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [showAIReading, setShowAIReading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { language, t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const { reading, isLoading, error, streamReading } = (0,_hooks_useTarotReading__WEBPACK_IMPORTED_MODULE_3__.useTarotReading)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CardResults.useEffect\": ()=>{\n            // 使用卡牌ID的哈希值来确定正逆位，避免hydration错误\n            const orientations = selectedCards.map({\n                \"CardResults.useEffect.orientations\": (card, index)=>{\n                    const hash = card.id.split('').reduce({\n                        \"CardResults.useEffect.orientations.hash\": (acc, char)=>acc + char.charCodeAt(0)\n                    }[\"CardResults.useEffect.orientations.hash\"], 0);\n                    return (hash + index) % 2 === 0;\n                }\n            }[\"CardResults.useEffect.orientations\"]);\n            setCardOrientations(orientations);\n        }\n    }[\"CardResults.useEffect\"], [\n        selectedCards\n    ]);\n    const handleGetAIReading = async ()=>{\n        if (!question.trim()) {\n            alert(t('question-required', '请先输入您的问题', 'Please enter your question first'));\n            return;\n        }\n        // 准备卡牌数据，包含正逆位信息\n        const cardsWithOrientation = selectedCards.map((card, index)=>({\n                ...card,\n                isReversed: cardOrientations[index]\n            }));\n        setShowAIReading(true);\n        // 不再传递locale参数，由服务器端自动检测\n        await streamReading(cardsWithOrientation, question);\n    };\n    const handleRevealCards = ()=>{\n        if (!question.trim()) {\n            alert(locale === 'zh' ? '请先输入您的问题' : 'Please enter your question first');\n            return;\n        }\n        setIsRevealed(true);\n        // 逐张翻牌动画\n        selectedCards.forEach((_, index)=>{\n            setTimeout(()=>{\n                setCurrentRevealIndex(index);\n            }, index * 800);\n        });\n    };\n    const cardPositions = [\n        {\n            title: locale === 'zh' ? '过去' : 'Past',\n            subtitle: locale === 'zh' ? '来自过去的影响' : 'What influences you from the past'\n        },\n        {\n            title: locale === 'zh' ? '现在' : 'Present',\n            subtitle: locale === 'zh' ? '您当前的状况' : 'Your current situation'\n        },\n        {\n            title: locale === 'zh' ? '未来' : 'Future',\n            subtitle: locale === 'zh' ? '未来的展望' : 'What the future holds'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"max-w-6xl mx-auto\",\n                initial: {\n                    opacity: 0,\n                    y: 50\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold text-white mb-4\",\n                                children: locale === 'zh' ? '您的塔罗占卜' : 'Your Tarot Reading'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-purple-200\",\n                                children: locale === 'zh' ? '三张牌为您指引道路' : 'Three cards to guide your path'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    !isRevealed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-white mb-4 text-center\",\n                                children: t('questionPrompt')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: question,\n                                        onChange: (e)=>setQuestion(e.target.value),\n                                        placeholder: t('questionPlaceholder'),\n                                        className: \"flex-1 px-6 py-4 bg-white/20 border border-purple-300/30 rounded-full text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent\",\n                                        onKeyDown: (e)=>e.key === 'Enter' && handleRevealCards()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: handleRevealCards,\n                                        className: \"px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center gap-2\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('reveal')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\",\n                        children: selectedCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 100\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.5 + index * 0.2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-white mb-2\",\n                                                children: cardPositions[index].title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-200 text-sm\",\n                                                children: cardPositions[index].subtitle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"relative\",\n                                            animate: {\n                                                scale: currentRevealIndex >= index ? 1.1 : 1\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TarotCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                card: card,\n                                                isRevealed: isRevealed && currentRevealIndex >= index,\n                                                isReversed: cardOrientations[index],\n                                                size: \"large\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this),\n                                    isRevealed && currentRevealIndex >= index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.5\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white mb-2\",\n                                                children: [\n                                                    locale === 'zh' ? card.name : card.nameEn,\n                                                    cardOrientations[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-300 text-sm ml-2\",\n                                                        children: [\n                                                            \"(\",\n                                                            t('reversed'),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-200 text-sm mb-3\",\n                                                children: locale === 'zh' ? card.keywords.join(', ') : card.keywordsEn.join(', ')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-sm leading-relaxed\",\n                                                children: cardOrientations[index] ? locale === 'zh' ? card.reversedMeaning : card.reversedMeaningEn : locale === 'zh' ? card.meaning : card.meaningEn\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, card.id, true, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    isRevealed && question && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8 text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 2.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this),\n                                    t('yourQuestion')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-purple-200 text-lg italic\",\n                                children: [\n                                    '\"',\n                                    question,\n                                    '\"'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 13\n                    }, this),\n                    isRevealed && currentRevealIndex >= 2 && !showAIReading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"text-center mb-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                            onClick: handleGetAIReading,\n                            disabled: isLoading,\n                            className: \"px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-full hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center gap-2 mx-auto disabled:opacity-50 disabled:cursor-not-allowed\",\n                            whileHover: {\n                                scale: isLoading ? 1 : 1.05\n                            },\n                            whileTap: {\n                                scale: isLoading ? 1 : 0.95\n                            },\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 20,\n                                        className: \"animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 21\n                                    }, this),\n                                    locale === 'zh' ? '分析中...' : 'Analyzing...'\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 21\n                                    }, this),\n                                    locale === 'zh' ? '开始分析' : 'Start Analysis'\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 13\n                    }, this),\n                    showAIReading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-semibold text-white mb-6 text-center flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 28\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this),\n                                    t('aiTarotReading')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, this),\n                            error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg p-4 mb-4 bg-red-500/20 border-red-500/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-200\",\n                                    children: [\n                                        locale === 'zh' ? '错误：' : 'Error:',\n                                        \" \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-invert max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-purple-100 leading-relaxed whitespace-pre-wrap\",\n                                        children: reading || isLoading && (locale === 'zh' ? '正在咨询宇宙智慧...' : 'Consulting the cosmic wisdom...')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 19\n                                    }, this),\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 24,\n                                            className: \"animate-spin text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                            onClick: onNewReading,\n                            className: \"px-6 py-3 bg-white/20 text-white font-semibold rounded-full hover:bg-white/30 transition-all duration-300 flex items-center gap-2 mx-auto\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_RotateCcw_Send_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, this),\n                                locale === 'zh' ? '新的占卜' : 'New Reading'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardResults.tsx\",\n        lineNumber: 88,\n        columnNumber: 7\n    }, this);\n}\n_s(CardResults, \"lHb+FgoOrk1psV4SsPdVmQkKC1A=\", false, function() {\n    return [\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage,\n        _hooks_useTarotReading__WEBPACK_IMPORTED_MODULE_3__.useTarotReading\n    ];\n});\n_c = CardResults;\nvar _c;\n$RefreshReg$(_c, \"CardResults\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CardResults.tsx\n"));

/***/ })

});