import createMiddleware from 'next-intl/middleware';
import { locales } from './i18n';

export default createMiddleware({
  // 支持的语言列表
  locales,

  // 默认语言
  defaultLocale: 'en',

  // 禁用自动语言检测，始终使用默认语言
  localeDetection: false,

  // 路径名配置
  pathnames: {
    '/': '/',
    '/reading': '/reading',
    '/history': '/history',
    '/profile': '/profile'
  }
});

export const config = {
  // 匹配所有路径，除了API路由、静态文件等
  matcher: [
    // 匹配所有路径除了以下开头的：
    // - api (API routes)
    // - _next/static (static files)
    // - _next/image (image optimization files)
    // - favicon.ico (favicon file)
    // - images (public images)
    // - sitemap.xml (sitemap file)
    // - robots.txt (robots file)
    '/((?!api|_next/static|_next/image|favicon.ico|images|sitemap.xml|robots.txt).*)'
  ]
};
