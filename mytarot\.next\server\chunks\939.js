exports.id=939,exports.ids=[939],exports.modules={6542:(a,b,c)=>{Promise.resolve().then(c.bind(c,80994)),Promise.resolve().then(c.bind(c,75116)),Promise.resolve().then(c.bind(c,68335))},11434:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>p,generateMetadata:()=>o,generateStaticParams:()=>q});var d=c(37413),e=c(5266),f=c.n(e),g=c(72948),h=c.n(g),i=c(88946),j=c(83930),k=c(39916),l=c(94163),m=c(68335),n=c(75116);async function o({params:a}){let{locale:b}=await a;return"zh"===b?{title:"神秘塔罗 - 免费AI塔罗牌占卜 | Mystic Tarot",description:"免费在线AI塔罗牌占卜，通过古老的塔罗牌智慧探索你的内心世界。专业的心理塔罗解读，无需注册即可使用。",keywords:"塔罗牌占卜,AI塔罗,免费塔罗,心理塔罗,在线占卜,神秘塔罗",openGraph:{title:"神秘塔罗 - 免费AI塔罗牌占卜",description:"免费在线AI塔罗牌占卜，探索你的内心世界",url:"https://tarotgo.top/zh",siteName:"Mystic Tarot",locale:"zh_CN",type:"website"},twitter:{card:"summary_large_image",title:"神秘塔罗 - 免费AI塔罗牌占卜",description:"免费在线AI塔罗牌占卜，探索你的内心世界"},alternates:{canonical:"https://tarotgo.top/zh",languages:{en:"https://tarotgo.top/en",zh:"https://tarotgo.top/zh"}}}:{title:"Mystic Tarot - Free AI Tarot Reading Online",description:"Free AI tarot reading online. Explore your inner world through ancient tarot wisdom. Professional psychic tarot reading app for browser, no registration required.",keywords:"Mystic Tarot,free ai tarot reading,ai tarot reading,psychic reading,psychic tarot reading,psychic tarot reading online,psychic tarot reading app for browser",openGraph:{title:"Mystic Tarot - Free AI Tarot Reading Online",description:"Free AI tarot reading online. Explore your inner world through ancient tarot wisdom.",url:"https://tarotgo.top/en",siteName:"Mystic Tarot",locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"Mystic Tarot - Free AI Tarot Reading Online",description:"Free AI tarot reading online. Explore your inner world through ancient tarot wisdom."},alternates:{canonical:"https://tarotgo.top/en",languages:{en:"https://tarotgo.top/en",zh:"https://tarotgo.top/zh"}}}}async function p({children:a,params:b}){let{locale:c}=await b;l.I.includes(c)||(0,k.notFound)();let e=await (0,j.A)({locale:c});return(0,d.jsx)("html",{lang:c,children:(0,d.jsxs)("body",{className:`${f().variable} ${h().variable} antialiased`,suppressHydrationWarning:!0,children:[(0,d.jsxs)(i.A,{messages:e,locale:c,children:[(0,d.jsx)(m.default,{type:"website"}),a]}),(0,d.jsx)(n.default,{})]})})}function q(){return[{locale:"en"},{locale:"zh"}]}c(61135)},20781:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},24078:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(60687),e=c(72600);function f(){let a=process.env.NEXT_PUBLIC_GA_ID||"G-LF2XCQJJ7Y";return a?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(e.default,{src:`https://www.googletagmanager.com/gtag/js?id=${a}`,strategy:"afterInteractive"}),(0,d.jsx)(e.default,{id:"google-analytics",strategy:"afterInteractive",children:`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${a}');
        `})]}):null}},34918:(a,b,c)=>{var d={"./en.json":[43789,789],"./zh.json":[1e3,0]};function e(a){if(!c.o(d,a))return Promise.resolve().then(()=>{var b=Error("Cannot find module '"+a+"'");throw b.code="MODULE_NOT_FOUND",b});var b=d[a],e=b[0];return c.e(b[1]).then(()=>c.t(e,19))}e.keys=()=>Object.keys(d),e.id=34918,a.exports=e},48785:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(71330),f=c(43210);function g({type:a="website",title:b,description:c,url:g}){let h=(0,e.Ym)(),[i,j]=(0,f.useState)(!1);if(!i)return null;let k="zh"===h,l="https://tarotgo.top",m=(()=>{let d={"@context":"https://schema.org","@type":"website"===a?"WebSite":"service"===a?"Service":"Article",name:b||(k?"神秘塔罗 - 免费AI塔罗牌占卜":"Mystic Tarot - Free AI Tarot Reading"),description:c||(k?"专业的免费在线AI塔罗牌占卜服务，通过古老的塔罗牌智慧探索你的内心世界。心理塔罗解读，无需注册即可使用。":"Professional free AI tarot reading online. Explore your inner world through ancient tarot wisdom. Psychic tarot reading with AI-powered insights, no registration required."),keywords:k?"塔罗牌占卜,AI塔罗,免费塔罗,心理塔罗,在线占卜,神秘塔罗":"Mystic Tarot,free ai tarot reading,ai tarot reading,psychic reading,psychic tarot reading,psychic tarot reading online,psychic tarot reading app for browser",url:g||`${l}/${h}`,inLanguage:"zh"===h?"zh-CN":"en-US",publisher:{"@type":"Organization",name:k?"神秘塔罗":"Mystic Tarot",url:l}};return"website"===a?{...d,potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:`${l}/${h}/reading?q={search_term_string}`},"query-input":"required name=search_term_string"},mainEntity:{"@type":"Service",name:k?"AI塔罗牌占卜服务":"AI Tarot Reading Service",description:k?"提供专业的免费在线AI塔罗牌占卜，帮助用户通过心理塔罗解读探索内心世界，获得人生指引":"Provides professional free online AI tarot readings to help users explore their inner world through psychic tarot interpretation and gain life guidance",provider:{"@type":"Organization",name:k?"神秘塔罗":"Mystic Tarot"},serviceType:k?"占卜服务":"Divination Service",areaServed:"Worldwide",hasOfferCatalog:{"@type":"OfferCatalog",name:k?"塔罗牌服务":"Tarot Services",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:k?"三张牌塔罗解读":"Three Card Tarot Reading",description:k?"使用过去、现在、未来三张牌进行塔罗解读":"Tarot reading using three cards representing past, present, and future"},price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock"}]}}}:"service"===a?{...d,"@type":"Service",serviceType:k?"塔罗牌占卜":"Tarot Reading",provider:{"@type":"Organization",name:k?"神秘塔罗":"Mystic Tarot",url:l},areaServed:"Worldwide",availableChannel:{"@type":"ServiceChannel",serviceUrl:`${l}/${h}/reading`,serviceSmsNumber:null,servicePhone:null,serviceLocation:null},category:k?"占卜服务":"Divination Service",offers:{"@type":"Offer",price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock",validFrom:new Date().toISOString()}}:d})();return(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(m,null,2)}})}},61135:()=>{},68335:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\StructuredData.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\project\\chuhai\\tarot_new\\mytarot\\src\\components\\StructuredData.tsx","default")},69590:(a,b,c)=>{Promise.resolve().then(c.bind(c,45196)),Promise.resolve().then(c.bind(c,24078)),Promise.resolve().then(c.bind(c,48785))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},75116:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\Analytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\project\\chuhai\\tarot_new\\mytarot\\src\\components\\Analytics.tsx","default")},78335:()=>{},84911:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(60687);function e(){return(0,d.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none z-0",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-radial from-indigo-900/20 via-purple-900/40 to-slate-900"}),(0,d.jsxs)("div",{className:"absolute inset-0",children:[(0,d.jsx)("div",{className:"absolute top-[10%] left-[15%] w-2 h-2 bg-white rounded-full animate-pulse opacity-80"}),(0,d.jsx)("div",{className:"absolute top-[20%] right-[20%] w-1.5 h-1.5 bg-blue-200 rounded-full animate-pulse opacity-70 animation-delay-1000"}),(0,d.jsx)("div",{className:"absolute top-[35%] left-[25%] w-1 h-1 bg-purple-200 rounded-full animate-pulse opacity-60 animation-delay-2000"}),(0,d.jsx)("div",{className:"absolute top-[45%] right-[35%] w-2 h-2 bg-yellow-200 rounded-full animate-pulse opacity-75 animation-delay-3000"}),(0,d.jsx)("div",{className:"absolute top-[60%] left-[40%] w-1.5 h-1.5 bg-white rounded-full animate-pulse opacity-80 animation-delay-4000"}),(0,d.jsx)("div",{className:"absolute top-[75%] right-[15%] w-1 h-1 bg-blue-300 rounded-full animate-pulse opacity-65 animation-delay-5000"}),(0,d.jsx)("div",{className:"absolute top-[85%] left-[60%] w-2 h-2 bg-purple-300 rounded-full animate-pulse opacity-70 animation-delay-6000"}),(0,d.jsx)("div",{className:"absolute top-[15%] left-[70%] w-1 h-1 bg-white rounded-full animate-pulse opacity-50 animation-delay-1500"}),(0,d.jsx)("div",{className:"absolute top-[25%] left-[50%] w-0.5 h-0.5 bg-blue-200 rounded-full animate-pulse opacity-60 animation-delay-2500"}),(0,d.jsx)("div",{className:"absolute top-[40%] right-[60%] w-1 h-1 bg-purple-200 rounded-full animate-pulse opacity-45 animation-delay-3500"}),(0,d.jsx)("div",{className:"absolute top-[55%] left-[80%] w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse opacity-55 animation-delay-4500"}),(0,d.jsx)("div",{className:"absolute top-[70%] right-[45%] w-1 h-1 bg-white rounded-full animate-pulse opacity-50 animation-delay-5500"}),(0,d.jsx)("div",{className:"absolute top-[80%] left-[30%] w-0.5 h-0.5 bg-blue-300 rounded-full animate-pulse opacity-40 animation-delay-6500"}),(0,d.jsx)("div",{className:"absolute top-[12%] right-[80%] w-0.5 h-0.5 bg-white rounded-full animate-pulse opacity-30 animation-delay-500"}),(0,d.jsx)("div",{className:"absolute top-[28%] left-[85%] w-0.5 h-0.5 bg-purple-300 rounded-full animate-pulse opacity-35 animation-delay-1200"}),(0,d.jsx)("div",{className:"absolute top-[42%] left-[10%] w-0.5 h-0.5 bg-blue-200 rounded-full animate-pulse opacity-40 animation-delay-2200"}),(0,d.jsx)("div",{className:"absolute top-[58%] right-[75%] w-0.5 h-0.5 bg-yellow-200 rounded-full animate-pulse opacity-30 animation-delay-3200"}),(0,d.jsx)("div",{className:"absolute top-[72%] left-[75%] w-0.5 h-0.5 bg-white rounded-full animate-pulse opacity-35 animation-delay-4200"}),(0,d.jsx)("div",{className:"absolute top-[88%] right-[85%] w-0.5 h-0.5 bg-purple-200 rounded-full animate-pulse opacity-25 animation-delay-5200"}),(0,d.jsx)("div",{className:"absolute top-[8%] left-[45%] w-0.5 h-0.5 bg-white rounded-full animate-pulse opacity-25 animation-delay-800"}),(0,d.jsx)("div",{className:"absolute top-[18%] right-[65%] w-0.5 h-0.5 bg-blue-100 rounded-full animate-pulse opacity-30 animation-delay-1800"}),(0,d.jsx)("div",{className:"absolute top-[32%] left-[65%] w-0.5 h-0.5 bg-purple-100 rounded-full animate-pulse opacity-35 animation-delay-2800"}),(0,d.jsx)("div",{className:"absolute top-[48%] right-[85%] w-0.5 h-0.5 bg-yellow-100 rounded-full animate-pulse opacity-25 animation-delay-3800"}),(0,d.jsx)("div",{className:"absolute top-[62%] left-[15%] w-0.5 h-0.5 bg-white rounded-full animate-pulse opacity-30 animation-delay-4800"}),(0,d.jsx)("div",{className:"absolute top-[78%] right-[55%] w-0.5 h-0.5 bg-blue-100 rounded-full animate-pulse opacity-25 animation-delay-5800"}),(0,d.jsx)("div",{className:"absolute top-[92%] left-[85%] w-0.5 h-0.5 bg-purple-100 rounded-full animate-pulse opacity-20 animation-delay-6800"})]}),(0,d.jsx)("div",{className:"absolute top-[20%] left-[10%] w-96 h-96 bg-purple-500/10 rounded-full filter blur-3xl animate-float"}),(0,d.jsx)("div",{className:"absolute bottom-[20%] right-[10%] w-80 h-80 bg-blue-500/10 rounded-full filter blur-3xl animate-float animation-delay-3000"}),(0,d.jsx)("div",{className:"absolute top-[50%] left-[50%] w-64 h-64 bg-indigo-500/10 rounded-full filter blur-3xl animate-float animation-delay-6000"}),(0,d.jsx)("div",{className:"absolute top-[30%] left-[20%] w-1 h-20 bg-gradient-to-b from-white to-transparent opacity-60 rotate-45 animate-meteor"}),(0,d.jsx)("div",{className:"absolute top-[60%] right-[30%] w-0.5 h-16 bg-gradient-to-b from-blue-200 to-transparent opacity-50 rotate-45 animate-meteor animation-delay-8000"}),(0,d.jsx)("div",{className:"absolute top-[25%] left-[30%] w-16 h-0.5 bg-gradient-to-r from-transparent via-purple-300/30 to-transparent rotate-12"}),(0,d.jsx)("div",{className:"absolute top-[65%] right-[25%] w-12 h-0.5 bg-gradient-to-r from-transparent via-blue-300/30 to-transparent -rotate-12"}),(0,d.jsxs)("div",{className:"absolute top-[40%] right-[20%] w-8 h-8 opacity-20",children:[(0,d.jsx)("div",{className:"w-full h-full border border-purple-300/50 rounded-full animate-spin-slow"}),(0,d.jsx)("div",{className:"absolute inset-2 border border-blue-300/50 rounded-full animate-spin-reverse"})]}),(0,d.jsxs)("div",{className:"absolute bottom-[30%] left-[25%] w-6 h-6 opacity-15",children:[(0,d.jsx)("div",{className:"w-full h-full border border-yellow-300/50 rounded-full animate-spin-slow animation-delay-4000"}),(0,d.jsx)("div",{className:"absolute inset-1 border border-purple-300/50 rounded-full animate-spin-reverse animation-delay-2000"})]})]})}},90983:(a,b,c)=>{"use strict";c.d(b,{A:()=>l});var d=c(60687),e=c(85814),f=c.n(e),g=c(71330),h=c(43210),i=c(11437),j=c(16189);function k(){let[a,b]=(0,h.useState)(!1),[c,e]=(0,h.useState)(!1),f=(0,h.useRef)(null),k=(0,j.useRouter)(),l=(0,j.usePathname)(),m=(0,g.Ym)(),n=async a=>{if(a===m)return void b(!1);e(!0);let c=l.replace(`/${m}`,"")||"/",d=`/${a}${c}`;b(!1),await new Promise(a=>setTimeout(a,300)),k.push(d)};return(0,d.jsxs)("div",{className:"relative",ref:f,children:[(0,d.jsx)("button",{onClick:()=>!c&&b(!a),disabled:c,className:"flex items-center gap-2 px-3 py-2 text-white hover:text-purple-200 transition-all duration-200 min-h-[44px] min-w-[44px] justify-center md:justify-start rounded-lg hover:bg-white/10 active:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed","aria-label":"切换语言 / Switch Language",children:c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("svg",{className:"w-5 h-5 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,d.jsx)("span",{className:"text-sm md:text-base font-medium",children:"zh"===m?"切换中...":"Switching..."})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(i.A,{size:20,className:"flex-shrink-0"}),(0,d.jsx)("span",{className:"uppercase text-sm md:text-base font-medium",children:"zh"===m?"中文":"EN"}),(0,d.jsx)("svg",{className:`w-4 h-4 transition-transform duration-200 ${a?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),a&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"fixed inset-0 bg-black/20 z-40 md:hidden",onClick:()=>b(!1)}),(0,d.jsxs)("div",{className:`absolute right-0 top-full mt-2 bg-white rounded-xl shadow-2xl z-50
                          border border-gray-100 overflow-hidden
                          transform origin-top-right transition-all duration-200
                          ${a?"opacity-100 scale-100":"opacity-0 scale-95"}
                          min-w-[160px] md:min-w-[180px]`,children:[(0,d.jsx)("div",{className:"px-4 py-3 bg-gradient-to-r from-purple-50 to-blue-50 border-b border-gray-100",children:(0,d.jsxs)("p",{className:"text-sm font-semibold text-gray-700 flex items-center gap-2",children:[(0,d.jsx)(i.A,{size:16}),"zh"===m?"选择语言":"Select Language"]})}),(0,d.jsxs)("div",{className:"py-2",children:[(0,d.jsx)("button",{onClick:()=>n("en"),disabled:c,className:`block w-full px-4 py-3 text-left hover:bg-purple-50 active:bg-purple-100
                           transition-all duration-150 text-base font-medium group disabled:opacity-50 disabled:cursor-not-allowed
                           ${"en"===m?"text-purple-600 bg-purple-50 border-r-4 border-purple-500":"text-gray-700"}`,children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("span",{className:"text-xl",children:"\uD83C\uDDFA\uD83C\uDDF8"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"font-medium",children:"English"}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:"English"})]}),"en"===m?(0,d.jsx)("svg",{className:"w-5 h-5 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}):c?(0,d.jsxs)("svg",{className:"w-4 h-4 animate-spin text-gray-400",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):null]})}),(0,d.jsx)("button",{onClick:()=>n("zh"),disabled:c,className:`block w-full px-4 py-3 text-left hover:bg-purple-50 active:bg-purple-100
                           transition-all duration-150 text-base font-medium group disabled:opacity-50 disabled:cursor-not-allowed
                           ${"zh"===m?"text-purple-600 bg-purple-50 border-r-4 border-purple-500":"text-gray-700"}`,children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("span",{className:"text-xl",children:"\uD83C\uDDE8\uD83C\uDDF3"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"font-medium",children:"中文"}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:"简体中文"})]}),"zh"===m?(0,d.jsx)("svg",{className:"w-5 h-5 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}):c?(0,d.jsxs)("svg",{className:"w-4 h-4 animate-spin text-gray-400",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):null]})})]}),(0,d.jsx)("div",{className:"px-4 py-2 bg-gray-50 border-t border-gray-100",children:(0,d.jsx)("p",{className:"text-xs text-gray-500 text-center",children:"zh"===m?"点击选择语言":"Click to select language"})})]})]})]})}function l(){let a=(0,g.Ym)();return(0,d.jsx)("header",{className:"relative z-50",children:(0,d.jsxs)("div",{className:"flex justify-between items-center p-4 md:p-6",children:[(0,d.jsxs)(f(),{href:`/${a}`,className:"text-white text-xl md:text-2xl font-bold hover:text-purple-200 transition-colors",children:["\uD83D\uDD2E ","zh"===a?"神秘塔罗":"Mystic Tarot"]}),(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsx)(k,{})})]})})}},94163:(a,b,c)=>{"use strict";c.d(b,{A:()=>f,I:()=>e});var d=c(35471);let e=["en","zh"],f=(0,d.A)(async({locale:a})=>{let b=a||"en";return{locale:b,messages:(await c(34918)(`./${b}.json`)).default}})},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(37413);let e={title:"Mystic Tarot - Free Online Tarot Reading",description:"Free online tarot reading service. Explore your inner world through ancient tarot wisdom."};function f({children:a}){return(0,d.jsx)("html",{children:(0,d.jsx)("body",{children:a})})}},96487:()=>{},96813:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))}};