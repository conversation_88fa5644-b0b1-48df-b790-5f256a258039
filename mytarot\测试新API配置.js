// 测试新的IP检测API配置
// 在浏览器控制台中运行此代码

const apis = [
  {
    url: 'https://ipapi.co/json/',
    parseCountry: (data) => data.country_code
  },
  {
    url: 'https://ip-api.com/json/',
    parseCountry: (data) => data.countryCode
  },
  {
    url: 'https://ipinfo.io/json',
    parseCountry: (data) => data.country
  }
];

async function testAllAPIs() {
  console.log('🚀 开始测试所有IP检测API...\n');
  
  for (let i = 0; i < apis.length; i++) {
    const api = apis[i];
    console.log(`📡 测试API ${i + 1}: ${api.url}`);
    
    try {
      const startTime = performance.now();
      
      const response = await fetch(api.url, {
        signal: AbortSignal.timeout(5000),
        headers: {
          'Accept': 'application/json'
        }
      });
      
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      if (!response.ok) {
        console.log(`❌ HTTP错误: ${response.status}`);
        continue;
      }
      
      const data = await response.json();
      const countryCode = api.parseCountry(data);
      
      console.log(`✅ 成功 (${responseTime}ms)`);
      console.log(`   IP地址: ${data.ip || '未知'}`);
      console.log(`   国家代码: ${countryCode}`);
      console.log(`   国家名称: ${data.country_name || data.country || '未知'}`);
      console.log(`   城市: ${data.city || '未知'}`);
      
      // 判断语言
      if (countryCode === 'CN') {
        console.log(`   🇨🇳 语言设置: 中文`);
      } else {
        console.log(`   🌐 语言设置: 英文`);
      }
      
      console.log(`   原始数据:`, data);
      
    } catch (error) {
      console.log(`❌ 失败: ${error.message}`);
    }
    
    console.log(''); // 空行分隔
  }
  
  console.log('🏁 测试完成');
}

// 测试后端API
async function testBackendAPI() {
  console.log('🔧 测试后端API: /api/detect-location\n');
  
  try {
    const startTime = performance.now();
    
    const response = await fetch('/api/detect-location', {
      signal: AbortSignal.timeout(8000),
      headers: {
        'Accept': 'application/json'
      }
    });
    
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);
    
    if (!response.ok) {
      console.log(`❌ HTTP错误: ${response.status}`);
      return;
    }
    
    const result = await response.json();
    
    console.log(`✅ 后端API成功 (${responseTime}ms)`);
    console.log(`   成功状态: ${result.success}`);
    
    if (result.success && result.data) {
      const { ip, country, countryCode, city, source } = result.data;
      console.log(`   IP地址: ${ip}`);
      console.log(`   国家: ${country}`);
      console.log(`   国家代码: ${countryCode}`);
      console.log(`   城市: ${city || '未知'}`);
      console.log(`   数据源: ${source}`);
      
      if (countryCode === 'CN') {
        console.log(`   🇨🇳 语言设置: 中文`);
      } else {
        console.log(`   🌐 语言设置: 英文`);
      }
    } else {
      console.log(`   消息: ${result.message || '未知错误'}`);
    }
    
    console.log(`   完整响应:`, result);
    
  } catch (error) {
    console.log(`❌ 后端API失败: ${error.message}`);
  }
  
  console.log(''); // 空行分隔
}

// 运行所有测试
async function runAllTests() {
  console.log('🧪 IP检测API测试套件\n');
  console.log('=' * 50);
  
  // 先测试后端API
  await testBackendAPI();
  
  console.log('=' * 50);
  
  // 再测试前端API
  await testAllAPIs();
  
  console.log('=' * 50);
  console.log('✨ 所有测试完成！');
}

// 自动运行测试
runAllTests();

// 也可以单独运行
// testBackendAPI();  // 只测试后端
// testAllAPIs();     // 只测试前端API
