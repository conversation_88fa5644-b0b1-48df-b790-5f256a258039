// Cloudflare Pages Functions 中间件
// 用于处理数据库连接和环境变量

export async function onRequest(context: any) {
  // 将 D1 数据库实例添加到环境中
  context.env.DB = context.env.DB;
  
  // 设置 CORS 头
  const response = await context.next();
  
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  return response;
}
