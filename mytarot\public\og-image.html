<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #1e1b4b 0%, #7c3aed 50%, #1e1b4b 100%);
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .stars {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .star {
            position: absolute;
            background: #fbbf24;
            border-radius: 50%;
            opacity: 0.8;
        }
        
        .star1 { width: 4px; height: 4px; top: 100px; left: 150px; }
        .star2 { width: 3px; height: 3px; top: 150px; left: 300px; opacity: 0.6; }
        .star3 { width: 5px; height: 5px; top: 80px; left: 450px; }
        .star4 { width: 3px; height: 3px; top: 120px; left: 750px; opacity: 0.7; }
        .star5 { width: 4px; height: 4px; top: 180px; left: 900px; }
        .star6 { width: 3px; height: 3px; top: 90px; left: 1050px; opacity: 0.6; }
        .star7 { width: 3px; height: 3px; top: 500px; left: 200px; opacity: 0.7; }
        .star8 { width: 4px; height: 4px; top: 520px; left: 400px; }
        .star9 { width: 3px; height: 3px; top: 480px; left: 800px; opacity: 0.7; }
        .star10 { width: 4px; height: 4px; top: 550px; left: 1000px; }
        
        .title {
            font-size: 72px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .subtitle {
            font-size: 36px;
            color: #c084fc;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .description {
            font-size: 24px;
            color: #e9d5ff;
            text-align: center;
            max-width: 800px;
        }
        
        .moon {
            position: absolute;
            left: 100px;
            top: 300px;
            width: 50px;
            height: 50px;
            background: #fbbf24;
            border-radius: 50%;
            opacity: 0.8;
        }
        
        .moon::after {
            content: '';
            position: absolute;
            top: -8px;
            left: 8px;
            width: 40px;
            height: 40px;
            background: #1e1b4b;
            border-radius: 50%;
        }
        
        .crystal {
            position: absolute;
            right: 100px;
            bottom: 150px;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, rgba(168, 85, 247, 0.6) 0%, rgba(168, 85, 247, 0.3) 70%, transparent 100%);
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="stars">
        <div class="star star1"></div>
        <div class="star star2"></div>
        <div class="star star3"></div>
        <div class="star star4"></div>
        <div class="star star5"></div>
        <div class="star star6"></div>
        <div class="star star7"></div>
        <div class="star star8"></div>
        <div class="star star9"></div>
        <div class="star star10"></div>
    </div>
    
    <div class="moon"></div>
    <div class="crystal"></div>
    
    <h1 class="title">Mystic Tarot</h1>
    <h2 class="subtitle">Free AI Tarot Reading Online</h2>
    <p class="description">Professional Psychic Tarot Reading with AI-Powered Insights</p>
</body>
</html>
