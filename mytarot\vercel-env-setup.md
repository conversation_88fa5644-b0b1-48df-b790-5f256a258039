# Vercel 环境变量配置指南

## 方法1：通过 Vercel Dashboard 配置

1. 访问 [vercel.com](https://vercel.com) 并登录
2. 找到你的项目
3. 进入 Settings → Environment Variables
4. 添加以下环境变量：

### 必需的环境变量

```bash
# NextAuth 配置
NEXTAUTH_URL=https://your-app-name.vercel.app
NEXTAUTH_SECRET=mystic-tarot-secret-key-2024

# Google OAuth
GOOGLE_CLIENT_ID=265729365221-er2q8kuk589k7hln7m8cb5m61okr4eiv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# GitHub OAuth
GITHUB_ID=********************
GITHUB_SECRET=****************************************

# OpenAI API
OPENAI_API_KEY=sk-IslnGB05pQQBD9y9d8miirlBxBNVovnPNb2rHcg8MlYoYJzF
OPENAI_BASE_URL=https://api.chatanywhere.tech/v1

# 邮件配置
EMAIL_SERVER_HOST=smtp.163.com
EMAIL_SERVER_PORT=465
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=FDk6ZzSiftf7XZ8K
EMAIL_FROM=<EMAIL>

# Creem 支付
CREEM_API_KEY=creem_test_3GlwT5RzjwOF2jCOsQMmGY
CREEM_PRODUCT_ID=prod_5f1A39IxIXP08P1qyLBoPK
NEXT_PUBLIC_CREEM_API_KEY=creem_test_3GlwT5RzjwOF2jCOsQMmGY
NEXT_PUBLIC_CREEM_PRODUCT_ID=prod_5f1A39IxIXP08P1qyLBoPK
```

## 方法2：使用 Vercel CLI

1. 安装 Vercel CLI：
```bash
npm install -g vercel
```

2. 登录 Vercel：
```bash
vercel login
```

3. 在项目目录中运行：
```bash
vercel env add NEXTAUTH_SECRET
# 输入值：mystic-tarot-secret-key-2024

vercel env add NEXTAUTH_URL
# 输入值：https://your-app-name.vercel.app
```

## 数据库配置

### 添加 Vercel Postgres

1. 在 Vercel Dashboard 中，进入你的项目
2. 点击 Storage 标签
3. 点击 "Create Database"
4. 选择 "Postgres"
5. 创建数据库后，Vercel 会自动添加以下环境变量：
   - `DATABASE_URL`
   - `DIRECT_URL`
   - `POSTGRES_URL`
   - `POSTGRES_PRISMA_URL`
   - `POSTGRES_URL_NON_POOLING`

## 重要提醒

1. **NEXTAUTH_URL**: 必须设置为你的实际域名，如 `https://your-app-name.vercel.app`
2. **数据库迁移**: 部署后需要运行数据库迁移
3. **OAuth 回调**: 需要在 Google 和 GitHub 中更新回调 URL

## 部署后的步骤

1. 运行数据库迁移：
```bash
npx prisma migrate deploy
```

2. 生成 Prisma 客户端：
```bash
npx prisma generate
```

3. 更新 OAuth 应用的回调 URL：
   - Google: `https://your-app-name.vercel.app/api/auth/callback/google`
   - GitHub: `https://your-app-name.vercel.app/api/auth/callback/github`
