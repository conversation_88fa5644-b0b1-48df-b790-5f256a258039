"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/hooks/useIPLanguage.ts":
/*!************************************!*\
  !*** ./src/hooks/useIPLanguage.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIPLanguage: () => (/* binding */ useIPLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useIPLanguage auto */ \nfunction useIPLanguage() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('en');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [ipInfo, setIpInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useIPLanguage.useEffect\": ()=>{\n            const detectLanguageFromIP = {\n                \"useIPLanguage.useEffect.detectLanguageFromIP\": async ()=>{\n                    try {\n                        // 首先获取用户IP地址\n                        let userIP = '';\n                        try {\n                            const ipResponse = await fetch('https://api.ipify.org?format=json', {\n                                signal: AbortSignal.timeout(3000)\n                            });\n                            if (ipResponse.ok) {\n                                const ipData = await ipResponse.json();\n                                userIP = ipData.ip;\n                                console.log('获取到用户IP:', userIP);\n                            }\n                        } catch (error) {\n                            var _navigator_languages;\n                            console.warn('获取IP地址失败:', error);\n                            // 如果获取IP失败，使用浏览器语言作为回退\n                            const browserLang = navigator.language || ((_navigator_languages = navigator.languages) === null || _navigator_languages === void 0 ? void 0 : _navigator_languages[0]) || 'en';\n                            if (browserLang.startsWith('zh')) {\n                                setLanguage('zh');\n                                console.log('🌐 IP检测失败，根据浏览器语言使用中文');\n                            } else {\n                                setLanguage('en');\n                                console.log('🌐 IP检测失败，根据浏览器语言使用英文');\n                            }\n                            setIsLoading(false);\n                            return;\n                        }\n                        // 使用新的IP检测接口\n                        if (userIP) {\n                            try {\n                                const controller = new AbortController();\n                                const timeoutId = setTimeout({\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP.timeoutId\": ()=>controller.abort()\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP.timeoutId\"], 5000);\n                                const response = await fetch('https://www.free-api.com/urltask', {\n                                    method: 'POST',\n                                    signal: controller.signal,\n                                    headers: {\n                                        'Content-Type': 'application/json',\n                                        'Accept': 'application/json'\n                                    },\n                                    body: JSON.stringify({\n                                        ip: userIP,\n                                        fzsid: 90\n                                    })\n                                });\n                                clearTimeout(timeoutId);\n                                if (!response.ok) {\n                                    throw new Error(\"HTTP \".concat(response.status));\n                                }\n                                const data = await response.json();\n                                console.log('IP检测响应:', data);\n                                if (data.resultcode === \"200\" && data.result) {\n                                    const country = data.result.Country || '';\n                                    const info = {\n                                        country: country,\n                                        countryCode: country === '中国' ? 'CN' : 'OTHER',\n                                        ip: userIP\n                                    };\n                                    setIpInfo(info);\n                                    // 如果是中国，使用中文\n                                    if (country === '中国') {\n                                        setLanguage('zh');\n                                        console.log('🇨🇳 检测到中国IP，切换到中文');\n                                    } else {\n                                        setLanguage('en');\n                                        console.log('🌐 检测到海外IP，使用英文');\n                                    }\n                                } else {\n                                    throw new Error('API返回格式错误');\n                                }\n                            } catch (error) {\n                                var _navigator_languages1;\n                                console.warn('IP地理位置检测失败:', error);\n                                // 回退到浏览器语言检测\n                                const browserLang = navigator.language || ((_navigator_languages1 = navigator.languages) === null || _navigator_languages1 === void 0 ? void 0 : _navigator_languages1[0]) || 'en';\n                                if (browserLang.startsWith('zh')) {\n                                    setLanguage('zh');\n                                    console.log('🌐 IP检测失败，根据浏览器语言使用中文');\n                                } else {\n                                    setLanguage('en');\n                                    console.log('🌐 IP检测失败，根据浏览器语言使用英文');\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error('语言检测失败:', error);\n                        // 默认使用英文\n                        setLanguage('en');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useIPLanguage.useEffect.detectLanguageFromIP\"];\n            detectLanguageFromIP();\n        }\n    }[\"useIPLanguage.useEffect\"], []);\n    return {\n        language,\n        isLoading,\n        ipInfo,\n        setLanguage\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useIPLanguage.ts\n"));

/***/ })

});