# 塔罗牌应用性能优化说明

## 已完成的优化

### 1. IP自动语言检测
- 创建了 `useIPLanguage` hook，自动检测用户IP并切换语言
- 中国IP自动使用中文，其他地区使用英文
- 如果IP检测失败，会根据浏览器语言设置进行回退
- 添加了 `LanguageContext` 提供统一的语言管理

### 2. 动画性能优化
- **TarotCard组件**：
  - 使用 `memo` 包装组件，减少不必要的重渲染
  - 简化hover动画效果（从 `scale: 1.05, y: -10` 改为 `scale: 1.02`）
  - 减少动画持续时间（从 0.3s 改为 0.2s）
  - 移除复杂的初始动画（去掉 `y: 50` 的位移）

- **CardSelection组件**：
  - 动画类型从 `spring` 改为 `tween`，性能更好
  - 减少动画延迟（从 `index * 0.02` 改为 `index * 0.01`）
  - 简化hover效果（移除旋转和Y轴位移）
  - 动画持续时间从 0.8s 减少到 0.4s

- **CardResults组件**：
  - 卡牌翻转动画延迟从 800ms 减少到 600ms
  - 优化翻译函数调用，减少重复计算

### 3. 图片加载优化 ⭐ **重要优化**
- **按需加载塔罗牌正面图片**：在卡牌选择阶段只显示背面，避免加载78张正面图片
- 创建了轻量级的 `CardBack` 组件专门用于选择阶段
- 只有在卡牌翻转显示时才加载正面图片，大幅减少初始加载时间
- 为卡牌背面图片添加 `priority={true}` 属性
- 添加 `placeholder="blur"` 和 `blurDataURL` 提升加载体验
- 使用 WebP 格式图片（已在 next.config.ts 中配置）

### 4. 网络请求优化
- IP检测服务添加了 3秒超时限制
- 使用 AbortController 正确处理请求取消
- 多个IP检测服务备选，提高成功率

### 5. 代码优化
- 移除了不必要的提示词生成功能和相关API路由
- 删除了未使用的 `LazyCardGrid` 组件
- 简化了组件状态管理
- 减少了不必要的import和依赖
- 创建了专用的 `CardBack` 组件，避免在选择阶段渲染复杂的 `TarotCard` 组件

## 性能监控

项目中包含了 `PerformanceMonitor` 组件，在开发环境中可以：
- 按 `Ctrl+Shift+P` 显示/隐藏性能监控面板
- 实时查看FPS、内存使用量和加载时间

## 建议的进一步优化

### 1. 图片优化
```bash
# 可以考虑将塔罗牌图片转换为更小的WebP格式
# 或者使用Next.js的图片优化API
```

### 2. 代码分割
```javascript
// 可以考虑懒加载某些组件
const CardResults = lazy(() => import('@/components/CardResults'));
```

### 3. 缓存优化
- 考虑缓存塔罗牌数据
- 缓存用户的语言偏好设置

### 4. 服务端优化
- 考虑使用CDN加速图片加载
- 优化API响应时间

## 使用说明

1. **语言切换**：现在会自动根据用户IP检测语言，无需手动切换
2. **性能监控**：开发环境下按 `Ctrl+Shift+P` 查看性能指标
3. **动画效果**：已优化为更流畅的体验，减少了卡顿

## 注意事项

- IP检测依赖第三方服务，如果服务不可用会回退到浏览器语言检测
- 某些动画效果已简化，如需要更复杂的效果可以适当调整
- 建议在生产环境中监控实际性能表现
