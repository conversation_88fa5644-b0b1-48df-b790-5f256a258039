// This is your Prisma schema file for production (PostgreSQL)
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  // Vercel Postgres + Prisma Accelerate 配置
  // DATABASE_URL 使用 Prisma Accelerate (prisma+postgres://)
  // DIRECT_URL 使用直连 (postgres://)
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                    String         @id @default(cuid())
  name                  String?
  email                 String?        @unique
  emailVerified         DateTime?
  image                 String?
  birthday              DateTime?
  freeReadingsUsed      Int            @default(0) // 已使用的免费解读次数
  freeReadingsLimit     Int            @default(2) // 免费解读次数限制（改为2次）
  isPremium             Boolean        @default(false) // 是否为付费用户
  subscriptionStatus    String? // 订阅状态: active, cancelled, expired
  subscriptionId        String? // Creem订阅ID
  subscriptionExpiresAt DateTime? // 订阅过期时间
  dailyReadingsUsed     Int            @default(0) // 每日已使用的解读次数（付费用户）
  dailyReadingsLimit    Int            @default(30) // 每日解读次数限制（付费用户每天30次）
  lastReadingDate       DateTime? // 最后一次解读的日期
  createdAt             DateTime       @default(now())
  updatedAt             DateTime       @updatedAt
  accounts              Account[]
  sessions              Session[]
  readings              TarotReading[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("VerificationToken")
}

model TarotReading {
  id             String   @id @default(cuid())
  userId         String
  question       String
  cards          String // JSON string of selected cards
  interpretation String?
  timestamp      DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}
