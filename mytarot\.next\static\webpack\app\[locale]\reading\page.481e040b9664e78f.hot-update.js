"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/hooks/useIPLanguage.ts":
/*!************************************!*\
  !*** ./src/hooks/useIPLanguage.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIPLanguage: () => (/* binding */ useIPLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useIPLanguage auto */ \nfunction useIPLanguage() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('en');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [ipInfo, setIpInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useIPLanguage.useEffect\": ()=>{\n            const detectLanguageFromIP = {\n                \"useIPLanguage.useEffect.detectLanguageFromIP\": async ()=>{\n                    try {\n                        // 尝试多个IP检测服务\n                        const services = [\n                            'https://ipapi.co/json/',\n                            'https://api.ipify.org?format=json',\n                            'https://httpbin.org/ip'\n                        ];\n                        let detected = false;\n                        for (const service of services){\n                            try {\n                                const controller = new AbortController();\n                                const timeoutId = setTimeout({\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP.timeoutId\": ()=>controller.abort()\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP.timeoutId\"], 3000);\n                                const response = await fetch(service, {\n                                    signal: controller.signal,\n                                    headers: {\n                                        'Accept': 'application/json'\n                                    }\n                                });\n                                clearTimeout(timeoutId);\n                                if (!response.ok) continue;\n                                const data = await response.json();\n                                // 处理不同服务的响应格式\n                                let countryCode = '';\n                                if (data.country_code) {\n                                    countryCode = data.country_code;\n                                } else if (data.country) {\n                                    countryCode = data.country;\n                                }\n                                if (countryCode) {\n                                    const info = {\n                                        country: data.country || data.country_name || '',\n                                        countryCode: countryCode,\n                                        ip: data.ip || data.origin || ''\n                                    };\n                                    setIpInfo(info);\n                                    // 如果是中国，使用中文\n                                    if (countryCode === 'CN') {\n                                        setLanguage('zh');\n                                        console.log('🇨🇳 检测到中国IP，切换到中文');\n                                    } else {\n                                        setLanguage('en');\n                                        console.log('🌐 检测到海外IP，使用英文');\n                                    }\n                                    detected = true;\n                                    break;\n                                }\n                            } catch (error) {\n                                console.warn(\"IP检测服务失败: \".concat(service), error);\n                                continue;\n                            }\n                        }\n                        if (!detected) {\n                            var _navigator_languages;\n                            // 如果所有服务都失败，尝试从浏览器语言推断\n                            const browserLang = navigator.language || ((_navigator_languages = navigator.languages) === null || _navigator_languages === void 0 ? void 0 : _navigator_languages[0]) || 'en';\n                            if (browserLang.startsWith('zh')) {\n                                setLanguage('zh');\n                                console.log('🌐 IP检测失败，根据浏览器语言使用中文');\n                            } else {\n                                setLanguage('en');\n                                console.log('🌐 IP检测失败，根据浏览器语言使用英文');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('语言检测失败:', error);\n                        // 默认使用英文\n                        setLanguage('en');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useIPLanguage.useEffect.detectLanguageFromIP\"];\n            detectLanguageFromIP();\n        }\n    }[\"useIPLanguage.useEffect\"], []);\n    return {\n        language,\n        isLoading,\n        ipInfo,\n        setLanguage\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useIPLanguage.ts\n"));

/***/ })

});