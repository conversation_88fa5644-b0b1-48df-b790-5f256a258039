'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Shuffle, ArrowRight } from 'lucide-react';
import Navigation from '@/components/Navigation';
import ShuffleAnimation from '@/components/ShuffleAnimation';
import CardSelection from '@/components/CardSelection';
import CardResults from '@/components/CardResults';
import StarryBackground from '@/components/StarryBackground';

import { TarotCard as TarotCardType } from '@/types/tarot';
import { useTranslations } from 'next-intl';

export default function ReadingPage() {
  const t = useTranslations('reading');
  const [isShuffling, setIsShuffling] = useState(false);
  const [showCardSelection, setShowCardSelection] = useState(false);
  const [selectedCards, setSelectedCards] = useState<TarotCardType[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // 防止hydration错误
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleStartShuffle = () => {
    setIsShuffling(true);
  };

  const handleShuffleComplete = () => {
    setIsShuffling(false);
    setShowCardSelection(true);
  };

  const handleCardsSelected = (cards: TarotCardType[]) => {
    setSelectedCards(cards);
    setShowCardSelection(false);
    setShowResults(true);
  };

  const handleNewReading = () => {
    setIsShuffling(false);
    setShowCardSelection(false);
    setSelectedCards([]);
    setShowResults(false);
  };

  // 防止hydration错误，在客户端挂载前显示加载状态
  if (!isMounted) {
    return (
      <div className="min-h-screen relative overflow-hidden bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <StarryBackground />
        <div className="text-white text-xl relative z-10">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900">
      <StarryBackground />
      {!showResults && <Navigation />}

      <ShuffleAnimation
        isShuffling={isShuffling}
        onComplete={handleShuffleComplete}
      />

      {showResults ? (
        <CardResults
          selectedCards={selectedCards}
          onNewReading={handleNewReading}
        />
      ) : (
        <main className="container mx-auto px-6 py-12">
        {!showCardSelection && !showResults ? (
          <motion.div
            className="max-w-2xl mx-auto text-center"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              {t('title')}
            </h1>

            <p className="text-xl text-purple-200 mb-8">
              {t('description')}
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">
                {t('instructions.title')}
              </h2>

              <div className="space-y-4 text-left">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold mt-1">
                    1
                  </div>
                  <p className="text-purple-100">
                    {t('instructions.step1')}
                  </p>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold mt-1">
                    2
                  </div>
                  <p className="text-purple-100">
                    {t('instructions.step2')}
                  </p>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold mt-1">
                    3
                  </div>
                  <p className="text-purple-100">
                    {t('instructions.step3')}
                  </p>
                </div>
              </div>
            </div>

            <motion.button
              onClick={handleStartShuffle}
              className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-full hover:from-purple-700 hover:to-pink-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={isShuffling}
            >
              <Shuffle size={24} />
              {t('startShuffle')}
              <ArrowRight size={20} />
            </motion.button>
          </motion.div>
        ) : showCardSelection ? (
          <CardSelection onCardsSelected={handleCardsSelected} />
        ) : showResults ? (
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold text-white mb-8">
              Your Reading
            </h2>

            <p className="text-purple-200 mb-8">
              Here are your selected cards and their meanings...
            </p>

            {/* 这里将在下一步添加结果显示界面 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <p className="text-white">
                结果显示界面即将实现...
              </p>
              <div className="flex justify-center gap-4 mt-4">
                {selectedCards.map((card, index) => (
                  <div key={card.id} className="text-white">
                    <p>{index + 1}. {card.name}</p>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        ) : null}
      </main>
      )}
    </div>
  );
}
