'use client';

import { useState, useEffect } from 'react';

interface IPInfo {
  country: string;
  countryCode: string;
  ip: string;
}

export function useIPLanguage() {
  const [language, setLanguage] = useState<'zh' | 'en'>('en');
  const [isLoading, setIsLoading] = useState(true);
  const [ipInfo, setIpInfo] = useState<IPInfo | null>(null);

  useEffect(() => {
    const detectLanguageFromIP = async () => {
      try {
        // 尝试多个IP检测服务
        const services = [
          'https://ipapi.co/json/',
          'https://api.ipify.org?format=json',
          'https://httpbin.org/ip'
        ];

        let detected = false;
        
        for (const service of services) {
          try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 3000);

            const response = await fetch(service, {
              signal: controller.signal,
              headers: {
                'Accept': 'application/json'
              }
            });

            clearTimeout(timeoutId);
            
            if (!response.ok) continue;
            
            const data = await response.json();
            
            // 处理不同服务的响应格式
            let countryCode = '';
            if (data.country_code) {
              countryCode = data.country_code;
            } else if (data.country) {
              countryCode = data.country;
            }
            
            if (countryCode) {
              const info: IPInfo = {
                country: data.country || data.country_name || '',
                countryCode: countryCode,
                ip: data.ip || data.origin || ''
              };
              
              setIpInfo(info);
              
              // 如果是中国，使用中文
              if (countryCode === 'CN') {
                setLanguage('zh');
                console.log('🇨🇳 检测到中国IP，切换到中文');
              } else {
                setLanguage('en');
                console.log('🌐 检测到海外IP，使用英文');
              }
              
              detected = true;
              break;
            }
          } catch (error) {
            console.warn(`IP检测服务失败: ${service}`, error);
            continue;
          }
        }
        
        if (!detected) {
          // 如果所有服务都失败，尝试从浏览器语言推断
          const browserLang = navigator.language || navigator.languages?.[0] || 'en';
          if (browserLang.startsWith('zh')) {
            setLanguage('zh');
            console.log('🌐 IP检测失败，根据浏览器语言使用中文');
          } else {
            setLanguage('en');
            console.log('🌐 IP检测失败，根据浏览器语言使用英文');
          }
        }
      } catch (error) {
        console.error('语言检测失败:', error);
        // 默认使用英文
        setLanguage('en');
      } finally {
        setIsLoading(false);
      }
    };

    detectLanguageFromIP();
  }, []);

  return {
    language,
    isLoading,
    ipInfo,
    setLanguage // 允许手动切换语言
  };
}
