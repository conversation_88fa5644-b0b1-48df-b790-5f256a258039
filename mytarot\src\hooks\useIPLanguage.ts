'use client';

import { useState, useEffect } from 'react';

interface IPInfo {
  country: string;
  countryCode: string;
  ip: string;
}

export function useIPLanguage() {
  const [language, setLanguage] = useState<'zh' | 'en'>('en');
  const [isLoading, setIsLoading] = useState(true);
  const [ipInfo, setIpInfo] = useState<IPInfo | null>(null);

  useEffect(() => {
    const detectLanguageFromIP = async () => {
      try {
        // 首先获取用户IP地址
        let userIP = '';

        try {
          const ipResponse = await fetch('https://api.ipify.org?format=json', {
            signal: AbortSignal.timeout(3000)
          });

          if (ipResponse.ok) {
            const ipData = await ipResponse.json();
            userIP = ipData.ip;
            console.log('获取到用户IP:', userIP);
          }
        } catch (error) {
          console.warn('获取IP地址失败:', error);
          // 如果获取IP失败，使用浏览器语言作为回退
          const browserLang = navigator.language || navigator.languages?.[0] || 'en';
          if (browserLang.startsWith('zh')) {
            setLanguage('zh');
            console.log('🌐 IP检测失败，根据浏览器语言使用中文');
          } else {
            setLanguage('en');
            console.log('🌐 IP检测失败，根据浏览器语言使用英文');
          }
          setIsLoading(false);
          return;
        }

        // 使用新的IP检测接口
        if (userIP) {
          try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch('https://www.free-api.com/urltask', {
              method: 'POST',
              signal: controller.signal,
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              body: JSON.stringify({
                ip: userIP,
                fzsid: 90
              })
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
              throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            console.log('IP检测响应:', data);

            if (data.resultcode === "200" && data.result) {
              const country = data.result.Country || '';

              const info: IPInfo = {
                country: country,
                countryCode: country === '中国' ? 'CN' : 'OTHER',
                ip: userIP
              };

              setIpInfo(info);

              // 如果是中国，使用中文
              if (country === '中国') {
                setLanguage('zh');
                console.log('🇨🇳 检测到中国IP，切换到中文');
              } else {
                setLanguage('en');
                console.log('🌐 检测到海外IP，使用英文');
              }
            } else {
              throw new Error('API返回格式错误');
            }
          } catch (error) {
            console.warn('IP地理位置检测失败:', error);
            // 回退到浏览器语言检测
            const browserLang = navigator.language || navigator.languages?.[0] || 'en';
            if (browserLang.startsWith('zh')) {
              setLanguage('zh');
              console.log('🌐 IP检测失败，根据浏览器语言使用中文');
            } else {
              setLanguage('en');
              console.log('🌐 IP检测失败，根据浏览器语言使用英文');
            }
          }
        }
      } catch (error) {
        console.error('语言检测失败:', error);
        // 默认使用英文
        setLanguage('en');
      } finally {
        setIsLoading(false);
      }
    };

    detectLanguageFromIP();
  }, []);

  return {
    language,
    isLoading,
    ipInfo,
    setLanguage // 允许手动切换语言
  };
}
