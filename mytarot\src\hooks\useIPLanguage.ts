'use client';

import { useState, useEffect } from 'react';

interface IPInfo {
  country: string;
  countryCode: string;
  ip: string;
}

export function useIPLanguage() {
  const [language, setLanguage] = useState<'zh' | 'en'>('en');
  const [isLoading, setIsLoading] = useState(true);
  const [ipInfo, setIpInfo] = useState<IPInfo | null>(null);

  useEffect(() => {
    const detectLanguageFromIP = async () => {
      try {
        // 优先使用我们自己的API（避免跨域问题）
        try {
          console.log('使用后端API检测地理位置...');

          const response = await fetch('/api/detect-location', {
            signal: AbortSignal.timeout(8000),
            headers: {
              'Accept': 'application/json'
            }
          });

          if (response.ok) {
            const result = await response.json();
            console.log('后端API响应:', result);

            if (result.success && result.data) {
              const { country, countryCode, ip } = result.data;

              const info: IPInfo = {
                country: country,
                countryCode: countryCode,
                ip: ip
              };

              setIpInfo(info);

              // 判断是否为中国
              const isChinese = country === '中国' ||
                               country === 'China' ||
                               countryCode === 'CN';

              if (isChinese) {
                setLanguage('zh');
                console.log('🇨🇳 检测到中国IP，切换到中文');
              } else {
                setLanguage('en');
                console.log('🌐 检测到海外IP，使用英文');
              }

              setIsLoading(false);
              return; // 成功检测，直接返回
            }
          }
        } catch (error) {
          console.warn('后端API检测失败:', error);
        }

        // 后端API失败，尝试前端直接调用（可能有跨域问题）
        console.log('尝试前端直接IP检测...');

        const ipServices = [
          {
            url: 'https://ipapi.co/json/',
            parser: (data: any) => ({
              ip: data.ip,
              country: data.country_name,
              countryCode: data.country_code
            })
          },
          {
            url: 'https://ipinfo.io/json',
            parser: (data: any) => ({
              ip: data.ip,
              country: data.country === 'CN' ? '中国' : data.country,
              countryCode: data.country
            })
          }
        ];

        let detected = false;

        for (const service of ipServices) {
          try {
            console.log(`尝试IP检测服务: ${service.url}`);

            const response = await fetch(service.url, {
              signal: AbortSignal.timeout(5000),
              headers: {
                'Accept': 'application/json'
              }
            });

            if (!response.ok) {
              console.warn(`服务 ${service.url} 返回错误: ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`${service.url} 响应:`, data);

            const parsed = service.parser(data);

            if (parsed.ip && parsed.country) {
              const info: IPInfo = {
                country: parsed.country,
                countryCode: parsed.countryCode || '',
                ip: parsed.ip
              };

              setIpInfo(info);

              // 判断是否为中国
              const isChinese = parsed.country === '中国' ||
                               parsed.country === 'China' ||
                               parsed.countryCode === 'CN';

              if (isChinese) {
                setLanguage('zh');
                console.log('🇨🇳 检测到中国IP，切换到中文');
              } else {
                setLanguage('en');
                console.log('🌐 检测到海外IP，使用英文');
              }

              detected = true;
              break;
            }
          } catch (error) {
            console.warn(`IP检测服务 ${service.url} 失败:`, error);
            continue;
          }
        }

        // 如果所有IP检测服务都失败，使用浏览器语言
        if (!detected) {
          console.log('所有IP检测服务都失败，使用浏览器语言检测');
          const browserLang = navigator.language || navigator.languages?.[0] || 'en';
          if (browserLang.startsWith('zh')) {
            setLanguage('zh');
            console.log('🌐 根据浏览器语言使用中文');
          } else {
            setLanguage('en');
            console.log('🌐 根据浏览器语言使用英文');
          }
        }
      } catch (error) {
        console.error('语言检测失败:', error);
        // 默认使用英文
        setLanguage('en');
      } finally {
        setIsLoading(false);
      }
    };

    detectLanguageFromIP();
  }, []);

  return {
    language,
    isLoading,
    ipInfo,
    setLanguage // 允许手动切换语言
  };
}
