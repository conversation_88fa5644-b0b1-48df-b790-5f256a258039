import { useState } from 'react';
import { TarotCard } from '@/types/tarot';

interface TarotReadingResponse {
  reading: string;
  cards: TarotCard[];
  question: string;
}

interface UseTarotReadingReturn {
  reading: string;
  isLoading: boolean;
  error: string | null;
  generateReading: (cards: TarotCard[], question: string, stream?: boolean) => Promise<void>;
  streamReading: (cards: TarotCard[], question: string) => Promise<void>;
}

export function useTarotReading(): UseTarotReadingReturn {
  const [reading, setReading] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateReading = async (cards: TarotCard[], question: string, stream = false) => {
    setIsLoading(true);
    setError(null);
    setReading('');

    try {
      const response = await fetch('/api/reading', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cards,
          question,
          stream,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate reading');
      }

      const data: TarotReadingResponse = await response.json();
      setReading(data.reading);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const streamReading = async (cards: TarotCard[], question: string) => {
    setIsLoading(true);
    setError(null);
    setReading('');

    try {
      const response = await fetch('/api/reading', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cards,
          question,
          stream: true,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate reading');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              setIsLoading(false);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.content) {
                setReading(prev => prev + parsed.content);
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    reading,
    isLoading,
    error,
    generateReading,
    streamReading,
  };
}
