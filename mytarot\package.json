{"name": "my<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "axios": "^1.11.0", "clsx": "^2.1.1", "framer-motion": "^12.23.11", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "lucide-react": "^0.533.0", "next": "15.4.4", "next-intl": "^4.3.4", "openai": "^5.10.2", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}