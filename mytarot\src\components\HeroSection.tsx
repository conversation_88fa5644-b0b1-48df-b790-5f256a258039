'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { useTranslations, useLocale } from 'next-intl';
import { Spark<PERSON>, Star, Moon } from 'lucide-react';

export default function HeroSection() {
  const t = useTranslations('home');
  const locale = useLocale();

  return (
    <main className="relative z-10 flex flex-col items-center justify-center min-h-[80vh] px-6 text-center">
      <div className="max-w-4xl mx-auto">
        {/* Floating Icons */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            className="absolute top-20 left-20 text-purple-300"
            animate={{ 
              y: [0, -20, 0],
              rotate: [0, 10, 0]
            }}
            transition={{ 
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Sparkles size={24} />
          </motion.div>
          
          <motion.div
            className="absolute top-40 right-32 text-pink-300"
            animate={{ 
              y: [0, 15, 0],
              rotate: [0, -15, 0]
            }}
            transition={{ 
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          >
            <Star size={20} />
          </motion.div>
          
          <motion.div
            className="absolute bottom-40 left-32 text-indigo-300"
            animate={{ 
              y: [0, -10, 0],
              rotate: [0, 20, 0]
            }}
            transition={{ 
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          >
            <Moon size={28} />
          </motion.div>
        </div>

        {/* Hero Content */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <h1 className="text-6xl md:text-8xl font-bold text-white mb-6 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
            {t('title')}
          </h1>
        </motion.div>

        <motion.p
          className="text-xl md:text-2xl text-purple-200 mb-4"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.3 }}
        >
          {t('subtitle')}
        </motion.p>

        <motion.p
          className="text-lg text-purple-300 mb-12 max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.6 }}
        >
          {t('description')}
        </motion.p>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.9 }}
        >
          <Link
            href={`/${locale}/reading`}
            className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-full hover:from-purple-700 hover:to-pink-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl group"
          >
            <span className="mr-2">{t('startReading')}</span>
            <motion.span
              animate={{ x: [0, 5, 0] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              ✨
            </motion.span>
          </Link>
        </motion.div>

        {/* Features */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1.2 }}
        >
          <motion.div
            className="text-center"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-purple-600 rounded-full flex items-center justify-center">
              <span className="text-2xl">🔮</span>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {t('features.authentic')}
            </h3>
            <p className="text-purple-300">
              {locale === 'zh' ? '传统塔罗系统，完整78张牌组，专业塔罗牌占卜' : 'Traditional tarot system with complete 78-card deck for professional tarot reading'}
            </p>
          </motion.div>

          <motion.div
            className="text-center"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-pink-600 rounded-full flex items-center justify-center">
              <span className="text-2xl">🤖</span>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {t('features.aiPowered')}
            </h3>
            <p className="text-purple-300">
              {locale === 'zh' ? 'AI驱动的智能心理塔罗解读系统' : 'Intelligent psychic tarot interpretation system powered by AI'}
            </p>
          </motion.div>

          <motion.div
            className="text-center"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-indigo-600 rounded-full flex items-center justify-center">
              <span className="text-2xl">🌍</span>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {t('features.multilingual')}
            </h3>
            <p className="text-purple-300">
              {locale === 'zh' ? '支持中文和英文' : 'Available in English and Chinese'}
            </p>
          </motion.div>
        </motion.div>
      </div>
    </main>
  );
}
