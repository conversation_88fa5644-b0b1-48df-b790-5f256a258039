#!/usr/bin/env node

/**
 * Cloudflare 部署脚本
 * 自动化部署流程，包括数据库迁移和环境变量设置
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始 Cloudflare 部署流程...\n');

// 1. 检查环境
console.log('📋 检查部署环境...');
try {
  execSync('wrangler whoami', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ 请先登录 Cloudflare: wrangler login');
  process.exit(1);
}

// 2. 构建项目
console.log('\n🔨 构建项目...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ 项目构建成功');
} catch (error) {
  console.error('❌ 项目构建失败');
  process.exit(1);
}

// 3. 创建 D1 数据库（如果不存在）
console.log('\n🗄️  设置 D1 数据库...');
try {
  // 检查数据库是否存在
  const databases = execSync('wrangler d1 list', { encoding: 'utf8' });
  
  if (!databases.includes('mystic-tarot-db')) {
    console.log('创建生产数据库...');
    execSync('wrangler d1 create mystic-tarot-db', { stdio: 'inherit' });
    
    console.log('创建预览数据库...');
    execSync('wrangler d1 create mystic-tarot-db-preview', { stdio: 'inherit' });
    
    console.log('⚠️  请更新 wrangler.toml 中的 database_id');
  }
} catch (error) {
  console.log('数据库可能已存在，继续部署...');
}

// 4. 运行数据库迁移
console.log('\n📊 运行数据库迁移...');
try {
  // 生成 Prisma 客户端
  execSync('npx prisma generate', { stdio: 'inherit' });
  
  // 推送数据库架构到 D1
  console.log('推送数据库架构到生产环境...');
  execSync('wrangler d1 execute mystic-tarot-db --file=./prisma/migrations/schema.sql', { stdio: 'inherit' });
  
  console.log('推送数据库架构到预览环境...');
  execSync('wrangler d1 execute mystic-tarot-db-preview --file=./prisma/migrations/schema.sql', { stdio: 'inherit' });
  
  console.log('✅ 数据库迁移完成');
} catch (error) {
  console.log('⚠️  数据库迁移可能失败，请手动检查');
}

// 5. 部署到 Cloudflare Pages
console.log('\n🌐 部署到 Cloudflare Pages...');
try {
  execSync('wrangler pages deploy out --project-name=mystic-tarot', { stdio: 'inherit' });
  console.log('✅ 部署成功！');
} catch (error) {
  console.error('❌ 部署失败');
  process.exit(1);
}

console.log('\n🎉 部署完成！');
console.log('📝 请记得在 Cloudflare Pages 控制台设置以下环境变量：');
console.log('   - NEXTAUTH_SECRET');
console.log('   - GOOGLE_CLIENT_ID');
console.log('   - GOOGLE_CLIENT_SECRET');
console.log('   - GITHUB_ID');
console.log('   - GITHUB_SECRET');
console.log('   - OPENAI_API_KEY');
console.log('   - OPENAI_BASE_URL');
console.log('   - EMAIL_SERVER_HOST');
console.log('   - EMAIL_SERVER_PORT');
console.log('   - EMAIL_SERVER_USER');
console.log('   - EMAIL_SERVER_PASSWORD');
console.log('   - EMAIL_FROM');
console.log('   - CREEM_API_KEY');
console.log('   - CREEM_PRODUCT_ID');
console.log('   - NEXT_PUBLIC_CREEM_API_KEY');
console.log('   - NEXT_PUBLIC_CREEM_PRODUCT_ID');
