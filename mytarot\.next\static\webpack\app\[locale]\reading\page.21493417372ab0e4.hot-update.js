"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/components/TarotCard.tsx":
/*!**************************************!*\
  !*** ./src/components/TarotCard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst TarotCard = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.memo)(_c = function TarotCard(param) {\n    let { card, isRevealed = false, isSelected = false, isReversed = false, size = 'large', onClick, className = '', style, showBack = true } = param;\n    const cardBackImage = '/images/card-back.webp';\n    // 根据尺寸设置样式\n    const sizeClasses = {\n        small: 'w-12 h-18',\n        medium: 'w-16 h-24',\n        large: 'w-24 h-36'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"relative cursor-pointer \".concat(sizeClasses[size], \" \").concat(className),\n        style: style,\n        onClick: onClick,\n        whileHover: {\n            scale: 1.05,\n            y: -10\n        },\n        whileTap: {\n            scale: 0.95\n        },\n        initial: {\n            opacity: 0,\n            y: 50\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-full \".concat(isSelected ? 'ring-4 ring-purple-400 ring-opacity-75' : '', \" rounded-lg overflow-hidden shadow-lg\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"absolute inset-0 backface-hidden\",\n                    initial: false,\n                    animate: {\n                        rotateY: isRevealed ? 180 : 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    style: {\n                        backfaceVisibility: 'hidden'\n                    },\n                    children: [\n                        showBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: cardBackImage,\n                            alt: \"Mystic tarot card back design - Free AI tarot reading\",\n                            fill: true,\n                            className: \"object-cover\",\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this),\n                        !showBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-indigo-700 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white text-4xl\",\n                                children: \"\\uD83D\\uDD2E\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                card && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"absolute inset-0 backface-hidden\",\n                    initial: false,\n                    animate: {\n                        rotateY: isRevealed ? 0 : -180\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    style: {\n                        backfaceVisibility: 'hidden'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full h-full \".concat(isReversed ? 'rotate-180' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: card.imagePath,\n                                alt: \"\".concat(card.nameEn, \" tarot card - Free AI tarot reading and psychic interpretation\"),\n                                fill: true,\n                                className: \"object-cover\",\n                                sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\",\n                                onError: ()=>{\n                                    console.error('Image failed to load:', card.imagePath);\n                                    console.error('Card data:', card);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold text-sm\",\n                                    children: [\n                                        card.nameEn,\n                                        isReversed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-yellow-400\",\n                                            children: \"↓\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-xs\",\n                                    children: isReversed ? 'Reversed' : 'Upright'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n});\n_c1 = TarotCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TarotCard);\nvar _c, _c1;\n$RefreshReg$(_c, \"TarotCard$memo\");\n$RefreshReg$(_c1, \"TarotCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TarotCard.tsx\n"));

/***/ })

});