(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[854],{1595:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>P});var n=a(5155),s=a(2115),i=a(8274),r=a(825),l=a(2138),o=a(6501),c=a(760),d=a(6766);function m(e){let{card:t,isRevealed:a=!1,isSelected:s=!1,isReversed:r=!1,size:l="large",onClick:o,className:c="",style:m,showBack:h=!0}=e;return(0,n.jsx)(i.P.div,{className:"relative cursor-pointer ".concat({small:"w-12 h-18",medium:"w-16 h-24",large:"w-24 h-36"}[l]," ").concat(c),style:m,onClick:o,whileHover:{scale:1.05,y:-10},whileTap:{scale:.95},initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,n.jsxs)("div",{className:"relative w-full h-full ".concat(s?"ring-4 ring-purple-400 ring-opacity-75":""," rounded-lg overflow-hidden shadow-lg"),children:[(0,n.jsxs)(i.P.div,{className:"absolute inset-0 backface-hidden",initial:!1,animate:{rotateY:180*!!a},transition:{duration:.6},style:{backfaceVisibility:"hidden"},children:[h&&(0,n.jsx)(d.default,{src:"/images/card-back.webp",alt:"Mystic tarot card back design - Free AI tarot reading",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),!h&&(0,n.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-purple-600 to-indigo-700 flex items-center justify-center",children:(0,n.jsx)("div",{className:"text-white text-4xl",children:"\uD83D\uDD2E"})})]}),t&&(0,n.jsxs)(i.P.div,{className:"absolute inset-0 backface-hidden",initial:!1,animate:{rotateY:a?0:-180},transition:{duration:.6},style:{backfaceVisibility:"hidden"},children:[(0,n.jsx)("div",{className:"relative w-full h-full ".concat(r?"rotate-180":""),children:(0,n.jsx)(d.default,{src:t.imagePath,alt:"".concat(t.nameEn," tarot card - Free AI tarot reading and psychic interpretation"),fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",onError:()=>{}})}),(0,n.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3",children:[(0,n.jsxs)("h3",{className:"text-white font-semibold text-sm",children:[t.nameEn,r&&(0,n.jsx)("span",{className:"ml-2 text-yellow-400",children:"↓"})]}),(0,n.jsx)("p",{className:"text-gray-300 text-xs",children:r?"Reversed":"Upright"})]})]})]})})}var h=a(7652);function x(e){let{onComplete:t,isShuffling:a}=e,r=(0,h.c3)("reading"),[l,o]=(0,s.useState)(0),[d,x]=(0,s.useState)(!1),[u,p]=(0,s.useState)([]);return((0,s.useEffect)(()=>{p(Array.from({length:20},(e,t)=>({x:150+100*Math.sin(.5*t),y:200+100*Math.cos(.3*t)})))},[]),(0,s.useEffect)(()=>{if(a){x(!0),o(0);let e=setInterval(()=>{o(a=>a>=10?(clearInterval(e),setTimeout(()=>{x(!1),t()},1e3),a):a+1)},300);return()=>clearInterval(e)}},[a,t]),a)?(0,n.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(i.P.h2,{className:"text-3xl font-bold text-white mb-8",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},children:r("shuffling")}),(0,n.jsxs)("div",{className:"relative w-80 h-96 mx-auto",children:[(0,n.jsx)(c.N,{children:d&&Array.from({length:5}).map((e,t)=>(0,n.jsx)(i.P.div,{className:"absolute w-24 h-36",initial:{x:150,y:200,rotate:0,scale:1},animate:{x:[150,100+40*t,150],y:[200,150-20*t,200],rotate:[0,(t-2)*15,0],scale:[1,1.1,1]},transition:{duration:.6,repeat:1/0,delay:.1*t,ease:"easeInOut"},children:(0,n.jsx)(m,{showBack:!0,size:"large"})},t))}),(0,n.jsx)("div",{className:"absolute inset-0 pointer-events-none",children:u.map((e,t)=>(0,n.jsx)(i.P.div,{className:"absolute w-2 h-2 bg-purple-400 rounded-full",initial:{x:150,y:200,opacity:0},animate:{x:e.x,y:e.y,opacity:[0,1,0],scale:[0,1,0]},transition:{duration:1,repeat:1/0,delay:.1*t,ease:"easeOut"}},t))})]}),(0,n.jsxs)(i.P.div,{className:"mt-8 flex items-center justify-center gap-2",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:[(0,n.jsx)("div",{className:"flex gap-1",children:Array.from({length:3}).map((e,t)=>(0,n.jsx)(i.P.div,{className:"w-2 h-2 bg-purple-400 rounded-full",animate:{scale:[1,1.5,1],opacity:[.5,1,.5]},transition:{duration:1,repeat:1/0,delay:.2*t}},t))}),(0,n.jsx)("span",{className:"text-purple-200 ml-2",children:r("preparingCards")})]})]})}):null}let u=[{name:"愚者",nameEn:"The Fool",keywords:["新开始","冒险","纯真","自由"],keywordsEn:["New beginnings","Adventure","Innocence","Freedom"]},{name:"魔术师",nameEn:"The Magician",keywords:["意志力","创造力","技能","专注"],keywordsEn:["Willpower","Creativity","Skill","Focus"]},{name:"女祭司",nameEn:"The High Priestess",keywords:["直觉","神秘","潜意识","智慧"],keywordsEn:["Intuition","Mystery","Subconscious","Wisdom"]},{name:"皇后",nameEn:"The Empress",keywords:["丰饶","母性","创造力","自然"],keywordsEn:["Abundance","Motherhood","Creativity","Nature"]},{name:"皇帝",nameEn:"The Emperor",keywords:["权威","结构","控制","稳定"],keywordsEn:["Authority","Structure","Control","Stability"]},{name:"教皇",nameEn:"The Hierophant",keywords:["传统","精神指导","教育","信仰"],keywordsEn:["Tradition","Spiritual guidance","Education","Faith"]},{name:"恋人",nameEn:"The Lovers",keywords:["爱情","选择","和谐","关系"],keywordsEn:["Love","Choice","Harmony","Relationships"]},{name:"战车",nameEn:"The Chariot",keywords:["胜利","意志力","控制","决心"],keywordsEn:["Victory","Willpower","Control","Determination"]},{name:"力量",nameEn:"Strength",keywords:["勇气","耐心","自控","内在力量"],keywordsEn:["Courage","Patience","Self-control","Inner strength"]},{name:"隐者",nameEn:"The Hermit",keywords:["内省","寻找","指导","智慧"],keywordsEn:["Introspection","Seeking","Guidance","Wisdom"]},{name:"命运之轮",nameEn:"Wheel of Fortune",keywords:["命运","变化","循环","机会"],keywordsEn:["Destiny","Change","Cycles","Opportunity"]},{name:"正义",nameEn:"Justice",keywords:["公正","平衡","真理","法律"],keywordsEn:["Justice","Balance","Truth","Law"]},{name:"倒吊人",nameEn:"The Hanged Man",keywords:["牺牲","等待","新视角","放手"],keywordsEn:["Sacrifice","Waiting","New perspective","Letting go"]},{name:"死神",nameEn:"Death",keywords:["转变","结束","重生","释放"],keywordsEn:["Transformation","Endings","Rebirth","Release"]},{name:"节制",nameEn:"Temperance",keywords:["平衡","耐心","调和","治愈"],keywordsEn:["Balance","Patience","Moderation","Healing"]},{name:"恶魔",nameEn:"The Devil",keywords:["束缚","诱惑","物质主义","依赖"],keywordsEn:["Bondage","Temptation","Materialism","Addiction"]},{name:"塔",nameEn:"The Tower",keywords:["突然变化","破坏","启示","解放"],keywordsEn:["Sudden change","Destruction","Revelation","Liberation"]},{name:"星星",nameEn:"The Star",keywords:["希望","灵感","治愈","指导"],keywordsEn:["Hope","Inspiration","Healing","Guidance"]},{name:"月亮",nameEn:"The Moon",keywords:["幻象","恐惧","潜意识","直觉"],keywordsEn:["Illusion","Fear","Subconscious","Intuition"]},{name:"太阳",nameEn:"The Sun",keywords:["快乐","成功","活力","乐观"],keywordsEn:["Joy","Success","Vitality","Optimism"]},{name:"审判",nameEn:"Judgement",keywords:["重生","觉醒","宽恕","救赎"],keywordsEn:["Rebirth","Awakening","Forgiveness","Redemption"]},{name:"世界",nameEn:"The World",keywords:["完成","成就","旅程结束","满足"],keywordsEn:["Completion","Achievement","Journey's end","Fulfillment"]}],p=[{suit:"cups",name:"圣杯",nameEn:"Cups",element:"水",elementEn:"Water"},{suit:"pentacles",name:"金币",nameEn:"Pentacles",element:"土",elementEn:"Earth"},{suit:"swords",name:"宝剑",nameEn:"Swords",element:"风",elementEn:"Air"},{suit:"wands",name:"权杖",nameEn:"Wands",element:"火",elementEn:"Fire"}],y=[{num:1,name:"王牌",nameEn:"Ace"},{num:2,name:"二",nameEn:"Two"},{num:3,name:"三",nameEn:"Three"},{num:4,name:"四",nameEn:"Four"},{num:5,name:"五",nameEn:"Five"},{num:6,name:"六",nameEn:"Six"},{num:7,name:"七",nameEn:"Seven"},{num:8,name:"八",nameEn:"Eight"},{num:9,name:"九",nameEn:"Nine"},{num:10,name:"十",nameEn:"Ten"},{num:11,name:"侍从",nameEn:"Page"},{num:12,name:"骑士",nameEn:"Knight"},{num:13,name:"王后",nameEn:"Queen"},{num:14,name:"国王",nameEn:"King"}];function g(e){let{onCardsSelected:t}=e,[a,r]=(0,s.useState)([]),[l,o]=(0,s.useState)([]),[d,x]=(0,s.useState)([]),g=(0,h.c3)("reading");(0,s.useEffect)(()=>{let e=(()=>{let e=[];return u.forEach((t,a)=>{let n=a.toString().padStart(2,"0"),s=t.nameEn.toLowerCase().replace(/^the\s+/,"").replace(/\s+/g,"-").replace(/'/g,"");e.push({id:"major-".concat(n),name:t.name,nameEn:t.nameEn,type:"major",number:a,imagePath:"/images/major/".concat(n,"-").concat(s,".webp"),keywords:t.keywords,keywordsEn:t.keywordsEn,meaning:"".concat(t.name,"代表").concat(t.keywords.join("、"),"。"),meaningEn:"".concat(t.nameEn," represents ").concat(t.keywordsEn.join(", ").toLowerCase(),"."),reversedMeaning:"逆位的".concat(t.name,"可能表示这些品质的缺失或过度。"),reversedMeaningEn:"Reversed ".concat(t.nameEn," may indicate the absence or excess of these qualities.")})}),p.forEach(t=>{y.forEach((a,n)=>{let s,i=n.toString().padStart(2,"0");s=0===n?"ace":"".concat(a.nameEn.toLowerCase(),"-of-").concat(t.nameEn.toLowerCase()),e.push({id:"".concat(t.suit,"-").concat(i),name:"".concat(t.name).concat(a.name),nameEn:"".concat(a.nameEn," of ").concat(t.nameEn),type:"minor",suit:t.suit,number:a.num,imagePath:"/images/minor/".concat(t.suit,"/").concat(i,"-").concat(s,".webp"),keywords:["".concat(t.element,"元素"),"".concat(a.name,"的能量")],keywordsEn:["".concat(t.elementEn," element"),"".concat(a.nameEn," energy")],meaning:"".concat(t.name).concat(a.name,"代表").concat(t.element,"元素的").concat(a.name,"阶段能量。"),meaningEn:"".concat(a.nameEn," of ").concat(t.nameEn," represents the ").concat(a.nameEn.toLowerCase()," stage of ").concat(t.elementEn.toLowerCase()," element energy."),reversedMeaning:"逆位时可能表示这种能量的阻塞或过度。",reversedMeaningEn:"When reversed, it may indicate blockage or excess of this energy."})})}),e})();r(e),x(e.map((e,t)=>{let a=Math.floor(t/12),n=t%12*30+15*a,s=Math.min(25+8*a,35);return{x:Math.max(-35,Math.min(35,Math.cos(n*Math.PI/180)*s+(17*t%21-10)*.5)),y:Math.max(-25,Math.min(25,Math.sin(n*Math.PI/180)*s*.6+(13*t%21-10)*.5)),rotation:(23*t%60-30)*.7}}))},[]);let f=e=>l.some(t=>t.id===e.id);return(0,n.jsxs)("div",{className:"relative w-full min-h-screen overflow-hidden",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(147,51,234,0.1)_0%,transparent_70%)]"}),(0,n.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(59,130,246,0.1)_0%,transparent_50%)]"}),(0,n.jsx)("div",{className:"absolute top-4 md:top-8 left-1/2 transform -translate-x-1/2 z-20",children:(0,n.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-full px-4 md:px-6 py-2 md:py-3 border border-white/20",children:(0,n.jsxs)("p",{className:"text-white text-sm md:text-lg font-semibold",children:[g("selectedCards",{count:l.length})," / 3"]})})}),(0,n.jsx)("div",{className:"absolute top-16 md:top-20 left-1/2 transform -translate-x-1/2 z-20 flex gap-2 md:gap-4",children:[0,1,2].map(e=>(0,n.jsx)("div",{className:"w-12 h-18 md:w-16 md:h-24 rounded-lg border-2 border-dashed ".concat(l[e]?"border-purple-400 bg-purple-400/20":"border-purple-600/50 bg-purple-600/10"," flex items-center justify-center"),children:l[e]&&(0,n.jsx)(i.P.div,{initial:{scale:0,rotate:180},animate:{scale:1,rotate:0},className:"w-full h-full",children:(0,n.jsx)(m,{card:l[e],isRevealed:!1,size:"small"})})},e))}),(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,n.jsx)("div",{className:"relative w-full max-w-6xl h-full",children:(0,n.jsx)(c.N,{children:a.map((e,a)=>{let s=d[a];if(!s)return null;let r=f(e),c=l.length>=3&&!r;return(0,n.jsx)(i.P.div,{className:"absolute ".concat(r?"cursor-default":c?"cursor-not-allowed":"cursor-pointer"),style:{left:"".concat(50+s.x,"%"),top:"".concat(50+s.y,"%"),transform:"translate(-50%, -50%) rotate(".concat(s.rotation,"deg)"),zIndex:r?15:10-Math.floor(a/12)},initial:{scale:0,opacity:0,rotate:s.rotation+180},animate:{scale:r?1.1:c?.7:1,opacity:r?1:c?.3:1,rotate:s.rotation},transition:{duration:.8,delay:.02*a,type:"spring",stiffness:100},whileHover:c||r?{}:{scale:1.1,rotate:0,zIndex:20,y:-10},onClick:()=>!c&&!r&&(e=>{if(l.length>=3||f(e))return;let a=[...l,e];o(a),3===a.length&&setTimeout(()=>{t(a)},500)})(e),children:(0,n.jsx)(m,{card:e,isRevealed:!1,isSelected:r,size:"medium"})},e.id)})})})}),(0,n.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20",children:(0,n.jsxs)(i.P.div,{className:"bg-white/10 backdrop-blur-sm rounded-2xl px-8 py-4 text-center",initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{delay:2},children:[(0,n.jsxs)("p",{className:"text-white text-lg mb-2",children:[0===l.length&&g("selectFirstCard"),1===l.length&&g("selectSecondCard"),2===l.length&&g("selectThirdCard"),3===l.length&&g("cardsComplete")]}),(0,n.jsx)("p",{className:"text-purple-200 text-sm",children:g("selectCardsDescription")})]})})]})}var f=a(2486),w=a(3311),b=a(1154),j=a(7434),v=a(646),N=a(4357),E=a(133),k=a(2550);function z(e){let{selectedCards:t,onNewReading:a}=e,[r,l]=(0,s.useState)(""),[o,c]=(0,s.useState)(!1),[d,x]=(0,s.useState)([]),[u,p]=(0,s.useState)(-1),[y,g]=(0,s.useState)(!1),[z,S]=(0,s.useState)(""),[P,T]=(0,s.useState)(!1),[C,A]=(0,s.useState)(!1),[M,F]=(0,s.useState)(!1),R=(0,h.c3)("reading"),I=(0,k.Ym)(),{reading:H,isLoading:_,error:O,streamReading:D}=function(){let[e,t]=(0,s.useState)(""),[a,n]=(0,s.useState)(!1),[i,r]=(0,s.useState)(null);return{reading:e,isLoading:a,error:i,generateReading:async function(e,a){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en",i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];n(!0),r(null),t("");try{let n=await fetch("/api/reading",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({cards:e,question:a,locale:s,stream:i})});if(!n.ok){let e=await n.json();throw Error(e.error||"Failed to generate reading")}let r=await n.json();t(r.reading)}catch(e){r(e instanceof Error?e.message:"An error occurred")}finally{n(!1)}},streamReading:async function(e,a){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en";n(!0),r(null),t("");try{var i;let r=await fetch("/api/reading",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({cards:e,question:a,locale:s,stream:!0})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to generate reading")}let l=null==(i=r.body)?void 0:i.getReader();if(!l)throw Error("Failed to get response reader");let o=new TextDecoder,c="";for(;;){let{done:e,value:a}=await l.read();if(e)break;let s=(c+=o.decode(a,{stream:!0})).split("\n");for(let e of(c=s.pop()||"",s))if(e.startsWith("data: ")){let a=e.slice(6);if("[DONE]"===a)return void n(!1);try{let e=JSON.parse(a);e.content&&t(t=>t+e.content)}catch(e){}}}}catch(e){r(e instanceof Error?e.message:"An error occurred")}finally{n(!1)}}}}();(0,s.useEffect)(()=>{x(t.map((e,t)=>(e.id.split("").reduce((e,t)=>e+t.charCodeAt(0),0)+t)%2==0))},[t]);let W=async()=>{if(!r.trim())return void alert("zh"===I?"请先输入您的问题":"Please enter your question first");A(!0);try{let e=t.map((e,t)=>({...e,isReversed:d[t]})),a=await fetch("/api/prompt",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({cards:e,question:r,locale:I})});if(!a.ok)throw Error("Failed to generate prompt");let n=await a.json();S(n.prompt),T(!0)}catch(e){alert("zh"===I?"生成提示词失败，请重试":"Failed to generate prompt, please try again")}finally{A(!1)}},L=async()=>{try{await navigator.clipboard.writeText(z),F(!0),setTimeout(()=>F(!1),2e3)}catch(e){alert("zh"===I?"复制失败，请手动复制":"Copy failed, please copy manually")}},J=async()=>{if(!r.trim())return void alert("zh"===I?"请先输入您的问题":"Please enter your question first");let e=t.map((e,t)=>({...e,isReversed:d[t]}));g(!0),await D(e,r,I)},Y=()=>{if(!r.trim())return void alert("zh"===I?"请先输入您的问题":"Please enter your question first");c(!0),t.forEach((e,t)=>{setTimeout(()=>{p(t)},800*t)})},q=[{title:"zh"===I?"过去":"Past",subtitle:"zh"===I?"来自过去的影响":"What influences you from the past"},{title:"zh"===I?"现在":"Present",subtitle:"zh"===I?"您当前的状况":"Your current situation"},{title:"zh"===I?"未来":"Future",subtitle:"zh"===I?"未来的展望":"What the future holds"}];return(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 py-12",children:(0,n.jsx)("div",{className:"container mx-auto px-6",children:(0,n.jsxs)(i.P.div,{className:"max-w-6xl mx-auto",initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,n.jsxs)("div",{className:"text-center mb-12",children:[(0,n.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-white mb-4",children:"zh"===I?"您的塔罗占卜":"Your Tarot Reading"}),(0,n.jsx)("p",{className:"text-xl text-purple-200",children:"zh"===I?"三张牌为您指引道路":"Three cards to guide your path"})]}),!o&&(0,n.jsxs)(i.P.div,{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-12",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,n.jsx)("h2",{className:"text-2xl font-semibold text-white mb-4 text-center",children:R("questionPrompt")}),(0,n.jsxs)("div",{className:"flex gap-4",children:[(0,n.jsx)("input",{type:"text",value:r,onChange:e=>l(e.target.value),placeholder:R("questionPlaceholder"),className:"flex-1 px-6 py-4 bg-white/20 border border-purple-300/30 rounded-full text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent",onKeyDown:e=>"Enter"===e.key&&Y()}),(0,n.jsxs)(i.P.button,{onClick:Y,className:"px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center gap-2",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,n.jsx)(f.A,{size:20}),R("reveal")]})]})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:t.map((e,t)=>(0,n.jsxs)(i.P.div,{className:"text-center",initial:{opacity:0,y:100},animate:{opacity:1,y:0},transition:{delay:.5+.2*t},children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:q[t].title}),(0,n.jsx)("p",{className:"text-purple-200 text-sm",children:q[t].subtitle})]}),(0,n.jsx)("div",{className:"flex justify-center mb-6",children:(0,n.jsx)(i.P.div,{className:"relative",animate:{scale:u>=t?1.1:1},transition:{duration:.8},children:(0,n.jsx)(m,{card:e,isRevealed:o&&u>=t,isReversed:d[t],size:"large"})})}),o&&u>=t&&(0,n.jsxs)(i.P.div,{className:"bg-white/10 backdrop-blur-sm rounded-xl p-6",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,n.jsxs)("h4",{className:"text-xl font-semibold text-white mb-2",children:["zh"===I?e.name:e.nameEn,d[t]&&(0,n.jsxs)("span",{className:"text-purple-300 text-sm ml-2",children:["(",R("reversed"),")"]})]}),(0,n.jsx)("p",{className:"text-purple-200 text-sm mb-3",children:"zh"===I?e.keywords.join(", "):e.keywordsEn.join(", ")}),(0,n.jsx)("p",{className:"text-white text-sm leading-relaxed",children:d[t]?"zh"===I?e.reversedMeaning:e.reversedMeaningEn:"zh"===I?e.meaning:e.meaningEn})]})]},e.id))}),o&&r&&(0,n.jsxs)(i.P.div,{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8 text-center",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:2.5},children:[(0,n.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center justify-center gap-2",children:[(0,n.jsx)(w.A,{size:24}),R("yourQuestion")]}),(0,n.jsxs)("p",{className:"text-purple-200 text-lg italic",children:['"',r,'"']})]}),o&&u>=2&&!P&&(0,n.jsx)(i.P.div,{className:"text-center mb-8",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:3},children:(0,n.jsx)(i.P.button,{onClick:W,disabled:C,className:"px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-full hover:from-green-700 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center gap-2 mx-auto disabled:opacity-50 disabled:cursor-not-allowed",whileHover:{scale:C?1:1.05},whileTap:{scale:C?1:.95},children:C?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(b.A,{size:20,className:"animate-spin"}),"zh"===I?"生成中...":"Generating..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(j.A,{size:20}),"zh"===I?"生成提示词":"Generate Prompt"]})})}),P&&z&&(0,n.jsxs)(i.P.div,{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsxs)("h3",{className:"text-2xl font-semibold text-white flex items-center gap-2",children:[(0,n.jsx)(j.A,{size:28}),"zh"===I?"塔罗牌提示词":"Tarot Prompt"]}),(0,n.jsx)(i.P.button,{onClick:L,className:"px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2 ".concat(M?"bg-green-600 text-white":"bg-white/20 text-white hover:bg-white/30"),whileHover:{scale:1.05},whileTap:{scale:.95},children:M?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(v.A,{size:16}),"zh"===I?"已复制":"Copied"]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N.A,{size:16}),"zh"===I?"复制":"Copy"]})})]}),(0,n.jsx)("div",{className:"bg-black/30 rounded-lg p-4 mb-4",children:(0,n.jsx)("pre",{className:"text-purple-100 text-sm leading-relaxed whitespace-pre-wrap font-mono",children:z})}),(0,n.jsx)("div",{className:"text-center text-purple-200 text-sm",children:"zh"===I?"您可以复制此提示词，在其他AI工具中使用以获得塔罗牌解读":"You can copy this prompt and use it with other AI tools to get tarot readings"})]}),P&&!y&&(0,n.jsxs)(i.P.div,{className:"text-center mb-8",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,n.jsx)(i.P.button,{onClick:J,disabled:_,className:"px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-full hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center gap-2 mx-auto disabled:opacity-50 disabled:cursor-not-allowed",whileHover:{scale:_?1:1.05},whileTap:{scale:_?1:.95},children:_?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(b.A,{size:20,className:"animate-spin"}),"zh"===I?"解读中...":"Reading..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(w.A,{size:20}),"zh"===I?"获取AI解读":"Get AI Reading"]})}),(0,n.jsx)("div",{className:"mt-4 text-center",children:(0,n.jsx)("p",{className:"text-purple-200 text-sm max-w-2xl mx-auto",children:"zh"===I?"\uD83D\uDCA1 该服务完全免费，但可能不稳定或较慢。您可以复制上方提示词使用其他大模型工具进行解答。":"\uD83D\uDCA1 This service is completely free, but may be unstable or slow. You can copy the prompt above and use other AI tools for readings."})})]}),y&&(0,n.jsxs)(i.P.div,{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,n.jsxs)("h3",{className:"text-2xl font-semibold text-white mb-6 text-center flex items-center justify-center gap-2",children:[(0,n.jsx)(w.A,{size:28}),R("aiTarotReading")]}),O?(0,n.jsx)("div",{className:"border rounded-lg p-4 mb-4 bg-red-500/20 border-red-500/30",children:(0,n.jsxs)("p",{className:"text-red-200",children:["zh"===I?"错误：":"Error:"," ",O]})}):(0,n.jsxs)("div",{className:"prose prose-invert max-w-none",children:[(0,n.jsx)("div",{className:"text-purple-100 leading-relaxed whitespace-pre-wrap",children:H||_&&("zh"===I?"正在咨询宇宙智慧...":"Consulting the cosmic wisdom...")}),_&&(0,n.jsx)("div",{className:"flex items-center justify-center mt-4",children:(0,n.jsx)(b.A,{size:24,className:"animate-spin text-purple-400"})})]})]}),(0,n.jsx)("div",{className:"text-center",children:(0,n.jsxs)(i.P.button,{onClick:a,className:"px-6 py-3 bg-white/20 text-white font-semibold rounded-full hover:bg-white/30 transition-all duration-300 flex items-center gap-2 mx-auto",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,n.jsx)(E.A,{size:20}),"zh"===I?"新的占卜":"New Reading"]})})]})})})}var S=a(1275);function P(){let e=(0,h.c3)("reading"),[t,a]=(0,s.useState)(!1),[c,d]=(0,s.useState)(!1),[m,u]=(0,s.useState)([]),[p,y]=(0,s.useState)(!1),[f,w]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{w(!0)},[]),f)?(0,n.jsxs)("div",{className:"min-h-screen relative overflow-hidden bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900",children:[(0,n.jsx)(S.A,{}),!p&&(0,n.jsx)(o.A,{}),(0,n.jsx)(x,{isShuffling:t,onComplete:()=>{a(!1),d(!0)}}),p?(0,n.jsx)(z,{selectedCards:m,onNewReading:()=>{a(!1),d(!1),u([]),y(!1)}}):(0,n.jsx)("main",{className:"container mx-auto px-6 py-12",children:c||p?c?(0,n.jsx)(g,{onCardsSelected:e=>{u(e),d(!1),y(!0)}}):p?(0,n.jsxs)(i.P.div,{className:"text-center",initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,n.jsx)("h2",{className:"text-3xl font-bold text-white mb-8",children:"Your Reading"}),(0,n.jsx)("p",{className:"text-purple-200 mb-8",children:"Here are your selected cards and their meanings..."}),(0,n.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-8",children:[(0,n.jsx)("p",{className:"text-white",children:"结果显示界面即将实现..."}),(0,n.jsx)("div",{className:"flex justify-center gap-4 mt-4",children:m.map((e,t)=>(0,n.jsx)("div",{className:"text-white",children:(0,n.jsxs)("p",{children:[t+1,". ",e.name]})},e.id))})]})]}):null:(0,n.jsxs)(i.P.div,{className:"max-w-2xl mx-auto text-center",initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,n.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-white mb-6",children:e("title")}),(0,n.jsx)("p",{className:"text-xl text-purple-200 mb-8",children:e("description")}),(0,n.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8",children:[(0,n.jsx)("h2",{className:"text-2xl font-semibold text-white mb-4",children:e("instructions.title")}),(0,n.jsxs)("div",{className:"space-y-4 text-left",children:[(0,n.jsxs)("div",{className:"flex items-start gap-3",children:[(0,n.jsx)("div",{className:"w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold mt-1",children:"1"}),(0,n.jsx)("p",{className:"text-purple-100",children:e("instructions.step1")})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3",children:[(0,n.jsx)("div",{className:"w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold mt-1",children:"2"}),(0,n.jsx)("p",{className:"text-purple-100",children:e("instructions.step2")})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3",children:[(0,n.jsx)("div",{className:"w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold mt-1",children:"3"}),(0,n.jsx)("p",{className:"text-purple-100",children:e("instructions.step3")})]})]})]}),(0,n.jsxs)(i.P.button,{onClick:()=>{a(!0)},className:"inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-full hover:from-purple-700 hover:to-pink-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl",whileHover:{scale:1.05},whileTap:{scale:.95},disabled:t,children:[(0,n.jsx)(r.A,{size:24}),e("startShuffle"),(0,n.jsx)(l.A,{size:20})]})]})})]}):(0,n.jsxs)("div",{className:"min-h-screen relative overflow-hidden bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:[(0,n.jsx)(S.A,{}),(0,n.jsx)("div",{className:"text-white text-xl relative z-10",children:"Loading..."})]})}},3798:(e,t,a)=>{Promise.resolve().then(a.bind(a,1595))}},e=>{e.O(0,[550,566,898,935,441,964,358],()=>e(e.s=3798)),_N_E=e.O()}]);