"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/reading/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/[locale]/reading/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReadingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shuffle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shuffle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shuffle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shuffle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shuffle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navigation */ \"(app-pages-browser)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_ShuffleAnimation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ShuffleAnimation */ \"(app-pages-browser)/./src/components/ShuffleAnimation.tsx\");\n/* harmony import */ var _components_CardSelection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CardSelection */ \"(app-pages-browser)/./src/components/CardSelection.tsx\");\n/* harmony import */ var _components_CardResults__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CardResults */ \"(app-pages-browser)/./src/components/CardResults.tsx\");\n/* harmony import */ var _components_StarryBackground__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/StarryBackground */ \"(app-pages-browser)/./src/components/StarryBackground.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ReadingPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)('reading');\n    const [isShuffling, setIsShuffling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCardSelection, setShowCardSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCards, setSelectedCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showResults, setShowResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 防止hydration错误\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReadingPage.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"ReadingPage.useEffect\"], []);\n    const handleStartShuffle = ()=>{\n        setIsShuffling(true);\n    };\n    const handleShuffleComplete = ()=>{\n        setIsShuffling(false);\n        setShowCardSelection(true);\n    };\n    const handleCardsSelected = (cards)=>{\n        setSelectedCards(cards);\n        setShowCardSelection(false);\n        setShowResults(true);\n    };\n    const handleNewReading = ()=>{\n        setIsShuffling(false);\n        setShowCardSelection(false);\n        setSelectedCards([]);\n        setShowResults(false);\n    };\n    // 防止hydration错误，在客户端挂载前显示加载状态\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen relative overflow-hidden bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StarryBackground__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-white text-xl relative z-10\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StarryBackground__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            !showResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 24\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShuffleAnimation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isShuffling: isShuffling,\n                onComplete: handleShuffleComplete\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            showResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CardResults__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                selectedCards: selectedCards,\n                onNewReading: handleNewReading\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-6 py-12\",\n                children: !showCardSelection && !showResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    className: \"max-w-2xl mx-auto text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-white mb-6\",\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-purple-200 mb-8\",\n                            children: t('description')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-white mb-4\",\n                                    children: t('instructions.title')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold mt-1\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-100\",\n                                                    children: t('instructions.step1')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold mt-1\",\n                                                    children: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-100\",\n                                                    children: t('instructions.step2')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold mt-1\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-100\",\n                                                    children: t('instructions.step3')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                            onClick: handleStartShuffle,\n                            className: \"inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-full hover:from-purple-700 hover:to-pink-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            disabled: isShuffling,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shuffle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                t('startShuffle'),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shuffle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this) : showCardSelection ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CardSelection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    onCardsSelected: handleCardsSelected\n                }, void 0, false, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this) : showResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    className: \"text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white mb-8\",\n                            children: \"Your Reading\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-purple-200 mb-8\",\n                            children: \"Here are your selected cards and their meanings...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white\",\n                                    children: \"结果显示界面即将实现...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-4 mt-4\",\n                                    children: selectedCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    index + 1,\n                                                    \". \",\n                                                    card.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, card.id, false, {\n                                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\reading\\\\page.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(ReadingPage, \"3SCrz/RtR/7W8FOh/14w1f4Up5I=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations\n    ];\n});\n_c = ReadingPage;\nvar _c;\n$RefreshReg$(_c, \"ReadingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/reading/page.tsx\n"));

/***/ })

});