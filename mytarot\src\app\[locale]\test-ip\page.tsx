'use client';

import { useState } from 'react';
import { LanguageProvider, useLanguage } from '@/contexts/LanguageContext';

function TestIPContent() {
  const { language, isLoading, ipInfo } = useLanguage();
  const [testResult, setTestResult] = useState<any>(null);
  const [testing, setTesting] = useState(false);

  const testBackendAPI = async () => {
    setTesting(true);
    try {
      const response = await fetch('/api/detect-location');
      const result = await response.json();
      setTestResult(result);
    } catch (error) {
      setTestResult({ error: error.message });
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-white mb-8 text-center">
          IP检测测试页面
        </h1>

        {/* 当前语言检测结果 */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-6">
          <h2 className="text-2xl font-semibold text-white mb-4">
            自动语言检测结果
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-black/20 rounded-lg p-4">
              <h3 className="text-lg font-medium text-purple-200 mb-2">检测状态</h3>
              <p className="text-white">
                {isLoading ? '🔄 检测中...' : '✅ 检测完成'}
              </p>
            </div>

            <div className="bg-black/20 rounded-lg p-4">
              <h3 className="text-lg font-medium text-purple-200 mb-2">当前语言</h3>
              <p className="text-white text-xl">
                {language === 'zh' ? '🇨🇳 中文' : '🌐 English'}
              </p>
            </div>
          </div>

          {ipInfo && (
            <div className="mt-4 bg-black/20 rounded-lg p-4">
              <h3 className="text-lg font-medium text-purple-200 mb-2">IP信息</h3>
              <div className="text-white space-y-1">
                <p><strong>IP地址:</strong> {ipInfo.ip}</p>
                <p><strong>国家:</strong> {ipInfo.country}</p>
                <p><strong>国家代码:</strong> {ipInfo.countryCode}</p>
              </div>
            </div>
          )}
        </div>

        {/* 手动测试后端API */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-6">
          <h2 className="text-2xl font-semibold text-white mb-4">
            手动测试后端API
          </h2>
          
          <button
            onClick={testBackendAPI}
            disabled={testing}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 disabled:opacity-50"
          >
            {testing ? '测试中...' : '测试 /api/detect-location'}
          </button>

          {testResult && (
            <div className="mt-4 bg-black/20 rounded-lg p-4">
              <h3 className="text-lg font-medium text-purple-200 mb-2">API响应</h3>
              <pre className="text-white text-sm overflow-auto">
                {JSON.stringify(testResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* 浏览器信息 */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
          <h2 className="text-2xl font-semibold text-white mb-4">
            浏览器信息
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-black/20 rounded-lg p-4">
              <h3 className="text-lg font-medium text-purple-200 mb-2">语言设置</h3>
              <p className="text-white">
                {navigator.language}
              </p>
            </div>

            <div className="bg-black/20 rounded-lg p-4">
              <h3 className="text-lg font-medium text-purple-200 mb-2">支持的语言</h3>
              <p className="text-white text-sm">
                {navigator.languages?.join(', ')}
              </p>
            </div>

            <div className="bg-black/20 rounded-lg p-4">
              <h3 className="text-lg font-medium text-purple-200 mb-2">用户代理</h3>
              <p className="text-white text-xs break-all">
                {navigator.userAgent}
              </p>
            </div>

            <div className="bg-black/20 rounded-lg p-4">
              <h3 className="text-lg font-medium text-purple-200 mb-2">时区</h3>
              <p className="text-white">
                {Intl.DateTimeFormat().resolvedOptions().timeZone}
              </p>
            </div>
          </div>
        </div>

        <div className="text-center mt-8">
          <a
            href="/reading"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-full hover:from-green-700 hover:to-emerald-700 transition-all duration-300"
          >
            返回塔罗占卜
          </a>
        </div>
      </div>
    </div>
  );
}

export default function TestIPPage() {
  return (
    <LanguageProvider>
      <TestIPContent />
    </LanguageProvider>
  );
}
