"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/hooks/useIPLanguage.ts":
/*!************************************!*\
  !*** ./src/hooks/useIPLanguage.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIPLanguage: () => (/* binding */ useIPLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useIPLanguage auto */ \nfunction useIPLanguage() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('en');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [ipInfo, setIpInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useIPLanguage.useEffect\": ()=>{\n            const detectLanguageFromIP = {\n                \"useIPLanguage.useEffect.detectLanguageFromIP\": async ()=>{\n                    try {\n                        // 优先使用我们自己的API（避免跨域问题）\n                        try {\n                            console.log('使用后端API检测地理位置...');\n                            const response = await fetch('/api/detect-location', {\n                                signal: AbortSignal.timeout(8000),\n                                headers: {\n                                    'Accept': 'application/json'\n                                }\n                            });\n                            if (response.ok) {\n                                const result = await response.json();\n                                console.log('后端API响应:', result);\n                                if (result.success && result.data) {\n                                    const { country, countryCode, ip } = result.data;\n                                    const info = {\n                                        country: country,\n                                        countryCode: countryCode,\n                                        ip: ip\n                                    };\n                                    setIpInfo(info);\n                                    // 判断是否为中国\n                                    const isChinese = country === '中国' || country === 'China' || countryCode === 'CN';\n                                    if (isChinese) {\n                                        setLanguage('zh');\n                                        console.log('🇨🇳 检测到中国IP，切换到中文');\n                                    } else {\n                                        setLanguage('en');\n                                        console.log('🌐 检测到海外IP，使用英文');\n                                    }\n                                    setIsLoading(false);\n                                    return; // 成功检测，直接返回\n                                }\n                            }\n                        } catch (error) {\n                            console.warn('后端API检测失败:', error);\n                        }\n                        // 后端API失败，尝试前端直接调用\n                        console.log('尝试前端直接IP检测...');\n                        const apis = [\n                            {\n                                url: 'https://ipapi.co/json/',\n                                parseCountry: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>data.country_code\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            },\n                            {\n                                url: 'https://ip-api.com/json/',\n                                parseCountry: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>data.countryCode\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            },\n                            {\n                                url: 'https://ipinfo.io/json',\n                                parseCountry: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>data.country\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            }\n                        ];\n                        let detected = false;\n                        for (const api of apis){\n                            try {\n                                console.log(\"尝试IP检测服务: \".concat(api.url));\n                                const response = await fetch(api.url, {\n                                    signal: AbortSignal.timeout(5000),\n                                    headers: {\n                                        'Accept': 'application/json'\n                                    }\n                                });\n                                if (!response.ok) {\n                                    console.warn(\"服务 \".concat(api.url, \" 返回错误: \").concat(response.status));\n                                    continue;\n                                }\n                                const data = await response.json();\n                                console.log(\"\".concat(api.url, \" 响应:\"), data);\n                                // 使用对应的解析函数获取国家代码\n                                const countryCode = api.parseCountry(data);\n                                if (countryCode && data.ip) {\n                                    const info = {\n                                        country: countryCode === 'CN' ? '中国' : data.country_name || data.country || countryCode,\n                                        countryCode: countryCode,\n                                        ip: data.ip\n                                    };\n                                    setIpInfo(info);\n                                    // 判断是否为中国\n                                    if (countryCode === 'CN') {\n                                        setLanguage('zh');\n                                        console.log('🇨🇳 检测到中国IP，切换到中文');\n                                    } else {\n                                        setLanguage('en');\n                                        console.log('🌐 检测到海外IP，使用英文');\n                                    }\n                                    detected = true;\n                                    break;\n                                }\n                            } catch (error) {\n                                console.warn(\"IP检测服务 \".concat(api.url, \" 失败:\"), error);\n                                continue;\n                            }\n                        }\n                        // 如果所有IP检测服务都失败，使用浏览器语言\n                        if (!detected) {\n                            var _navigator_languages;\n                            console.log('所有IP检测服务都失败，使用浏览器语言检测');\n                            const browserLang = navigator.language || ((_navigator_languages = navigator.languages) === null || _navigator_languages === void 0 ? void 0 : _navigator_languages[0]) || 'en';\n                            if (browserLang.startsWith('zh')) {\n                                setLanguage('zh');\n                                console.log('🌐 根据浏览器语言使用中文');\n                            } else {\n                                setLanguage('en');\n                                console.log('🌐 根据浏览器语言使用英文');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('语言检测失败:', error);\n                        // 默认使用英文\n                        setLanguage('en');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useIPLanguage.useEffect.detectLanguageFromIP\"];\n            detectLanguageFromIP();\n        }\n    }[\"useIPLanguage.useEffect\"], []);\n    return {\n        language,\n        isLoading,\n        ipInfo,\n        setLanguage\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useIPLanguage.ts\n"));

/***/ })

});