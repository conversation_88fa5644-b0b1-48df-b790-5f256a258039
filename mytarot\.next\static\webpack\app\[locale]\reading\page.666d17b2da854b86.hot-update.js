"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/hooks/useIPLanguage.ts":
/*!************************************!*\
  !*** ./src/hooks/useIPLanguage.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIPLanguage: () => (/* binding */ useIPLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useIPLanguage auto */ \nfunction useIPLanguage() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('en');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [ipInfo, setIpInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useIPLanguage.useEffect\": ()=>{\n            const detectLanguageFromIP = {\n                \"useIPLanguage.useEffect.detectLanguageFromIP\": async ()=>{\n                    try {\n                        // 尝试多个支持CORS的IP检测服务\n                        const ipServices = [\n                            {\n                                url: 'https://ipapi.co/json/',\n                                parser: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>({\n                                            ip: data.ip,\n                                            country: data.country_name,\n                                            countryCode: data.country_code\n                                        })\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            },\n                            {\n                                url: 'https://api.ipgeolocation.io/ipgeo?apiKey=free',\n                                parser: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>({\n                                            ip: data.ip,\n                                            country: data.country_name,\n                                            countryCode: data.country_code2\n                                        })\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            },\n                            {\n                                url: 'https://ipinfo.io/json',\n                                parser: {\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP\": (data)=>({\n                                            ip: data.ip,\n                                            country: data.country === 'CN' ? '中国' : data.country,\n                                            countryCode: data.country\n                                        })\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP\"]\n                            }\n                        ];\n                        let detected = false;\n                        for (const service of ipServices){\n                            try {\n                                console.log(\"尝试IP检测服务: \".concat(service.url));\n                                const controller = new AbortController();\n                                const timeoutId = setTimeout({\n                                    \"useIPLanguage.useEffect.detectLanguageFromIP.timeoutId\": ()=>controller.abort()\n                                }[\"useIPLanguage.useEffect.detectLanguageFromIP.timeoutId\"], 5000);\n                                const response = await fetch(service.url, {\n                                    signal: controller.signal,\n                                    headers: {\n                                        'Accept': 'application/json'\n                                    }\n                                });\n                                clearTimeout(timeoutId);\n                                if (!response.ok) {\n                                    console.warn(\"服务 \".concat(service.url, \" 返回错误: \").concat(response.status));\n                                    continue;\n                                }\n                                const data = await response.json();\n                                console.log(\"\".concat(service.url, \" 响应:\"), data);\n                                const parsed = service.parser(data);\n                                if (parsed.ip && parsed.country) {\n                                    const info = {\n                                        country: parsed.country,\n                                        countryCode: parsed.countryCode || '',\n                                        ip: parsed.ip\n                                    };\n                                    setIpInfo(info);\n                                    // 判断是否为中国\n                                    const isChinese = parsed.country === '中国' || parsed.country === 'China' || parsed.countryCode === 'CN';\n                                    if (isChinese) {\n                                        setLanguage('zh');\n                                        console.log('🇨🇳 检测到中国IP，切换到中文');\n                                    } else {\n                                        setLanguage('en');\n                                        console.log('🌐 检测到海外IP，使用英文');\n                                    }\n                                    detected = true;\n                                    break;\n                                }\n                            } catch (error) {\n                                console.warn(\"IP检测服务 \".concat(service.url, \" 失败:\"), error);\n                                continue;\n                            }\n                        }\n                        // 如果所有IP检测服务都失败，使用浏览器语言\n                        if (!detected) {\n                            var _navigator_languages;\n                            console.log('所有IP检测服务都失败，使用浏览器语言检测');\n                            const browserLang = navigator.language || ((_navigator_languages = navigator.languages) === null || _navigator_languages === void 0 ? void 0 : _navigator_languages[0]) || 'en';\n                            if (browserLang.startsWith('zh')) {\n                                setLanguage('zh');\n                                console.log('🌐 根据浏览器语言使用中文');\n                            } else {\n                                setLanguage('en');\n                                console.log('🌐 根据浏览器语言使用英文');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('语言检测失败:', error);\n                        // 默认使用英文\n                        setLanguage('en');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useIPLanguage.useEffect.detectLanguageFromIP\"];\n            detectLanguageFromIP();\n        }\n    }[\"useIPLanguage.useEffect\"], []);\n    return {\n        language,\n        isLoading,\n        ipInfo,\n        setLanguage\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useIPLanguage.ts\n"));

/***/ })

});