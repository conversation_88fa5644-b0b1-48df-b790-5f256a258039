"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[566],{845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(2115).createContext)({})},1508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2082:(t,e,i)=>{i.d(e,{xQ:()=>s});var n=i(2115),r=i(845);function s(t=!0){let e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,n.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},2664:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isLocalURL",{enumerable:!0,get:function(){return s}});let n=i(9991),r=i(7102);function s(t){if(!(0,n.isAbsoluteUrl)(t))return!0;try{let e=(0,n.getLocationOrigin)(),i=new URL(t,e);return i.origin===e&&(0,r.hasBasePath)(i.pathname)}catch(t){return!1}}},2757:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{formatUrl:function(){return s},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let n=i(6966)._(i(8859)),r=/https?|ftp|gopher|file/;function s(t){let{auth:e,hostname:i}=t,s=t.protocol||"",o=t.pathname||"",a=t.hash||"",l=t.query||"",u=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?u=e+t.host:i&&(u=e+(~i.indexOf(":")?"["+i+"]":i),t.port&&(u+=":"+t.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let h=t.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),t.slashes||(!s||r.test(s))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),h&&"?"!==h[0]&&(h="?"+h),""+s+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return s(t)}},2885:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(2115);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},3180:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"errorOnce",{enumerable:!0,get:function(){return i}});let i=t=>{}},3311:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]])},4869:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5695:(t,e,i)=>{var n=i(8999);i.o(n,"usePathname")&&i.d(e,{usePathname:function(){return n.usePathname}}),i.o(n,"useRouter")&&i.d(e,{useRouter:function(){return n.useRouter}})},6654:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=i(2115);function r(t,e){let i=(0,n.useRef)(null),r=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let t=i.current;t&&(i.current=null,t());let e=r.current;e&&(r.current=null,e())}else t&&(i.current=s(t,n)),e&&(r.current=s(e,n))},[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6874:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return g},useLinkStatus:function(){return v}});let n=i(6966),r=i(5155),s=n._(i(2115)),o=i(2757),a=i(5227),l=i(9818),u=i(6654),h=i(9991),d=i(5929);i(3230);let c=i(4930),p=i(2664),m=i(6634);function f(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}function g(t){let e,i,n,[o,g]=(0,s.useOptimistic)(c.IDLE_LINK_STATUS),v=(0,s.useRef)(null),{href:x,as:T,children:w,prefetch:b=null,passHref:P,replace:S,shallow:A,scroll:M,onClick:E,onMouseEnter:C,onTouchStart:V,legacyBehavior:k=!1,onNavigate:D,ref:R,unstable_dynamicOnHover:j,...L}=t;e=w,k&&("string"==typeof e||"number"==typeof e)&&(e=(0,r.jsx)("a",{children:e}));let F=s.default.useContext(a.AppRouterContext),O=!1!==b,B=null===b||"auto"===b?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:I,as:U}=s.default.useMemo(()=>{let t=f(x);return{href:t,as:T?f(T):t}},[x,T]);k&&(i=s.default.Children.only(e));let N=k?i&&"object"==typeof i&&i.ref:R,W=s.default.useCallback(t=>(null!==F&&(v.current=(0,c.mountLinkInstance)(t,I,F,B,O,g)),()=>{v.current&&((0,c.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,c.unmountPrefetchableInstance)(t)}),[O,I,F,B,g]),$={ref:(0,u.useMergedRef)(W,N),onClick(t){k||"function"!=typeof E||E(t),k&&i.props&&"function"==typeof i.props.onClick&&i.props.onClick(t),F&&(t.defaultPrevented||function(t,e,i,n,r,o,a){let{nodeName:l}=t.currentTarget;if(!("A"===l.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t)||t.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(e)){r&&(t.preventDefault(),location.replace(e));return}if(t.preventDefault(),a){let t=!1;if(a({preventDefault:()=>{t=!0}}),t)return}s.default.startTransition(()=>{(0,m.dispatchNavigateAction)(i||e,r?"replace":"push",null==o||o,n.current)})}}(t,I,U,v,S,M,D))},onMouseEnter(t){k||"function"!=typeof C||C(t),k&&i.props&&"function"==typeof i.props.onMouseEnter&&i.props.onMouseEnter(t),F&&O&&(0,c.onNavigationIntent)(t.currentTarget,!0===j)},onTouchStart:function(t){k||"function"!=typeof V||V(t),k&&i.props&&"function"==typeof i.props.onTouchStart&&i.props.onTouchStart(t),F&&O&&(0,c.onNavigationIntent)(t.currentTarget,!0===j)}};return(0,h.isAbsoluteUrl)(U)?$.href=U:k&&!P&&("a"!==i.type||"href"in i.props)||($.href=(0,d.addBasePath)(U)),n=k?s.default.cloneElement(i,$):(0,r.jsx)("a",{...L,...$,children:e}),(0,r.jsx)(y.Provider,{value:o,children:n})}i(3180);let y=(0,s.createContext)(c.IDLE_LINK_STATUS),v=()=>(0,s.useContext)(y);("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6983:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},7351:(t,e,i)=>{i.d(e,{s:()=>r});var n=i(6983);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},7494:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(2115);let r=i(8972).B?n.useLayoutEffect:n.useEffect},7652:(t,e,i)=>{i.d(e,{c3:()=>s});var n=i(2550);function r(t,e){return(...t)=>{try{return e(...t)}catch{throw Error(void 0)}}}let s=r(0,n.c3);r(0,n.kc)},8274:(t,e,i)=>{let n;i.d(e,{P:()=>sm});var r=i(2115);let s=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(s),a=t=>180*t/Math.PI,l=t=>h(a(Math.atan2(t[1],t[0]))),u={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:l,rotateZ:l,skewX:t=>a(Math.atan(t[1])),skewY:t=>a(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},h=t=>((t%=360)<0&&(t+=360),t),d=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),c=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),p={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:d,scaleY:c,scale:t=>(d(t)+c(t))/2,rotateX:t=>h(a(Math.atan2(t[6],t[5]))),rotateY:t=>h(a(Math.atan2(-t[2],t[0]))),rotateZ:l,rotate:l,skewX:t=>a(Math.atan(t[4])),skewY:t=>a(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function m(t){return+!!t.includes("scale")}function f(t,e){let i,n;if(!t||"none"===t)return m(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=p,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=u,n=e}if(!n)return m(e);let s=i[e],o=n[1].split(",").map(g);return"function"==typeof s?s(o):o[s]}function g(t){return parseFloat(t.trim())}let y=t=>e=>"string"==typeof e&&e.startsWith(t),v=y("--"),x=y("var(--"),T=t=>!!x(t)&&w.test(t.split("/*")[0].trim()),w=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function b({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}let P=(t,e,i)=>t+(e-t)*i;function S(t){return void 0===t||1===t}function A({scale:t,scaleX:e,scaleY:i}){return!S(t)||!S(e)||!S(i)}function M(t){return A(t)||E(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function E(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function C(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function V(t,e=0,i=1,n,r){t.min=C(t.min,e,i,n,r),t.max=C(t.max,e,i,n,r)}function k(t,{x:e,y:i}){V(t.x,e.translate,e.scale,e.originPoint),V(t.y,i.translate,i.scale,i.originPoint)}function D(t,e){t.min=t.min+e,t.max=t.max+e}function R(t,e,i,n,r=.5){let s=P(t.min,t.max,r);V(t,e,i,s,n)}function j(t,e){R(t.x,e.x,e.scaleX,e.scale,e.originX),R(t.y,e.y,e.scaleY,e.scale,e.originY)}function L(t,e){return b(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let F=new Set(["width","height","top","left","right","bottom",...s]),O=(t,e,i)=>i>e?e:i<t?t:i,B={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},I={...B,transform:t=>O(0,1,t)},U={...B,default:1},N=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),W=N("deg"),$=N("%"),_=N("px"),z=N("vh"),Y=N("vw"),K={...$,parse:t=>$.parse(t)/100,transform:t=>$.transform(100*t)},X=t=>e=>e.test(t),H=[B,_,$,W,Y,z,{test:t=>"auto"===t,parse:t=>t}],q=t=>H.find(X(t)),G=()=>{},Z=()=>{},Q=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),J=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tt=t=>t===B||t===_,te=new Set(["x","y","z"]),ti=s.filter(t=>!te.has(t)),tn={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>f(e,"x"),y:(t,{transform:e})=>f(e,"y")};tn.translateX=tn.x,tn.translateY=tn.y;let tr=t=>t,ts={},to=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ta={value:null,addProjectionMetrics:null};function tl(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=to.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&ta.value&&ta.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:d,preRender:c,render:p,postRender:m}=o,f=()=>{let s=ts.useManualTiming?r.timestamp:performance.now();i=!1,ts.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),h.process(r),d.process(r),c.process(r),p.process(r),m.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(f))};return{schedule:to.reduce((e,s)=>{let a=o[s];return e[s]=(e,s=!1,o=!1)=>(!i&&(i=!0,n=!0,r.isProcessing||t(f)),a.schedule(e,s,o)),e},{}),cancel:t=>{for(let e=0;e<to.length;e++)o[to[e]].cancel(t)},state:r,steps:o}}let{schedule:tu,cancel:th,state:td,steps:tc}=tl("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:tr,!0),tp=new Set,tm=!1,tf=!1,tg=!1;function ty(){if(tf){let t=Array.from(tp).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ti.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tf=!1,tm=!1,tp.forEach(t=>t.complete(tg)),tp.clear()}function tv(){tp.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tf=!0)})}class tx{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(tp.add(this),tm||(tm=!0,tu.read(tv),tu.resolveKeyframes(ty))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tp.delete(this)}cancel(){"scheduled"===this.state&&(tp.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tT=t=>/^0[^.\s]+$/u.test(t),tw=t=>Math.round(1e5*t)/1e5,tb=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tP=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tS=(t,e)=>i=>!!("string"==typeof i&&tP.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tA=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(tb);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tM={...B,transform:t=>Math.round(O(0,255,t))},tE={test:tS("rgb","red"),parse:tA("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tM.transform(t)+", "+tM.transform(e)+", "+tM.transform(i)+", "+tw(I.transform(n))+")"},tC={test:tS("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tE.transform},tV={test:tS("hsl","hue"),parse:tA("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+$.transform(tw(e))+", "+$.transform(tw(i))+", "+tw(I.transform(n))+")"},tk={test:t=>tE.test(t)||tC.test(t)||tV.test(t),parse:t=>tE.test(t)?tE.parse(t):tV.test(t)?tV.parse(t):tC.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tE.transform(t):tV.transform(t),getAnimatableNone:t=>{let e=tk.parse(t);return e.alpha=0,tk.transform(e)}},tD=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tR="number",tj="color",tL=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tF(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tL,t=>(tk.test(t)?(n.color.push(s),r.push(tj),i.push(tk.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tR),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tO(t){return tF(t).values}function tB(t){let{split:e,types:i}=tF(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tR?r+=tw(t[s]):e===tj?r+=tk.transform(t[s]):r+=t[s]}return r}}let tI=t=>"number"==typeof t?0:tk.test(t)?tk.getAnimatableNone(t):t,tU={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tb)?.length||0)+(t.match(tD)?.length||0)>0},parse:tO,createTransformer:tB,getAnimatableNone:function(t){let e=tO(t);return tB(t)(e.map(tI))}},tN=new Set(["brightness","contrast","saturate","opacity"]);function tW(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(tb)||[];if(!n)return t;let r=i.replace(n,""),s=+!!tN.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let t$=/\b([a-z-]*)\(.*?\)/gu,t_={...tU,getAnimatableNone:t=>{let e=t.match(t$);return e?e.map(tW).join(" "):t}},tz={...B,transform:Math.round},tY={borderWidth:_,borderTopWidth:_,borderRightWidth:_,borderBottomWidth:_,borderLeftWidth:_,borderRadius:_,radius:_,borderTopLeftRadius:_,borderTopRightRadius:_,borderBottomRightRadius:_,borderBottomLeftRadius:_,width:_,maxWidth:_,height:_,maxHeight:_,top:_,right:_,bottom:_,left:_,padding:_,paddingTop:_,paddingRight:_,paddingBottom:_,paddingLeft:_,margin:_,marginTop:_,marginRight:_,marginBottom:_,marginLeft:_,backgroundPositionX:_,backgroundPositionY:_,rotate:W,rotateX:W,rotateY:W,rotateZ:W,scale:U,scaleX:U,scaleY:U,scaleZ:U,skew:W,skewX:W,skewY:W,distance:_,translateX:_,translateY:_,translateZ:_,x:_,y:_,z:_,perspective:_,transformPerspective:_,opacity:I,originX:K,originY:K,originZ:_,zIndex:tz,fillOpacity:I,strokeOpacity:I,numOctaves:tz},tK={...tY,color:tk,backgroundColor:tk,outlineColor:tk,fill:tk,stroke:tk,borderColor:tk,borderTopColor:tk,borderRightColor:tk,borderBottomColor:tk,borderLeftColor:tk,filter:t_,WebkitFilter:t_},tX=t=>tK[t];function tH(t,e){let i=tX(t);return i!==t_&&(i=tU),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tq=new Set(["auto","none","0"]);class tG extends tx{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&T(n=n.trim())){let r=function t(e,i,n=1){Z(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[r,s]=function(t){let e=J.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return Q(t)?parseFloat(t):t}return T(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!F.has(i)||2!==t.length)return;let[n,r]=t,s=q(n),o=q(r);if(s!==o)if(tt(s)&&tt(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tn[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||tT(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!tq.has(e)&&tF(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=tH(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tn[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=tn[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let tZ=t=>!!(t&&t.getVelocity);function tQ(){n=void 0}let tJ={now:()=>(void 0===n&&tJ.set(td.isProcessing||ts.useManualTiming?td.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(tQ)}};function t0(t,e){-1===t.indexOf(e)&&t.push(e)}function t1(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class t5{constructor(){this.subscriptions=[]}add(t){return t0(this.subscriptions,t),()=>t1(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let t2={current:void 0};class t3{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{let e=tJ.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=tJ.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new t5);let i=this.events[t].add(e);return"change"===t?()=>{i(),tu.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return t2.current&&t2.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=tJ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function t9(t,e){return new t3(t,e)}let t4=[...H,tk,tU],{schedule:t6}=tl(queueMicrotask,!1),t8={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},t7={};for(let t in t8)t7[t]={isEnabled:e=>t8[t].some(t=>!!e[t])};let et=()=>({translate:0,scale:1,origin:0,originPoint:0}),ee=()=>({x:et(),y:et()}),ei=()=>({min:0,max:0}),en=()=>({x:ei(),y:ei()});var er=i(8972);let es={current:null},eo={current:!1},ea=new WeakMap;function el(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function eu(t){return"string"==typeof t||Array.isArray(t)}let eh=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ed=["initial",...eh];function ec(t){return el(t.animate)||ed.some(e=>eu(t[e]))}function ep(t){return!!(ec(t)||t.variants)}function em(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function ef(t,e,i,n){if("function"==typeof e){let[r,s]=em(n);e=e(void 0!==i?i:t.custom,r,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,s]=em(n);e=e(void 0!==i?i:t.custom,r,s)}return e}let eg=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ey{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tx,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tJ.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,tu.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=ec(e),this.isVariantNode=ep(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&tZ(e)&&e.set(a[t])}}mount(t){this.current=t,ea.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),eo.current||function(){if(eo.current=!0,er.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>es.current=t.matches;t.addEventListener("change",e),e()}else es.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||es.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),th(this.notifyUpdate),th(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=o.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tu.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in t7){let e=t7[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):en()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<eg.length;e++){let i=eg[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(tZ(r))t.addValue(n,r);else if(tZ(s))t.addValue(n,t9(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,t9(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=t9(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=i){if("string"==typeof i&&(Q(i)||tT(i)))i=parseFloat(i);else{let n;n=i,!t4.find(X(n))&&tU.test(e)&&(i=tH(t,e))}this.setBaseTarget(t,tZ(i)?i.get():i)}return tZ(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=ef(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||tZ(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new t5),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){t6.render(this.render)}}class ev extends ey{constructor(){super(...arguments),this.KeyframeResolver=tG}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tZ(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let ex=(t,e)=>e&&"number"==typeof t?e.transform(t):t,eT={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ew=s.length;function eb(t,e,i){let{style:n,vars:r,transformOrigin:a}=t,l=!1,u=!1;for(let t in e){let i=e[t];if(o.has(t)){l=!0;continue}if(v(t)){r[t]=i;continue}{let e=ex(i,tY[t]);t.startsWith("origin")?(u=!0,a[t]=e):n[t]=e}}if(!e.transform&&(l||i?n.transform=function(t,e,i){let n="",r=!0;for(let o=0;o<ew;o++){let a=s[o],l=t[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||i){let t=ex(l,tY[a]);if(!u){r=!1;let e=eT[a]||a;n+=`${e}(${t}) `}i&&(e[a]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;n.transformOrigin=`${t} ${e} ${i}`}}function eP(t,{style:e,vars:i},n,r){let s,o=t.style;for(s in e)o[s]=e[s];for(s in r?.applyProjectionStyles(o,n),i)o.setProperty(s,i[s])}let eS={};function eA(t,{layout:e,layoutId:i}){return o.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eS[t]||"opacity"===t)}function eM(t,e,i){let{style:n}=t,r={};for(let s in n)(tZ(n[s])||e.style&&tZ(e.style[s])||eA(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}class eE extends ev{constructor(){super(...arguments),this.type="html",this.renderInstance=eP}readValueFromInstance(t,e){if(o.has(e))return this.projection?.isProjecting?m(e):((t,e)=>{let{transform:i="none"}=getComputedStyle(t);return f(i,e)})(t,e);{let i=window.getComputedStyle(t),n=(v(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return L(t,e)}build(t,e,i){eb(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return eM(t,e,i)}}let eC=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eV={offset:"stroke-dashoffset",array:"stroke-dasharray"},ek={offset:"strokeDashoffset",array:"strokeDasharray"};function eD(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(eb(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?eV:ek;t[s.offset]=_.transform(-n);let o=_.transform(e),a=_.transform(i);t[s.array]=`${o} ${a}`}(d,r,s,o,!1)}let eR=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),ej=t=>"string"==typeof t&&"svg"===t.toLowerCase();function eL(t,e,i){let n=eM(t,e,i);for(let i in t)(tZ(t[i])||tZ(e[i]))&&(n[-1!==s.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}class eF extends ev{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=en}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(o.has(e)){let t=tX(e);return t&&t.default||0}return e=eR.has(e)?e:eC(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return eL(t,e,i)}build(t,e,i){eD(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in eP(t,e,void 0,n),e.attrs)t.setAttribute(eR.has(i)?i:eC(i),e.attrs[i])}mount(t){this.isSVGTag=ej(t.tagName),super.mount(t)}}let eO=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function eB(t){if("string"!=typeof t||t.includes("-"));else if(eO.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var eI=i(5155),eU=i(869);let eN=(0,r.createContext)({strict:!1});var eW=i(1508);let e$=(0,r.createContext)({});function e_(t){return Array.isArray(t)?t.join(" "):t}let ez=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eY(t,e,i){for(let n in e)tZ(e[n])||eA(n,i)||(t[n]=e[n])}let eK=()=>({...ez(),attrs:{}}),eX=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eH(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||eX.has(t)}let eq=t=>!eH(t);try{!function(t){"function"==typeof t&&(eq=e=>e.startsWith("on")?!eH(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}var eG=i(845),eZ=i(2885);function eQ(t){return tZ(t)?t.get():t}let eJ=t=>(e,i)=>{let n=(0,r.useContext)(e$),s=(0,r.useContext)(eG.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,r){return{latestValues:function(t,e,i,n){let r={},s=n(t,{});for(let t in s)r[t]=eQ(s[t]);let{initial:o,animate:a}=t,l=ec(t),u=ep(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!i&&!1===i.initial,d=(h=h||!1===o)?a:o;if(d&&"boolean"!=typeof d&&!el(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let n=ef(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,n,r,t),renderState:e()}})(t,e,n,s);return i?o():(0,eZ.M)(o)},e0=eJ({scrapeMotionValuesFromProps:eM,createRenderState:ez}),e1=eJ({scrapeMotionValuesFromProps:eL,createRenderState:eK}),e5=Symbol.for("motionComponentSymbol");function e2(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let e3="data-"+eC("framerAppearId"),e9=(0,r.createContext)({});var e4=i(7494);function e6(t){var e,i;let{forwardMotionProps:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;s&&function(t){for(let e in t)t7[e]={...t7[e],...t[e]}}(s);let a=eB(t)?e1:e0;function l(e,i){var s;let l,u={...(0,r.useContext)(eW.Q),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,r.useContext)(eU.L).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:h}=u,d=function(t){let{initial:e,animate:i}=function(t,e){if(ec(t)){let{initial:e,animate:i}=t;return{initial:!1===e||eu(e)?e:void 0,animate:eu(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,r.useContext)(e$));return(0,r.useMemo)(()=>({initial:e,animate:i}),[e_(e),e_(i)])}(e),c=a(e,h);if(!h&&er.B){(0,r.useContext)(eN).strict;let e=function(t){let{drag:e,layout:i}=t7;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(u);l=e.MeasureLayout,d.visualElement=function(t,e,i,n,s){let{visualElement:o}=(0,r.useContext)(e$),a=(0,r.useContext)(eN),l=(0,r.useContext)(eG.t),u=(0,r.useContext)(eW.Q).reducedMotion,h=(0,r.useRef)(null);n=n||a.renderer,!h.current&&n&&(h.current=n(t,{visualState:e,parent:o,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let d=h.current,c=(0,r.useContext)(e9);d&&!d.projection&&s&&("html"===d.type||"svg"===d.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&e2(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,i,s,c);let p=(0,r.useRef)(!1);(0,r.useInsertionEffect)(()=>{d&&p.current&&d.update(i,l)});let m=i[e3],f=(0,r.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return(0,e4.E)(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,r.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1),d.enteringChildren=void 0)}),d}(t,c,u,o,e.ProjectionNode)}return(0,eI.jsxs)(e$.Provider,{value:d,children:[l&&d.visualElement?(0,eI.jsx)(l,{visualElement:d.visualElement,...u}):null,function(t,e,i,{latestValues:n},s,o=!1){let a=(eB(t)?function(t,e,i,n){let s=(0,r.useMemo)(()=>{let i=eK();return eD(i,e,ej(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};eY(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return eY(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,r.useMemo)(()=>{let i=ez();return eb(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(e,n,s,t),l=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(eq(r)||!0===i&&eH(r)||!e&&!eH(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(e,"string"==typeof t,o),u=t!==r.Fragment?{...l,...a,ref:i}:{},{children:h}=e,d=(0,r.useMemo)(()=>tZ(h)?h.get():h,[h]);return(0,r.createElement)(t,{...u,children:d})}(t,e,(s=d.visualElement,(0,r.useCallback)(t=>{t&&c.onMount&&c.onMount(t),s&&(t?s.mount(t):s.unmount()),i&&("function"==typeof i?i(t):e2(i)&&(i.current=t))},[s])),c,h,n)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(i=null!=(e=t.displayName)?e:t.name)?i:"",")"));let u=(0,r.forwardRef)(l);return u[e5]=t,u}function e8(t,e,i){let n=t.getProps();return ef(n,e,void 0!==i?i:n.custom,t)}function e7(t,e){return t?.[e]??t?.default??t}let it=t=>Array.isArray(t);function ie(t,e){let i=t.getValue("willChange");if(tZ(i)&&i.add)return i.add(e);if(!i&&ts.WillChange){let i=new ts.WillChange("auto");t.addValue("willChange",i),i.add(e)}}function ii(t){t.duration=0,t.type}let ir=(t,e)=>i=>e(t(i)),is=(...t)=>t.reduce(ir),io=t=>1e3*t,ia={layout:0,mainThread:0,waapi:0};function il(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function iu(t,e){return i=>i>0?e:t}let ih=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},id=[tC,tE,tV];function ic(t){let e=id.find(e=>e.test(t));if(G(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tV&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=il(a,n,t+1/3),s=il(a,n,t),o=il(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let ip=(t,e)=>{let i=ic(t),n=ic(e);if(!i||!n)return iu(t,e);let r={...i};return t=>(r.red=ih(i.red,n.red,t),r.green=ih(i.green,n.green,t),r.blue=ih(i.blue,n.blue,t),r.alpha=P(i.alpha,n.alpha,t),tE.transform(r))},im=new Set(["none","hidden"]);function ig(t,e){return i=>P(t,e,i)}function iy(t){return"number"==typeof t?ig:"string"==typeof t?T(t)?iu:tk.test(t)?ip:iT:Array.isArray(t)?iv:"object"==typeof t?tk.test(t)?ip:ix:iu}function iv(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>iy(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function ix(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=iy(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let iT=(t,e)=>{let i=tU.createTransformer(e),n=tF(t),r=tF(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?im.has(t)&&!r.values.length||im.has(e)&&!n.values.length?function(t,e){return im.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):is(iv(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):(G(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),iu(t,e))};function iw(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?P(t,e,i):iy(t)(t,e)}let ib=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>tu.update(e,t),stop:()=>th(e),now:()=>td.isProcessing?td.timestamp:tJ.now()}},iP=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function iS(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function iA(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let iM={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iE(t,e){return t*Math.sqrt(1-e*e)}let iC=["duration","bounce"],iV=["stiffness","damping","mass"];function ik(t,e){return e.some(e=>void 0!==t[e])}function iD(t=iM.visualDuration,e=iM.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:iM.velocity,stiffness:iM.stiffness,damping:iM.damping,mass:iM.mass,isResolvedFromDuration:!1,...t};if(!ik(t,iV)&&ik(t,iC))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*O(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:iM.mass,stiffness:n,damping:r}}else{let i=function({duration:t=iM.duration,bounce:e=iM.bounce,velocity:i=iM.velocity,mass:n=iM.mass}){let r,s;G(t<=io(iM.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-e;o=O(iM.minDamping,iM.maxDamping,o),t=O(iM.minDuration,iM.maxDuration,t/1e3),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/iE(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=iE(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=io(t),isNaN(a))return{stiffness:iM.stiffness,damping:iM.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:iM.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-((n.velocity||0)/1e3)}),f=p||0,g=h/(2*Math.sqrt(u*d)),y=a-o,v=Math.sqrt(u/d)/1e3,x=5>Math.abs(y);if(r||(r=x?iM.restSpeed.granular:iM.restSpeed.default),s||(s=x?iM.restDelta.granular:iM.restDelta.default),g<1){let t=iE(v,g);i=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return a-i*((f+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let T={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0===t?f:0;g<1&&(n=0===t?io(f):iA(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(iS(T),2e4),e=iP(e=>T.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return T}function iR({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=i*e,g=p+f,y=void 0===o?g:o(g);y!==g&&(f=y-p);let v=t=>-f*Math.exp(-t/n),x=t=>y+v(t),T=t=>{let e=v(t),i=x(t);m.done=Math.abs(e)<=u,m.value=m.done?y:i},w=t=>{let e;if(e=m.value,void 0!==a&&e<a||void 0!==l&&e>l){var i;d=t,c=iD({keyframes:[m.value,(i=m.value,void 0===a?l:void 0===l||Math.abs(a-i)<Math.abs(l-i)?a:l)],velocity:iA(x,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h})}};return w(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),w(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}iD.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(iS(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:r/1e3}}(t,100,iD);return t.ease=e.ease,t.duration=io(e.duration),t.type="keyframes",t};let ij=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function iL(t,e,i,n){return t===e&&i===n?tr:r=>0===r||1===r?r:ij(function(t,e,i,n,r){let s,o,a=0;do(s=ij(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o}(r,0,1,t,i),e,n)}let iF=iL(.42,0,1,1),iO=iL(0,0,.58,1),iB=iL(.42,0,.58,1),iI=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,iU=t=>e=>1-t(1-e),iN=iL(.33,1.53,.69,.99),iW=iU(iN),i$=iI(iW),i_=t=>(t*=2)<1?.5*iW(t):.5*(2-Math.pow(2,-10*(t-1))),iz=t=>1-Math.sin(Math.acos(t)),iY=iU(iz),iK=iI(iz),iX=t=>Array.isArray(t)&&"number"==typeof t[0],iH={linear:tr,easeIn:iF,easeInOut:iB,easeOut:iO,circIn:iz,circInOut:iK,circOut:iY,backIn:iW,backInOut:i$,backOut:iN,anticipate:i_},iq=t=>{if(iX(t)){Z(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,n,r]=t;return iL(e,i,n,r)}return"string"==typeof t?(Z(void 0!==iH[t],`Invalid easing type '${t}'`,"invalid-easing-type"),iH[t]):t},iG=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function iZ({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let s=Array.isArray(n)&&"number"!=typeof n[0]?n.map(iq):iq(n),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(Z(s===e.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||ts.mix||iw,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=is(Array.isArray(e)?e[i]||tr:e,s)),n.push(s)}return n}(e,n,r),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=iG(t[n],t[n+1],i);return a[n](r)};return i?e=>u(O(t[0],t[s-1],e)):u}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=iG(0,e,n);t.push(P(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||iB).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let iQ=t=>null!==t;function iJ(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(iQ),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let i0={decay:iR,inertia:iR,tween:iZ,keyframes:iZ,spring:iD};function i1(t){"string"==typeof t.type&&(t.type=i0[t.type])}class i5{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let i2=t=>t/100;class i3 extends i5{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==tJ.now()&&this.tick(tJ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},ia.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;i1(t);let{type:e=iZ,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||iZ;a!==iZ&&"number"!=typeof o[0]&&(this.mixKeyframes=is(i2,iw(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=iS(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(x=s)),v=O(0,1,i)*o}let T=y?{done:!1,value:u[0]}:x.next(v);r&&(T.value=r(T.value));let{done:w}=T;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return b&&p!==iR&&(T.value=iJ(u,this.options,f,this.speed)),m&&m(T.value),b&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(t){t=io(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(tJ.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:t=ib,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(tJ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,ia.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function i9(t){let e;return()=>(void 0===e&&(e=t()),e)}let i4=i9(()=>void 0!==window.ScrollTimeline),i6={},i8=function(t,e){let i=i9(t);return()=>i6[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),i7=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,nt={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:i7([0,.65,.55,1]),circOut:i7([.55,0,1,.45]),backIn:i7([.31,.01,.66,-.59]),backOut:i7([.33,1.53,.69,.99])};function ne(t){return"function"==typeof t&&"applyToOptions"in t}class ni extends i5{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,Z("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return ne(t)&&i8()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?i8()?iP(e,i):"ease-out":iX(e)?i7(e):Array.isArray(e)?e.map(e=>t(e,i)||nt.easeOut):nt[e]}(a,r);Array.isArray(d)&&(h.easing=d),ta.value&&ia.waapi++;let c={delay:n,duration:r,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(c.pseudoElement=u);let p=t.animate(h,c);return ta.value&&p.finished.finally(()=>{ia.waapi--}),p}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=iJ(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e.startsWith("--")?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(t){this.finishedTime=null,this.animation.currentTime=io(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&i4())?(this.animation.timeline=t,tr):e(this)}}let nn={anticipate:i_,backInOut:i$,circInOut:iK};class nr extends ni{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in nn&&(t.ease=nn[t.ease])}(t),i1(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new i3({...s,autoplay:!1}),a=io(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let ns=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tU.test(t)||"0"===t)&&!t.startsWith("url(")),no=new Set(["opacity","clipPath","filter","transform"]),na=i9(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class nl extends i5{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=tJ.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||tx;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=tJ.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=ns(r,e),a=ns(s,e);return G(o===a,`You are trying to animate ${e} from "${r}" to "${s}". "${o?s:r}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||ne(i))&&n)}(t,r,s,o)&&((ts.instantAnimations||!a)&&u?.(iJ(t,i,e)),t[0]=t[t.length-1],ii(i),i.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return na()&&i&&no.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(h)?new nr({...h,element:h.motionValue.owner.current}):new i3(h);d.finished.then(()=>this.notifyFinished()).catch(tr),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tg=!0,tv(),ty(),tg=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let nu=t=>null!==t,nh={type:"spring",stiffness:500,damping:25,restSpeed:10},nd={type:"keyframes",duration:.8},nc={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},np=(t,e,i,n={},r,s)=>a=>{let l=e7(n,t)||{},u=l.delay||n.delay||0,{elapsed:h=0}=n;h-=io(u);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-h,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(l)&&Object.assign(d,((t,{keyframes:e})=>e.length>2?nd:o.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:nh:nc)(t,d)),d.duration&&(d.duration=io(d.duration)),d.repeatDelay&&(d.repeatDelay=io(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let c=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(ii(d),0===d.delay&&(c=!0)),(ts.instantAnimations||ts.skipAnimations)&&(c=!0,ii(d),d.delay=0),d.allowFlatten=!l.type&&!l.ease,c&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(nu),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(d.keyframes,l);if(void 0!==t)return void tu.update(()=>{d.onUpdate(t),d.onComplete()})}return l.isSync?new i3(d):new nl(d)};function nm(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=e;n&&(s=n);let l=[],u=r&&t.animationState&&t.animationState.getState()[r];for(let e in a){let n=t.getValue(e,t.latestValues[e]??null),r=a[e];if(void 0===r||u&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(u,e))continue;let o={delay:i,...e7(s||{},e)},h=n.get();if(void 0!==h&&!n.isAnimating&&!Array.isArray(r)&&r===h&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[e3];if(i){let t=window.MotionHandoffAnimation(i,e,tu);null!==t&&(o.startTime=t,d=!0)}}ie(t,e),n.start(np(e,n,r,t.shouldReduceMotion&&F.has(e)?{type:!1}:o,t,d));let c=n.animation;c&&l.push(c)}return o&&Promise.all(l).then(()=>{tu.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=e8(t,e)||{};for(let e in r={...r,...i}){var s;let i=it(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,t9(i))}}(t,o)})}),l}function nf(t,e,i,n=0,r=1){let s=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),o=t.size,a=(o-1)*n;return"function"==typeof i?i(s,o):1===r?s*n:a-s*n}function ng(t,e,i={}){let n=e8(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(nm(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=0,s=1,o){let a=[];for(let l of t.variantChildren)l.notify("AnimationStart",e),a.push(ng(l,e,{...o,delay:i+("function"==typeof n?0:n)+nf(t.variantChildren,l,n,r,s)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(a)}(t,e,n,s,o,a,i)}:()=>Promise.resolve(),{when:a}=r;if(!a)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function ny(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}let nv=ed.length,nx=[...eh].reverse(),nT=eh.length;function nw(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nb(){return{animate:nw(!0),whileInView:nw(),whileHover:nw(),whileTap:nw(),whileDrag:nw(),whileFocus:nw(),exit:nw()}}class nP{constructor(t){this.isMounted=!1,this.node=t}update(){}}class nS extends nP{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>ng(t,e,i)));else if("string"==typeof e)n=ng(t,e,i);else{let r="function"==typeof e?e8(t,e,i.custom):e;n=Promise.all(nm(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=nb(),n=!0,r=e=>(i,n)=>{let r=e8(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function s(s){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<nv;t++){let n=ed[t],r=e.props[n];(eu(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},l=[],u=new Set,h={},d=1/0;for(let e=0;e<nT;e++){var c,p;let m=nx[e],f=i[m],g=void 0!==o[m]?o[m]:a[m],y=eu(g),v=m===s?f.isActive:null;!1===v&&(d=e);let x=g===a[m]&&g!==o[m]&&y;if(x&&n&&t.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...h},!f.isActive&&null===v||!g&&!f.prevProp||el(g)||"boolean"==typeof g)continue;let T=(c=f.prevProp,"string"==typeof(p=g)?p!==c:!!Array.isArray(p)&&!ny(p,c)),w=T||m===s&&f.isActive&&!x&&y||e>d&&y,b=!1,P=Array.isArray(g)?g:[g],S=P.reduce(r(m),{});!1===v&&(S={});let{prevResolvedValues:A={}}=f,M={...A,...S},E=e=>{w=!0,u.has(e)&&(b=!0,u.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=S[t],i=A[t];if(!h.hasOwnProperty(t))(it(e)&&it(i)?ny(e,i):e===i)?void 0!==e&&u.has(t)?E(t):f.protectedKeys[t]=!0:null!=e?E(t):u.add(t)}f.prevProp=g,f.prevResolvedValues=S,f.isActive&&(h={...h,...S}),n&&t.blockInitialAnimation&&(w=!1);let C=x&&T,V=!C||b;w&&V&&l.push(...P.map(e=>{let i={type:m};if("string"==typeof e&&n&&!C&&t.manuallyAnimateOnMount&&t.parent){let{parent:n}=t,r=e8(n,e);if(n.enteringChildren&&r){let{delayChildren:e}=r.transition||{};i.delay=nf(n.enteringChildren,t,e)}}return{animation:e,options:i}}))}if(u.size){let e={};if("boolean"!=typeof o.initial){let i=e8(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}u.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),l.push({animation:e})}let m=!!l.length;return n&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(m=!1),n=!1,m?e(l):Promise.resolve()}return{animateChanges:s,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=s(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=nb(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();el(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nA=0;class nM extends nP{constructor(){super(...arguments),this.id=nA++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let nE={x:!1,y:!1};function nC(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let nV=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function nk(t){return{point:{x:t.pageX,y:t.pageY}}}function nD(t,e,i,n){return nC(t,e,t=>nV(t)&&i(t,nk(t)),n)}function nR(t){return t.max-t.min}function nj(t,e,i,n=.5){t.origin=n,t.originPoint=P(e.min,e.max,t.origin),t.scale=nR(i)/nR(e),t.translate=P(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function nL(t,e,i,n){nj(t.x,e.x,i.x,n?n.originX:void 0),nj(t.y,e.y,i.y,n?n.originY:void 0)}function nF(t,e,i){t.min=i.min+e.min,t.max=t.min+nR(e)}function nO(t,e,i){t.min=e.min-i.min,t.max=t.min+nR(e)}function nB(t,e,i){nO(t.x,e.x,i.x),nO(t.y,e.y,i.y)}function nI(t){return[t("x"),t("y")]}let nU=({current:t})=>t?t.ownerDocument.defaultView:null,nN=(t,e)=>Math.abs(t-e);class nW{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:r=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=nz(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(nN(t.x,e.x)**2+nN(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=td;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=n$(e,this.transformPagePoint),tu.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=nz("pointercancel"===t.type?this.lastMoveEventInfo:n$(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!nV(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=n||window;let o=n$(nk(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=td;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,nz(o,this.history)),this.removeListeners=is(nD(this.contextWindow,"pointermove",this.handlePointerMove),nD(this.contextWindow,"pointerup",this.handlePointerUp),nD(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),th(this.updatePoint)}}function n$(t,e){return e?{point:e(t.point)}:t}function n_(t,e){return{x:t.x-e.x,y:t.y-e.y}}function nz({point:t},e){return{point:t,delta:n_(t,nY(e)),offset:n_(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=nY(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>io(.1)));)i--;if(!n)return{x:0,y:0};let s=(r.timestamp-n.timestamp)/1e3;if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function nY(t){return t[t.length-1]}function nK(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function nX(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function nH(t,e,i){return{min:nq(t,e),max:nq(t,i)}}function nq(t,e){return"number"==typeof t?t:t[e]||0}let nG=new WeakMap;class nZ{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=en(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let r=t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(nk(t).point)},s=(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(nE[t])return null;else return nE[t]=!0,()=>{nE[t]=!1};return nE.x||nE.y?null:(nE.x=nE.y=!0,()=>{nE.x=nE.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nI(t=>{let e=this.getAxisMotionValue(t).get()||0;if($.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=nR(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&tu.postRender(()=>r(t,e)),ie(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},o=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},a=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>nI(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()),{dragSnapToOrigin:u}=this.getProps();this.panSession=new nW(t,{onSessionStart:r,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:i,contextWindow:nU(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!n||!i)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&tu.postRender(()=>o(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!nQ(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?P(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?P(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&e2(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:nK(t.x,i,r),y:nK(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:nH(t,"left","right"),y:nH(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&nI(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!e2(e))return!1;let n=e.current;Z(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=L(t,i),{scroll:r}=e;return r&&(D(n.x,r.offset.x),D(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:nX(t.x,s.x),y:nX(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=b(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(nI(o=>{if(!nQ(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return ie(this.visualElement,t),i.start(np(t,i,0,e,this.visualElement,!1))}stopAnimation(){nI(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){nI(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){nI(e=>{let{drag:i}=this.getProps();if(!nQ(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-P(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!e2(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};nI(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=nR(t),r=nR(e);return r>n?i=iG(e.min,e.max-n,t.min):n>r&&(i=iG(t.min,t.max-r,e.min)),O(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),nI(e=>{if(!nQ(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(P(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;nG.set(this.visualElement,this);let t=nD(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();e2(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),tu.read(e);let r=nC(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(nI(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function nQ(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class nJ extends nP{constructor(t){super(t),this.removeGroupControls=tr,this.removeListeners=tr,this.controls=new nZ(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tr}unmount(){this.removeGroupControls(),this.removeListeners()}}let n0=t=>(e,i)=>{t&&tu.postRender(()=>t(e,i))};class n1 extends nP{constructor(){super(...arguments),this.removePointerDownListener=tr}onPointerDown(t){this.session=new nW(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nU(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:n0(t),onStart:n0(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&tu.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=nD(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n5=i(2082);let n2={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function n3(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let n9={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!_.test(t))return t;else t=parseFloat(t);let i=n3(t,e.target.x),n=n3(t,e.target.y);return`${i}% ${n}%`}},n4=!1;class n6 extends r.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in n7)eS[t]=n7[t],v(t)&&(eS[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),n4&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),n2.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,n4=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||tu.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),t6.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n4=!0,n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function n8(t){let[e,i]=(0,n5.xQ)(),n=(0,r.useContext)(eU.L);return(0,eI.jsx)(n6,{...t,layoutGroup:n,switchLayoutGroup:(0,r.useContext)(e9),isPresent:e,safeToRemove:i})}let n7={borderRadius:{...n9,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:n9,borderTopRightRadius:n9,borderBottomLeftRadius:n9,borderBottomRightRadius:n9,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tU.parse(t);if(n.length>5)return t;let r=tU.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=P(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var rt=i(6983);function re(t){return(0,rt.G)(t)&&"ownerSVGElement"in t}let ri=(t,e)=>t.depth-e.depth;class rn{constructor(){this.children=[],this.isDirty=!1}add(t){t0(this.children,t),this.isDirty=!0}remove(t){t1(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(ri),this.isDirty=!1,this.children.forEach(t)}}let rr=["TopLeft","TopRight","BottomLeft","BottomRight"],rs=rr.length,ro=t=>"string"==typeof t?parseFloat(t):t,ra=t=>"number"==typeof t||_.test(t);function rl(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let ru=rd(0,.5,iY),rh=rd(.5,.95,tr);function rd(t,e,i){return n=>n<t?0:n>e?1:i(iG(t,e,n))}function rc(t,e){t.min=e.min,t.max=e.max}function rp(t,e){rc(t.x,e.x),rc(t.y,e.y)}function rm(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rf(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function rg(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if($.test(e)&&(e=parseFloat(e),e=P(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=P(s.min,s.max,n);t===s&&(a-=e),t.min=rf(t.min,e,i,a,r),t.max=rf(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let ry=["x","scaleX","originX"],rv=["y","scaleY","originY"];function rx(t,e,i,n){rg(t.x,e,ry,i?i.x:void 0,n?n.x:void 0),rg(t.y,e,rv,i?i.y:void 0,n?n.y:void 0)}function rT(t){return 0===t.translate&&1===t.scale}function rw(t){return rT(t.x)&&rT(t.y)}function rb(t,e){return t.min===e.min&&t.max===e.max}function rP(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rS(t,e){return rP(t.x,e.x)&&rP(t.y,e.y)}function rA(t){return nR(t.x)/nR(t.y)}function rM(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rE{constructor(){this.members=[]}add(t){t0(this.members,t),t.scheduleRender()}remove(t){if(t1(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rC={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rV=["","X","Y","Z"],rk=0;function rD(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function rR({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=rk++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ta.value&&(rC.nodes=rC.calculatedTargetDeltas=rC.calculatedProjections=0),this.nodes.forEach(rF),this.nodes.forEach(r$),this.nodes.forEach(r_),this.nodes.forEach(rO),ta.addProjectionMetrics&&ta.addProjectionMetrics(rC)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rn)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new t5),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=re(e)&&!(re(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=0,r=()=>this.root.updateBlockedByResize=!1;tu.read(()=>{n=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tJ.now(),n=({timestamp:e})=>{let r=e-i;r>=250&&(th(n),t(r-250))};return tu.setup(n,!0),()=>th(n)}(r,250),n2.hasAnimatedSinceResize&&(n2.hasAnimatedSinceResize=!1,this.nodes.forEach(rW)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||rq,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!rS(this.targetLayout,n),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...e7(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||rW(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),th(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rz),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[e3];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",tu,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rI);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(rU);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(rN),this.nodes.forEach(rj),this.nodes.forEach(rL)):this.nodes.forEach(rU),this.clearAllSnapshots();let t=tJ.now();td.delta=O(0,1e3/60,t-td.timestamp),td.timestamp=t,td.isProcessing=!0,tc.update.process(td),tc.preRender.process(td),tc.render.process(td),td.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,t6.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rB),this.sharedNodes.forEach(rY)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tu.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tu.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nR(this.snapshot.measuredBox.x)||nR(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=en(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rw(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||M(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),rQ((e=n).x),rQ(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return en();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(r0))){let{scroll:t}=this.root;t&&(D(e.x,t.offset.x),D(e.y,t.offset.y))}return e}removeElementScroll(t){let e=en();if(rp(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&rp(e,t),D(e.x,r.offset.x),D(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=en();rp(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&j(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),M(n.latestValues)&&j(i,n.latestValues)}return M(this.latestValues)&&j(i,this.latestValues),i}removeTransform(t){let e=en();rp(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!M(i.latestValues))continue;A(i.latestValues)&&i.updateSnapshot();let n=en();rp(n,i.measurePageBox()),rx(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return M(this.latestValues)&&rx(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==td.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=td.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=en(),this.relativeTargetOrigin=en(),nB(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rp(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=en(),this.targetWithTransforms=en()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,nF(s.x,o.x,a.x),nF(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rp(this.target,this.layout.layoutBox),k(this.target,this.targetDelta)):rp(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=en(),this.relativeTargetOrigin=en(),nB(this.relativeTargetOrigin,this.target,t.target),rp(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ta.value&&rC.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||A(this.parent.latestValues)||E(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===td.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;rp(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&j(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,k(t,s)),n&&M(r.latestValues)&&j(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=en());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rm(this.prevProjectionDelta.x,this.projectionDelta.x),rm(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nL(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&rM(this.projectionDelta.x,this.prevProjectionDelta.x)&&rM(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ta.value&&rC.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ee(),this.projectionDelta=ee(),this.projectionDeltaWithTransform=ee()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=ee();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=en(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(rH));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(rK(o.x,t.x,n),rK(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;nB(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=n,rX(p.x,m.x,f.x,g),rX(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,c=i,rb(u.x,c.x)&&rb(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=en()),rp(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=P(0,i.opacity??1,ru(n)),t.opacityExit=P(e.opacity??1,0,rh(n))):s&&(t.opacity=P(e.opacity??1,i.opacity??1,n));for(let r=0;r<rs;r++){let s=`border${rr[r]}Radius`,o=rl(e,s),a=rl(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||ra(o)===ra(a)?(t[s]=Math.max(P(ro(o),ro(a),n),0),($.test(a)||$.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=P(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(th(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tu.update(()=>{n2.hasAnimatedSinceResize=!0,ia.layout++,this.motionValue||(this.motionValue=t9(0)),this.currentAnimation=function(t,e,i){let n=tZ(t)?t:t9(t);return n.start(np("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{ia.layout--},onComplete:()=>{ia.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&rJ(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||en();let e=nR(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=nR(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}rp(e,i),j(e,r),nL(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rE),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&rD("z",t,n,this.animationValues);for(let e=0;e<rV.length;e++)rD(`rotate${rV[e]}`,t,n,this.animationValues),rD(`skew${rV[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=eQ(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eQ(e?.pointerEvents)||""),this.hasProjected&&!M(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let r=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(s=i(r,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,eS){if(void 0===r[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=eS[e],l="none"===s?r[e]:i(r[e],n);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?eQ(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(rI),this.root.sharedNodes.clear()}}}function rj(t){t.updateLayout()}function rL(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?nI(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=nR(n);n.min=i[t].min,n.max=n.min+r}):rJ(r,e.layoutBox,i)&&nI(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=nR(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=ee();nL(o,i,e.layoutBox);let a=ee();s?nL(a,t.applyTransform(n,!0),e.measuredBox):nL(a,i,e.layoutBox);let l=!rw(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=en();nB(o,e.layoutBox,r.layoutBox);let a=en();nB(a,i,s.layoutBox),rS(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function rF(t){ta.value&&rC.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rO(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function rB(t){t.clearSnapshot()}function rI(t){t.clearMeasurements()}function rU(t){t.isLayoutDirty=!1}function rN(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function rW(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function r$(t){t.resolveTargetDelta()}function r_(t){t.calcProjection()}function rz(t){t.resetSkewAndRotation()}function rY(t){t.removeLeadSnapshot()}function rK(t,e,i){t.translate=P(e.translate,0,i),t.scale=P(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function rX(t,e,i,n){t.min=P(e.min,i.min,n),t.max=P(e.max,i.max,n)}function rH(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let rq={duration:.45,ease:[.4,0,.1,1]},rG=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rZ=rG("applewebkit/")&&!rG("chrome/")?Math.round:tr;function rQ(t){t.min=rZ(t.min),t.max=rZ(t.max)}function rJ(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rA(e)-rA(i)))}function r0(t){return t!==t.root&&t.scroll?.wasRoot}let r1=rR({attachResizeListener:(t,e)=>nC(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),r5={current:void 0},r2=rR({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!r5.current){let t=new r1({});t.mount(window),t.setOptions({layoutScroll:!0}),r5.current=t}return r5.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function r3(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function r9(t){return!("touch"===t.pointerType||nE.x||nE.y)}function r4(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&tu.postRender(()=>r(e,nk(e)))}class r6 extends nP{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=r3(t,i),o=t=>{if(!r9(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{r9(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(r4(this.node,e,"Start"),t=>r4(this.node,t,"End"))))}unmount(){}}class r8 extends nP{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=is(nC(this.node.current,"focus",()=>this.onFocus()),nC(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var r7=i(7351);let st=(t,e)=>!!e&&(t===e||st(t,e.parentElement)),se=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),si=new WeakSet;function sn(t){return e=>{"Enter"===e.key&&t(e)}}function sr(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function ss(t){return nV(t)&&!(nE.x||nE.y)}function so(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&tu.postRender(()=>r(e,nk(e)))}class sa extends nP{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=r3(t,i),o=t=>{let n=t.currentTarget;if(!ss(t))return;si.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),si.has(n)&&si.delete(n),ss(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||st(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,r7.s)(t))&&(t.addEventListener("focus",t=>((t,e)=>{let i=t.currentTarget;if(!i)return;let n=sn(()=>{if(si.has(i))return;sr(i,"down");let t=sn(()=>{sr(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>sr(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)})(t,r)),se.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(so(this.node,e,"Start"),(t,{success:e})=>so(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sl=new WeakMap,su=new WeakMap,sh=t=>{let e=sl.get(t.target);e&&e(t)},sd=t=>{t.forEach(sh)},sc={some:0,all:1};class sp extends nP{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:sc[n]},o=t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)};var a=this.node.current;let l=function({root:t,...e}){let i=t||document;su.has(i)||su.set(i,{});let n=su.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(sd,{root:t,...e})),n[r]}(s);return sl.set(a,o),l.observe(a),()=>{sl.delete(a),l.unobserve(a)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sm=function(t,e){if("undefined"==typeof Proxy)return e6;let i=new Map,n=(i,n)=>e6(i,n,t,e);return new Proxy((t,e)=>n(t,e),{get:(r,s)=>"create"===s?n:(i.has(s)||i.set(s,e6(s,void 0,t,e)),i.get(s))})}({animation:{Feature:nS},exit:{Feature:nM},inView:{Feature:sp},tap:{Feature:sa},focus:{Feature:r8},hover:{Feature:r6},pan:{Feature:n1},drag:{Feature:nJ,ProjectionNode:r2,MeasureLayout:n8},layout:{ProjectionNode:r2,MeasureLayout:n8}},(t,e)=>eB(t)?new eF(e):new eE(e,{allowProjection:t!==r.Fragment}))},8859:(t,e)=>{function i(t){let e={};for(let[i,n]of t.entries()){let t=e[i];void 0===t?e[i]=n:Array.isArray(t)?t.push(n):e[i]=[t,n]}return e}function n(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function r(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))if(Array.isArray(r))for(let t of r)e.append(i,n(t));else e.set(i,n(r));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,n]of e.entries())t.append(i,n)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},8972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},9946:(t,e,i)=>{i.d(e,{A:()=>l});var n=i(2115);let r=t=>{let e=t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase());return e.charAt(0).toUpperCase()+e.slice(1)},s=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((t,e)=>{let{color:i="currentColor",size:r=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,iconNode:d,...c}=t;return(0,n.createElement)("svg",{ref:e,...o,width:r,height:r,stroke:i,strokeWidth:l?24*Number(a)/Number(r):a,className:s("lucide",u),...!h&&!(t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0})(c)&&{"aria-hidden":"true"},...c},[...d.map(t=>{let[e,i]=t;return(0,n.createElement)(e,i)}),...Array.isArray(h)?h:[h]])}),l=(t,e)=>{let i=(0,n.forwardRef)((i,o)=>{let{className:l,...u}=i;return(0,n.createElement)(a,{ref:o,iconNode:e,className:s("lucide-".concat(r(t).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(t),l),...u})});return i.displayName=r(t),i}},9991:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return f},PageNotFoundError:function(){return g},SP:function(){return c},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function n(t){let e,i=!1;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];return i||(i=!0,e=t(...r)),e}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>r.test(t);function o(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function d(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await d(e.Component,e.ctx)}:{};let n=await t.getInitialProps(e);if(i&&u(i))return n;if(!n)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let c="undefined"!=typeof performance,p=c&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class m extends Error{}class f extends Error{}class g extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class y extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(t){return JSON.stringify({message:t.message,stack:t.stack})}}}]);