{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url", "GOOGLE_CLIENT_ID": "@google_client_id", "GOOGLE_CLIENT_SECRET": "@google_client_secret", "GITHUB_ID": "@github_id", "GITHUB_SECRET": "@github_secret", "EMAIL_SERVER_HOST": "@email_server_host", "EMAIL_SERVER_PORT": "@email_server_port", "EMAIL_SERVER_USER": "@email_server_user", "EMAIL_SERVER_PASSWORD": "@email_server_password", "EMAIL_FROM": "@email_from", "OPENAI_API_KEY": "@openai_api_key", "OPENAI_BASE_URL": "@openai_base_url", "DATABASE_URL": "@database_url", "DIRECT_URL": "@direct_url", "CREEM_API_KEY": "@creem_api_key", "CREEM_WEBHOOK_SECRET": "@creem_webhook_secret"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/", "destination": "/en", "permanent": false}]}