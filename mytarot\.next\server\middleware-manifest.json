{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images|sitemap.xml|robots.txt).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images|sitemap.xml|robots.txt).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "W2Jg0J4yZwe5YoMqrcfGB", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "A1d/zTtDXxsz1x1ICfAAgxtwOGATr31z8bf+pFjVkc4=", "__NEXT_PREVIEW_MODE_ID": "2e858a41a5fdab34dfca9ae41d443aa2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e5263ae5710ca4fe71da7f09a6ce280df30b479eb7b75e1e6b0ade68044e0fae", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3d3318459c846fb46e4e5f185166e75a6fa55859670b871e88bbd2592dca78de"}}}, "functions": {}, "sortedMiddleware": ["/"]}