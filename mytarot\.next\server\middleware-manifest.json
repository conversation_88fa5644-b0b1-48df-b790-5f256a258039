{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "A1d/zTtDXxsz1x1ICfAAgxtwOGATr31z8bf+pFjVkc4=", "__NEXT_PREVIEW_MODE_ID": "3bc9ebc92c0fe37912221a86368d36cf", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b3e974f465c40694e7995ee08e9ade6aae24ce7950cf4edad455b540e04ba2ee", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c14df17baa93a2629b892d12c17e9010fe743504019ef67c520e81ab265e1e7c"}}}, "functions": {}, "sortedMiddleware": ["/"]}