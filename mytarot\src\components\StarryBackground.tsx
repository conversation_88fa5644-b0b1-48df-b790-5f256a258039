'use client';

export default function StarryBackground() {
  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {/* 主要星空渐变 */}
      <div className="absolute inset-0 bg-gradient-radial from-indigo-900/20 via-purple-900/40 to-slate-900"></div>
      
      {/* 星星效果 */}
      <div className="absolute inset-0">
        {/* 大星星 */}
        <div className="absolute top-[10%] left-[15%] w-2 h-2 bg-white rounded-full animate-pulse opacity-80"></div>
        <div className="absolute top-[20%] right-[20%] w-1.5 h-1.5 bg-blue-200 rounded-full animate-pulse opacity-70 animation-delay-1000"></div>
        <div className="absolute top-[35%] left-[25%] w-1 h-1 bg-purple-200 rounded-full animate-pulse opacity-60 animation-delay-2000"></div>
        <div className="absolute top-[45%] right-[35%] w-2 h-2 bg-yellow-200 rounded-full animate-pulse opacity-75 animation-delay-3000"></div>
        <div className="absolute top-[60%] left-[40%] w-1.5 h-1.5 bg-white rounded-full animate-pulse opacity-80 animation-delay-4000"></div>
        <div className="absolute top-[75%] right-[15%] w-1 h-1 bg-blue-300 rounded-full animate-pulse opacity-65 animation-delay-5000"></div>
        <div className="absolute top-[85%] left-[60%] w-2 h-2 bg-purple-300 rounded-full animate-pulse opacity-70 animation-delay-6000"></div>
        
        {/* 中等星星 */}
        <div className="absolute top-[15%] left-[70%] w-1 h-1 bg-white rounded-full animate-pulse opacity-50 animation-delay-1500"></div>
        <div className="absolute top-[25%] left-[50%] w-0.5 h-0.5 bg-blue-200 rounded-full animate-pulse opacity-60 animation-delay-2500"></div>
        <div className="absolute top-[40%] right-[60%] w-1 h-1 bg-purple-200 rounded-full animate-pulse opacity-45 animation-delay-3500"></div>
        <div className="absolute top-[55%] left-[80%] w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse opacity-55 animation-delay-4500"></div>
        <div className="absolute top-[70%] right-[45%] w-1 h-1 bg-white rounded-full animate-pulse opacity-50 animation-delay-5500"></div>
        <div className="absolute top-[80%] left-[30%] w-0.5 h-0.5 bg-blue-300 rounded-full animate-pulse opacity-40 animation-delay-6500"></div>
        
        {/* 小星星 */}
        <div className="absolute top-[12%] right-[80%] w-0.5 h-0.5 bg-white rounded-full animate-pulse opacity-30 animation-delay-500"></div>
        <div className="absolute top-[28%] left-[85%] w-0.5 h-0.5 bg-purple-300 rounded-full animate-pulse opacity-35 animation-delay-1200"></div>
        <div className="absolute top-[42%] left-[10%] w-0.5 h-0.5 bg-blue-200 rounded-full animate-pulse opacity-40 animation-delay-2200"></div>
        <div className="absolute top-[58%] right-[75%] w-0.5 h-0.5 bg-yellow-200 rounded-full animate-pulse opacity-30 animation-delay-3200"></div>
        <div className="absolute top-[72%] left-[75%] w-0.5 h-0.5 bg-white rounded-full animate-pulse opacity-35 animation-delay-4200"></div>
        <div className="absolute top-[88%] right-[85%] w-0.5 h-0.5 bg-purple-200 rounded-full animate-pulse opacity-25 animation-delay-5200"></div>
        
        {/* 更多小星星填充空白区域 */}
        <div className="absolute top-[8%] left-[45%] w-0.5 h-0.5 bg-white rounded-full animate-pulse opacity-25 animation-delay-800"></div>
        <div className="absolute top-[18%] right-[65%] w-0.5 h-0.5 bg-blue-100 rounded-full animate-pulse opacity-30 animation-delay-1800"></div>
        <div className="absolute top-[32%] left-[65%] w-0.5 h-0.5 bg-purple-100 rounded-full animate-pulse opacity-35 animation-delay-2800"></div>
        <div className="absolute top-[48%] right-[85%] w-0.5 h-0.5 bg-yellow-100 rounded-full animate-pulse opacity-25 animation-delay-3800"></div>
        <div className="absolute top-[62%] left-[15%] w-0.5 h-0.5 bg-white rounded-full animate-pulse opacity-30 animation-delay-4800"></div>
        <div className="absolute top-[78%] right-[55%] w-0.5 h-0.5 bg-blue-100 rounded-full animate-pulse opacity-25 animation-delay-5800"></div>
        <div className="absolute top-[92%] left-[85%] w-0.5 h-0.5 bg-purple-100 rounded-full animate-pulse opacity-20 animation-delay-6800"></div>
      </div>

      {/* 神秘光晕效果 */}
      <div className="absolute top-[20%] left-[10%] w-96 h-96 bg-purple-500/10 rounded-full filter blur-3xl animate-float"></div>
      <div className="absolute bottom-[20%] right-[10%] w-80 h-80 bg-blue-500/10 rounded-full filter blur-3xl animate-float animation-delay-3000"></div>
      <div className="absolute top-[50%] left-[50%] w-64 h-64 bg-indigo-500/10 rounded-full filter blur-3xl animate-float animation-delay-6000"></div>
      
      {/* 流星效果 */}
      <div className="absolute top-[30%] left-[20%] w-1 h-20 bg-gradient-to-b from-white to-transparent opacity-60 rotate-45 animate-meteor"></div>
      <div className="absolute top-[60%] right-[30%] w-0.5 h-16 bg-gradient-to-b from-blue-200 to-transparent opacity-50 rotate-45 animate-meteor animation-delay-8000"></div>
      
      {/* 星座连线效果 */}
      <div className="absolute top-[25%] left-[30%] w-16 h-0.5 bg-gradient-to-r from-transparent via-purple-300/30 to-transparent rotate-12"></div>
      <div className="absolute top-[65%] right-[25%] w-12 h-0.5 bg-gradient-to-r from-transparent via-blue-300/30 to-transparent -rotate-12"></div>
      
      {/* 神秘符号光效 */}
      <div className="absolute top-[40%] right-[20%] w-8 h-8 opacity-20">
        <div className="w-full h-full border border-purple-300/50 rounded-full animate-spin-slow"></div>
        <div className="absolute inset-2 border border-blue-300/50 rounded-full animate-spin-reverse"></div>
      </div>
      
      <div className="absolute bottom-[30%] left-[25%] w-6 h-6 opacity-15">
        <div className="w-full h-full border border-yellow-300/50 rounded-full animate-spin-slow animation-delay-4000"></div>
        <div className="absolute inset-1 border border-purple-300/50 rounded-full animate-spin-reverse animation-delay-2000"></div>
      </div>
    </div>
  );
}
