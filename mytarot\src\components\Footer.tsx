'use client';

import Link from 'next/link';
import { useLocale } from 'next-intl';

export default function Footer() {
  const locale = useLocale();

  return (
    <footer className="relative z-10 bg-black/20 backdrop-blur-sm border-t border-purple-500/20 mt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="grid md:grid-cols-3 gap-8">
          {/* Brand */}
          <div>
            <h3 className="text-white text-xl font-bold mb-4">
              🔮 {locale === 'zh' ? '神秘塔罗' : 'Mystic Tarot'}
            </h3>
            <p className="text-gray-300 text-sm">
              {locale === 'zh' 
                ? '通过古老的塔罗牌智慧探索你的内心世界' 
                : 'Explore your inner world through ancient tarot wisdom'
              }
            </p>
          </div>

          {/* Features */}
          <div>
            <h4 className="text-white font-semibold mb-4">
              {locale === 'zh' ? '功能特色' : 'Features'}
            </h4>
            <div className="space-y-2">
              <Link
                href={`/${locale}/reading`}
                className="block text-gray-300 hover:text-purple-300 transition-colors text-sm"
              >
                {locale === 'zh' ? '塔罗解读' : 'Tarot Reading'}
              </Link>
              <div className="text-gray-300 text-sm">
                {locale === 'zh' ? '✨ 完全免费' : '✨ Completely Free'}
              </div>
              <div className="text-gray-300 text-sm">
                {locale === 'zh' ? '🌍 多语言支持' : '🌍 Multi-language'}
              </div>
              <div className="text-gray-300 text-sm">
                {locale === 'zh' ? '📱 移动端友好' : '📱 Mobile Friendly'}
              </div>
            </div>
          </div>

          {/* About */}
          <div>
            <h4 className="text-white font-semibold mb-4">
              {locale === 'zh' ? '关于我们' : 'About'}
            </h4>
            <div className="space-y-2">
              <p className="text-gray-300 text-sm">
                {locale === 'zh'
                  ? '我们致力于为用户提供免费、准确的塔罗牌解读服务。'
                  : 'We are dedicated to providing free and accurate tarot reading services.'
                }
              </p>
              <div className="text-gray-300 text-sm">
                {locale === 'zh' ? '🔮 古老智慧' : '🔮 Ancient Wisdom'}
              </div>
              <div className="text-gray-300 text-sm">
                {locale === 'zh' ? '💫 现代科技' : '💫 Modern Technology'}
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-purple-500/20 mt-8 pt-6 text-center">
          <p className="text-gray-400 text-sm">
            © 2024 Mystic Tarot. {locale === 'zh' ? '保留所有权利。' : 'All rights reserved.'}
          </p>
          <p className="text-gray-400 text-sm mt-2">
            {locale === 'zh' ? '完全免费，无限制使用' : 'Completely free, unlimited use'}
          </p>
        </div>
      </div>
    </footer>
  );
}
