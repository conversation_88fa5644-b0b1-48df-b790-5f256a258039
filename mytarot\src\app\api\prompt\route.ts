import { NextRequest, NextResponse } from 'next/server';
import { TarotCard } from '@/types/tarot';

// 构建塔罗牌提示词
function buildTarotPrompt(cards: TarotCard[], question: string, locale: string): string {
  const isZh = locale === 'zh';
  
  if (isZh) {
    const systemPrompt = `你是一位经验丰富的塔罗牌解读师，拥有深厚的神秘学知识和直觉洞察力。请为用户提供一次深入、准确且富有洞察力的塔罗牌解读。

解读要求：
1. 基于用户的问题和三张塔罗牌（过去、现在、未来）进行解读
2. 卡牌之间的联系和故事线
3. 基于解读的实用指导和建议

请用温暖、富有洞察力和鼓励性的语调书写,总体解读内容应约为500-600字。`;

    const userPrompt = `用户问题：${question}

塔罗牌解读：
${cards.map((card, index) => {
  const position = ['过去', '现在', '未来'][index];
  const cardName = card.name;
  const keywords = card.keywords.join(', ');
  const meaning = card.isReversed ? card.reversedMeaning : card.meaning;
  const orientation = card.isReversed ? '（逆位）' : '（正位）';
  
  return `${position}：${cardName}${orientation}
关键词：${keywords}
含义：${meaning}`;
}).join('\n\n')}

请基于以上信息，为用户提供一次完整的塔罗牌解读。`;

    return `${systemPrompt}\n\n${userPrompt}`;
  } else {
    const systemPrompt = `You are an experienced tarot reader with deep knowledge of mysticism and intuitive insights. Please provide a thorough, accurate, and insightful tarot reading for the user.

Reading requirements:
1. Base the reading on the user's question and three tarot cards (Past, Present, Future)
2. The connections and story between the cards
3. Practical guidance and advice based on the reading

Write in a warm, insightful, and encouraging tone. The reading should be approximately 500-600 words.`;

    const userPrompt = `User's Question: ${question}

Tarot Reading:
${cards.map((card, index) => {
  const position = ['Past', 'Present', 'Future'][index];
  const cardName = card.nameEn;
  const keywords = card.keywordsEn.join(', ');
  const meaning = card.isReversed ? card.reversedMeaningEn : card.meaningEn;
  const orientation = card.isReversed ? '(Reversed)' : '(Upright)';
  
  return `${position}: ${cardName} ${orientation}
Keywords: ${keywords}
Meaning: ${meaning}`;
}).join('\n\n')}

Please provide a complete tarot reading based on the above information.`;

    return `${systemPrompt}\n\n${userPrompt}`;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { cards, question, locale = 'en' } = await request.json();

    if (!cards || !Array.isArray(cards) || cards.length !== 3) {
      return NextResponse.json(
        { error: 'Three cards are required' },
        { status: 400 }
      );
    }

    if (!question || typeof question !== 'string') {
      return NextResponse.json(
        { error: 'Question is required' },
        { status: 400 }
      );
    }

    // 生成提示词
    const prompt = buildTarotPrompt(cards, question, locale);

    return NextResponse.json({
      prompt,
      cards: cards.map((card: any, index: number) => ({
        ...card,
        position: ['Past', 'Present', 'Future'][index],
      })),
      question,
      locale
    });

  } catch (error) {
    console.error('生成提示词错误:', error);
    return NextResponse.json(
      { error: 'Failed to generate prompt' },
      { status: 500 }
    );
  }
}
