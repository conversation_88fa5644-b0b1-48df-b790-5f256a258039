const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function initDatabase() {
  try {
    console.log('🚀 开始初始化数据库...');
    
    // 检查环境变量
    const databaseUrl = process.env.DATABASE_URL || process.env.DIRECT_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL 或 DIRECT_URL 环境变量未设置');
    }
    
    console.log('📋 数据库连接:', databaseUrl.replace(/:[^:@]*@/, ':***@'));
    
    // 确保使用生产环境的 schema
    const schemaPath = path.join(__dirname, '../prisma/schema.prisma');
    const productionSchemaPath = path.join(__dirname, '../prisma/schema.production.prisma');
    
    if (fs.existsSync(productionSchemaPath)) {
      console.log('📝 使用生产环境 schema...');
      const productionSchema = fs.readFileSync(productionSchemaPath, 'utf8');
      fs.writeFileSync(schemaPath, productionSchema);
    }
    
    // 生成 Prisma 客户端
    console.log('🔧 生成 Prisma 客户端...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    
    // 推送数据库 schema
    console.log('🗄️ 创建数据库表...');
    const env = {
      ...process.env,
      DATABASE_URL: process.env.DIRECT_URL || process.env.DATABASE_URL
    };
    
    execSync('npx prisma db push --accept-data-loss', { 
      stdio: 'inherit',
      env: env
    });
    
    console.log('✅ 数据库初始化完成！');
    console.log('📊 数据库表已创建：');
    console.log('  - User (用户表)');
    console.log('  - Account (账户表)');
    console.log('  - Session (会话表)');
    console.log('  - VerificationToken (验证令牌表)');
    console.log('  - Reading (塔罗牌解读表)');
    console.log('  - Subscription (订阅表)');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    console.error('🔍 请检查：');
    console.error('  1. 数据库连接字符串是否正确');
    console.error('  2. 数据库服务是否可访问');
    console.error('  3. 权限是否足够');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = initDatabase;
