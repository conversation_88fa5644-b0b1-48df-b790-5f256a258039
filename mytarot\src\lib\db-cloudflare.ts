// Cloudflare D1 数据库配置
import { PrismaClient } from '@prisma/client'
import { PrismaD1 } from '@prisma/adapter-d1'

declare global {
  var __prisma: PrismaClient | undefined
}

// Cloudflare D1 适配器
export function createPrismaClient(d1Database?: D1Database) {
  if (d1Database) {
    // 生产环境：使用 Cloudflare D1
    const adapter = new PrismaD1(d1Database)
    return new PrismaClient({ adapter })
  } else {
    // 开发环境：使用 SQLite
    return new PrismaClient()
  }
}

// 获取数据库实例
export function getDatabase(env?: { DB?: D1Database }) {
  if (typeof window !== 'undefined') {
    throw new Error('Database should not be used on the client side')
  }

  // 在 Cloudflare Pages Functions 中，env.DB 是 D1 数据库实例
  const d1Database = env?.DB

  if (globalThis.__prisma) {
    return globalThis.__prisma
  }

  const prisma = createPrismaClient(d1Database)
  
  if (process.env.NODE_ENV === 'development') {
    globalThis.__prisma = prisma
  }

  return prisma
}

// 默认导出（用于开发环境）
const prisma = globalThis.__prisma || createPrismaClient()

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma
}

export default prisma
