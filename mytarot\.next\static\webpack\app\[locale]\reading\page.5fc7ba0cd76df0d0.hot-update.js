"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/components/CardSelection.tsx":
/*!******************************************!*\
  !*** ./src/components/CardSelection.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CardSelection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _TarotCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TarotCard */ \"(app-pages-browser)/./src/components/TarotCard.tsx\");\n/* harmony import */ var _data_generate_cards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/generate-cards */ \"(app-pages-browser)/./src/data/generate-cards.ts\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CardSelection(param) {\n    let { onCardsSelected } = param;\n    _s();\n    const [allCards, setAllCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCards, setSelectedCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [shuffledPositions, setShuffledPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)('reading');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CardSelection.useEffect\": ()=>{\n            // 生成所有78张牌\n            const cards = (0,_data_generate_cards__WEBPACK_IMPORTED_MODULE_3__.generateAllCards)();\n            setAllCards(cards);\n            // 创建更美观的卡牌布局 - 多层螺旋分布\n            const positions = cards.map({\n                \"CardSelection.useEffect.positions\": (_, index)=>{\n                    // 使用多层螺旋算法，确保卡牌不超出屏幕\n                    const layer = Math.floor(index / 12); // 每层12张卡\n                    const angleStep = 30; // 每张卡间隔30度\n                    const baseAngle = index % 12 * angleStep + layer * 15; // 每层错开15度\n                    const radius = Math.min(25 + layer * 8, 35); // 限制最大半径，确保不超出屏幕\n                    // 添加一些随机偏移，但保持在安全范围内\n                    const randomOffsetX = (index * 17 % 21 - 10) * 0.5; // -5 to 5\n                    const randomOffsetY = (index * 13 % 21 - 10) * 0.5; // -5 to 5\n                    const x = Math.cos(baseAngle * Math.PI / 180) * radius + randomOffsetX;\n                    const y = Math.sin(baseAngle * Math.PI / 180) * radius * 0.6 + randomOffsetY; // 压扁椭圆\n                    return {\n                        x: Math.max(-35, Math.min(35, x)),\n                        y: Math.max(-25, Math.min(25, y)),\n                        rotation: (index * 23 % 60 - 30) * 0.7 // 减小旋转角度\n                    };\n                }\n            }[\"CardSelection.useEffect.positions\"]);\n            setShuffledPositions(positions);\n        }\n    }[\"CardSelection.useEffect\"], []);\n    const handleCardClick = (card)=>{\n        // 检查是否已达到最大选择数量\n        if (selectedCards.length >= 3) return;\n        // 检查卡牌是否已被选择，防止重复选择\n        if (isCardSelected(card)) return;\n        const newSelectedCards = [\n            ...selectedCards,\n            card\n        ];\n        setSelectedCards(newSelectedCards);\n        if (newSelectedCards.length === 3) {\n            // 延迟一下让用户看到第三张卡的选择效果\n            setTimeout(()=>{\n                onCardsSelected(newSelectedCards);\n            }, 500);\n        }\n    };\n    const isCardSelected = (card)=>{\n        return selectedCards.some((selected)=>selected.id === card.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full min-h-screen overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(147,51,234,0.1)_0%,transparent_70%)]\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(59,130,246,0.1)_0%,transparent_50%)]\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 md:top-8 left-1/2 transform -translate-x-1/2 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/10 backdrop-blur-sm rounded-full px-4 md:px-6 py-2 md:py-3 border border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-sm md:text-lg font-semibold\",\n                        children: [\n                            t('selectedCards', {\n                                count: selectedCards.length\n                            }),\n                            \" / 3\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-16 md:top-20 left-1/2 transform -translate-x-1/2 z-20 flex gap-2 md:gap-4\",\n                children: [\n                    0,\n                    1,\n                    2\n                ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-18 md:w-16 md:h-24 rounded-lg border-2 border-dashed \".concat(selectedCards[index] ? 'border-purple-400 bg-purple-400/20' : 'border-purple-600/50 bg-purple-600/10', \" flex items-center justify-center\"),\n                        children: selectedCards[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                scale: 0,\n                                rotate: 180\n                            },\n                            animate: {\n                                scale: 1,\n                                rotate: 0\n                            },\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TarotCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                card: selectedCards[index],\n                                isRevealed: false,\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 15\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full max-w-6xl h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: allCards.map((card, index)=>{\n                            const position = shuffledPositions[index];\n                            if (!position) return null;\n                            const isSelected = isCardSelected(card);\n                            const isDisabled = selectedCards.length >= 3 && !isSelected;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute \".concat(isSelected ? 'cursor-default' : isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'),\n                                style: {\n                                    left: \"\".concat(50 + position.x, \"%\"),\n                                    top: \"\".concat(50 + position.y, \"%\"),\n                                    transform: \"translate(-50%, -50%) rotate(\".concat(position.rotation, \"deg)\"),\n                                    zIndex: isSelected ? 15 : 10 - Math.floor(index / 12)\n                                },\n                                initial: {\n                                    scale: 0,\n                                    opacity: 0,\n                                    rotate: position.rotation + 180\n                                },\n                                animate: {\n                                    scale: isSelected ? 1.1 : isDisabled ? 0.7 : 1,\n                                    opacity: isSelected ? 1 : isDisabled ? 0.3 : 1,\n                                    rotate: position.rotation\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.01,\n                                    type: \"tween\",\n                                    ease: \"easeOut\"\n                                },\n                                whileHover: !isDisabled && !isSelected ? {\n                                    scale: 1.05,\n                                    zIndex: 20\n                                } : {},\n                                onClick: ()=>!isDisabled && !isSelected && handleCardClick(card),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TarotCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    card: card,\n                                    isRevealed: false,\n                                    isSelected: isSelected,\n                                    size: \"medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 19\n                                }, this)\n                            }, card.id, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"bg-white/10 backdrop-blur-sm rounded-2xl px-8 py-4 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white text-lg mb-2\",\n                            children: [\n                                selectedCards.length === 0 && t('selectFirstCard'),\n                                selectedCards.length === 1 && t('selectSecondCard'),\n                                selectedCards.length === 2 && t('selectThirdCard'),\n                                selectedCards.length === 3 && t('cardsComplete')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-purple-200 text-sm\",\n                            children: t('selectCardsDescription')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(CardSelection, \"4lHri/AtiRymmPzXDGA6jIOKrPE=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c = CardSelection;\nvar _c;\n$RefreshReg$(_c, \"CardSelection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CardSelection.tsx\n"));

/***/ })

});