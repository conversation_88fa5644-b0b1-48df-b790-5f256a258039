'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import TarotCard from './TarotCard';
import { useTranslations } from 'next-intl';

interface ShuffleAnimationProps {
  onComplete: () => void;
  isShuffling: boolean;
}

export default function ShuffleAnimation({ onComplete, isShuffling }: ShuffleAnimationProps) {
  const t = useTranslations('reading');
  const [shuffleCount, setShuffleCount] = useState(0);
  const [showCards, setShowCards] = useState(false);
  const [particlePositions, setParticlePositions] = useState<Array<{x: number, y: number}>>([]);

  useEffect(() => {
    // 在客户端生成固定的粒子位置
    if (typeof window !== 'undefined') {
      const positions = Array.from({ length: 20 }, (_, i) => ({
        x: 150 + (Math.sin(i * 0.5) * 100),
        y: 200 + (Math.cos(i * 0.3) * 100)
      }));
      setParticlePositions(positions);
    }
  }, []);

  useEffect(() => {
    if (isShuffling) {
      setShowCards(true);
      setShuffleCount(0);

      const shuffleInterval = setInterval(() => {
        setShuffleCount(prev => {
          if (prev >= 10) {
            clearInterval(shuffleInterval);
            setTimeout(() => {
              setShowCards(false);
              onComplete();
            }, 1000);
            return prev;
          }
          return prev + 1;
        });
      }, 300);

      return () => clearInterval(shuffleInterval);
    }
  }, [isShuffling, onComplete]);

  if (!isShuffling) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="text-center">
        <motion.h2
          className="text-3xl font-bold text-white mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          {t('shuffling')}
        </motion.h2>

        <div className="relative w-80 h-96 mx-auto">
          <AnimatePresence>
            {showCards && Array.from({ length: 5 }).map((_, index) => (
              <motion.div
                key={index}
                className="absolute w-24 h-36"
                initial={{ 
                  x: 150, 
                  y: 200,
                  rotate: 0,
                  scale: 1
                }}
                animate={{
                  x: [150, 100 + index * 40, 150],
                  y: [200, 150 - index * 20, 200],
                  rotate: [0, (index - 2) * 15, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 0.6,
                  repeat: Infinity,
                  delay: index * 0.1,
                  ease: "easeInOut"
                }}
              >
                <TarotCard showBack={true} size="large" />
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Shuffle Effect Particles */}
          <div className="absolute inset-0 pointer-events-none">
            {particlePositions.map((position, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-purple-400 rounded-full"
                initial={{
                  x: 150,
                  y: 200,
                  opacity: 0
                }}
                animate={{
                  x: position.x,
                  y: position.y,
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.1,
                  ease: "easeOut"
                }}
              />
            ))}
          </div>
        </div>

        <motion.div
          className="mt-8 flex items-center justify-center gap-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex gap-1">
            {Array.from({ length: 3 }).map((_, i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-purple-400 rounded-full"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </div>
          <span className="text-purple-200 ml-2">
            {t('preparingCards')}
          </span>
        </motion.div>
      </div>
    </div>
  );
}
