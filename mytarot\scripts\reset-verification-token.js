const { execSync } = require('child_process');

// 设置生产环境变量
process.env.DATABASE_URL = "prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19XNmY3Q1YxZ1VxZ2FEQm5LNDBEbmkiLCJhcGlfa2V5IjoiMDFLMUpEOFE1MDdOWjNETUVBRFZZUTFXNFYiLCJ0ZW5hbnRfaWQiOiI2NjAwMGJlMGVmMDFlYmQyZDNjMWY1NTVkMGFkODJhZjc3M2EyMzlkMmNkMzIzNWIzMjg3ODYzMDY0NmE3MDBiIiwiaW50ZXJuYWxfc2VjcmV0IjoiMTVlZDcwMmEtN2M2Mi00M2FmLTlmNjYtZmE3ODY2NmFmOGIwIn0.U5cXSRaBwwcO7W2JI1j5mSVa5etgOF1hD95sPh_LwFI";

process.env.DIRECT_URL = "postgres://66000be0ef01ebd2d3c1f555d0ad82af773a239d2cd3235b32878630646a700b:<EMAIL>:5432/?sslmode=require";

try {
  console.log('🔄 重置 VerificationToken 表...');
  
  console.log('⚠️ 警告: 这将删除所有现有数据！');
  console.log('🗄️ 强制重置并推送数据库 schema...');
  
  // 使用 force-reset 来完全重建数据库
  execSync('npx prisma db push --schema=prisma/schema.production.prisma --force-reset --accept-data-loss', { 
    stdio: 'inherit'
  });
  
  console.log('🔧 重新生成 Prisma 客户端...');
  execSync('npx prisma generate --schema=prisma/schema.production.prisma', { 
    stdio: 'inherit'
  });
  
  console.log('✅ VerificationToken 表重置完成！');
  console.log('📋 现在所有表都有正确的结构，包括主键');
  
} catch (error) {
  console.error('❌ 重置失败:', error.message);
  process.exit(1);
}
