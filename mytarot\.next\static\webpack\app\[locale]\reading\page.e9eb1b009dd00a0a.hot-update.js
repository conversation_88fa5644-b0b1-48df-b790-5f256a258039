"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/components/CardSelection.tsx":
/*!******************************************!*\
  !*** ./src/components/CardSelection.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CardSelection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _TarotCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TarotCard */ \"(app-pages-browser)/./src/components/TarotCard.tsx\");\n/* harmony import */ var _data_generate_cards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/generate-cards */ \"(app-pages-browser)/./src/data/generate-cards.ts\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CardSelection(param) {\n    let { onCardsSelected } = param;\n    _s();\n    const [allCards, setAllCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCards, setSelectedCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [shuffledPositions, setShuffledPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)('reading');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CardSelection.useEffect\": ()=>{\n            // 生成所有78张牌\n            const cards = (0,_data_generate_cards__WEBPACK_IMPORTED_MODULE_3__.generateAllCards)();\n            setAllCards(cards);\n            // 创建更美观的卡牌布局 - 多层螺旋分布\n            const positions = cards.map({\n                \"CardSelection.useEffect.positions\": (_, index)=>{\n                    // 使用多层螺旋算法，确保卡牌不超出屏幕\n                    const layer = Math.floor(index / 12); // 每层12张卡\n                    const angleStep = 30; // 每张卡间隔30度\n                    const baseAngle = index % 12 * angleStep + layer * 15; // 每层错开15度\n                    const radius = Math.min(25 + layer * 8, 35); // 限制最大半径，确保不超出屏幕\n                    // 添加一些随机偏移，但保持在安全范围内\n                    const randomOffsetX = (index * 17 % 21 - 10) * 0.5; // -5 to 5\n                    const randomOffsetY = (index * 13 % 21 - 10) * 0.5; // -5 to 5\n                    const x = Math.cos(baseAngle * Math.PI / 180) * radius + randomOffsetX;\n                    const y = Math.sin(baseAngle * Math.PI / 180) * radius * 0.6 + randomOffsetY; // 压扁椭圆\n                    return {\n                        x: Math.max(-35, Math.min(35, x)),\n                        y: Math.max(-25, Math.min(25, y)),\n                        rotation: (index * 23 % 60 - 30) * 0.7 // 减小旋转角度\n                    };\n                }\n            }[\"CardSelection.useEffect.positions\"]);\n            setShuffledPositions(positions);\n        }\n    }[\"CardSelection.useEffect\"], []);\n    const handleCardClick = (card)=>{\n        // 检查是否已达到最大选择数量\n        if (selectedCards.length >= 3) return;\n        // 检查卡牌是否已被选择，防止重复选择\n        if (isCardSelected(card)) return;\n        const newSelectedCards = [\n            ...selectedCards,\n            card\n        ];\n        setSelectedCards(newSelectedCards);\n        if (newSelectedCards.length === 3) {\n            // 延迟一下让用户看到第三张卡的选择效果\n            setTimeout(()=>{\n                onCardsSelected(newSelectedCards);\n            }, 500);\n        }\n    };\n    const isCardSelected = (card)=>{\n        return selectedCards.some((selected)=>selected.id === card.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full min-h-screen overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(147,51,234,0.1)_0%,transparent_70%)]\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(59,130,246,0.1)_0%,transparent_50%)]\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 md:top-8 left-1/2 transform -translate-x-1/2 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/10 backdrop-blur-sm rounded-full px-4 md:px-6 py-2 md:py-3 border border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-sm md:text-lg font-semibold\",\n                        children: [\n                            t('selectedCards', {\n                                count: selectedCards.length\n                            }),\n                            \" / 3\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-16 md:top-20 left-1/2 transform -translate-x-1/2 z-20 flex gap-2 md:gap-4\",\n                children: [\n                    0,\n                    1,\n                    2\n                ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-18 md:w-16 md:h-24 rounded-lg border-2 border-dashed \".concat(selectedCards[index] ? 'border-purple-400 bg-purple-400/20' : 'border-purple-600/50 bg-purple-600/10', \" flex items-center justify-center\"),\n                        children: selectedCards[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                scale: 0,\n                                rotate: 180\n                            },\n                            animate: {\n                                scale: 1,\n                                rotate: 0\n                            },\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TarotCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                card: selectedCards[index],\n                                isRevealed: false,\n                                size: \"small\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 15\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full max-w-6xl h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: allCards.map((card, index)=>{\n                            const position = shuffledPositions[index];\n                            if (!position) return null;\n                            const isSelected = isCardSelected(card);\n                            const isDisabled = selectedCards.length >= 3 && !isSelected;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute \".concat(isSelected ? 'cursor-default' : isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'),\n                                style: {\n                                    left: \"\".concat(50 + position.x, \"%\"),\n                                    top: \"\".concat(50 + position.y, \"%\"),\n                                    transform: \"translate(-50%, -50%) rotate(\".concat(position.rotation, \"deg)\"),\n                                    zIndex: isSelected ? 15 : 10 - Math.floor(index / 12)\n                                },\n                                initial: {\n                                    scale: 0,\n                                    opacity: 0,\n                                    rotate: position.rotation + 180\n                                },\n                                animate: {\n                                    scale: isSelected ? 1.1 : isDisabled ? 0.7 : 1,\n                                    opacity: isSelected ? 1 : isDisabled ? 0.3 : 1,\n                                    rotate: position.rotation\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.01,\n                                    type: \"tween\",\n                                    ease: \"easeOut\"\n                                },\n                                whileHover: !isDisabled && !isSelected ? {\n                                    scale: 1.1,\n                                    rotate: 0,\n                                    zIndex: 20,\n                                    y: -10\n                                } : {},\n                                onClick: ()=>!isDisabled && !isSelected && handleCardClick(card),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TarotCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    card: card,\n                                    isRevealed: false,\n                                    isSelected: isSelected,\n                                    size: \"medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 19\n                                }, this)\n                            }, card.id, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"bg-white/10 backdrop-blur-sm rounded-2xl px-8 py-4 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white text-lg mb-2\",\n                            children: [\n                                selectedCards.length === 0 && t('selectFirstCard'),\n                                selectedCards.length === 1 && t('selectSecondCard'),\n                                selectedCards.length === 2 && t('selectThirdCard'),\n                                selectedCards.length === 3 && t('cardsComplete')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-purple-200 text-sm\",\n                            children: t('selectCardsDescription')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\CardSelection.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(CardSelection, \"4lHri/AtiRymmPzXDGA6jIOKrPE=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c = CardSelection;\nvar _c;\n$RefreshReg$(_c, \"CardSelection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CardSelection.tsx\n"));

/***/ })

});