import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { locales } from '@/i18n';
import StructuredData from '@/components/StructuredData';
import Analytics from '@/components/Analytics';
import "../globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// 动态生成metadata
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;

  if (locale === 'zh') {
    return {
      title: "神秘塔罗 - 免费AI塔罗牌占卜 | Mystic Tarot",
      description: "免费在线AI塔罗牌占卜，通过古老的塔罗牌智慧探索你的内心世界。专业的心理塔罗解读，无需注册即可使用。",
      keywords: "塔罗牌占卜,AI塔罗,免费塔罗,心理塔罗,在线占卜,神秘塔罗",
      openGraph: {
        title: "神秘塔罗 - 免费AI塔罗牌占卜",
        description: "免费在线AI塔罗牌占卜，探索你的内心世界",
        url: "https://tarotgo.top/zh",
        siteName: "Mystic Tarot",
        locale: "zh_CN",
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title: "神秘塔罗 - 免费AI塔罗牌占卜",
        description: "免费在线AI塔罗牌占卜，探索你的内心世界",
      },
      alternates: {
        canonical: "https://tarotgo.top/zh",
        languages: {
          'en': "https://tarotgo.top/en",
          'zh': "https://tarotgo.top/zh",
        },
      },
    };
  }

  return {
    title: "Mystic Tarot - Free AI Tarot Reading Online",
    description: "Free AI tarot reading online. Explore your inner world through ancient tarot wisdom. Professional psychic tarot reading app for browser, no registration required.",
    keywords: "Mystic Tarot,free ai tarot reading,ai tarot reading,psychic reading,psychic tarot reading,psychic tarot reading online,psychic tarot reading app for browser",
    openGraph: {
      title: "Mystic Tarot - Free AI Tarot Reading Online",
      description: "Free AI tarot reading online. Explore your inner world through ancient tarot wisdom.",
      url: "https://tarotgo.top/en",
      siteName: "Mystic Tarot",
      locale: "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: "Mystic Tarot - Free AI Tarot Reading Online",
      description: "Free AI tarot reading online. Explore your inner world through ancient tarot wisdom.",
    },
    alternates: {
      canonical: "https://tarotgo.top/en",
      languages: {
        'en': "https://tarotgo.top/en",
        'zh': "https://tarotgo.top/zh",
      },
    },
  };
}

interface RootLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function RootLayout({
  children,
  params
}: RootLayoutProps) {
  const { locale } = await params;

  // 验证locale
  if (!locales.includes(locale as any)) {
    notFound();
  }

  // 获取消息
  const messages = await getMessages({ locale });

  return (
    <html lang={locale}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        <NextIntlClientProvider messages={messages} locale={locale}>
          <StructuredData type="website" />
          {children}
        </NextIntlClientProvider>
        <Analytics />
      </body>
    </html>
  );
}

// 生成静态参数
export function generateStaticParams() {
  return [{ locale: 'en' }, { locale: 'zh' }];
}
