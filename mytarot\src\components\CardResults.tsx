'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Send, RotateCcw, <PERSON>rkles, Loader2, Co<PERSON>, FileText, CheckCircle } from 'lucide-react';
import TarotCard from './TarotCard';
import { TarotCard as TarotCardType } from '@/types/tarot';
import { useTarotReading } from '@/hooks/useTarotReading';
import { useTranslations, useLocale } from 'next-intl';

interface CardResultsProps {
  selectedCards: TarotCardType[];
  onNewReading: () => void;
}

export default function CardResults({ selectedCards, onNewReading }: CardResultsProps) {
  const [question, setQuestion] = useState('');
  const [isRevealed, setIsRevealed] = useState(false);
  const [cardOrientations, setCardOrientations] = useState<boolean[]>([]);
  const [currentRevealIndex, setCurrentRevealIndex] = useState(-1);
  const [showAIReading, setShowAIReading] = useState(false);

  // 提示词相关状态
  const [prompt, setPrompt] = useState('');
  const [showPrompt, setShowPrompt] = useState(false);
  const [isGeneratingPrompt, setIsGeneratingPrompt] = useState(false);
  const [promptCopied, setPromptCopied] = useState(false);

  const t = useTranslations('reading');
  const locale = useLocale();

  const { reading, isLoading, error, streamReading } = useTarotReading();

  useEffect(() => {
    // 使用卡牌ID的哈希值来确定正逆位，避免hydration错误
    const orientations = selectedCards.map((card, index) => {
      const hash = card.id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
      return (hash + index) % 2 === 0;
    });
    setCardOrientations(orientations);
  }, [selectedCards]);

  // 生成提示词
  const handleGeneratePrompt = async () => {
    if (!question.trim()) {
      alert(locale === 'zh' ? '请先输入您的问题' : 'Please enter your question first');
      return;
    }

    setIsGeneratingPrompt(true);
    try {
      // 准备卡牌数据，包含正逆位信息
      const cardsWithOrientation = selectedCards.map((card, index) => ({
        ...card,
        isReversed: cardOrientations[index]
      }));

      const response = await fetch('/api/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cards: cardsWithOrientation,
          question,
          locale
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate prompt');
      }

      const data = await response.json();
      setPrompt(data.prompt);
      setShowPrompt(true);
    } catch (error) {
      console.error('生成提示词失败:', error);
      alert(locale === 'zh' ? '生成提示词失败，请重试' : 'Failed to generate prompt, please try again');
    } finally {
      setIsGeneratingPrompt(false);
    }
  };

  // 复制提示词
  const handleCopyPrompt = async () => {
    try {
      await navigator.clipboard.writeText(prompt);
      setPromptCopied(true);
      setTimeout(() => setPromptCopied(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
      alert(locale === 'zh' ? '复制失败，请手动复制' : 'Copy failed, please copy manually');
    }
  };

  const handleGetAIReading = async () => {
    if (!question.trim()) {
      alert(locale === 'zh' ? '请先输入您的问题' : 'Please enter your question first');
      return;
    }

    // 准备卡牌数据，包含正逆位信息
    const cardsWithOrientation = selectedCards.map((card, index) => ({
      ...card,
      isReversed: cardOrientations[index]
    }));

    setShowAIReading(true);
    await streamReading(cardsWithOrientation, question, locale);
  };

  const handleRevealCards = () => {
    if (!question.trim()) {
      alert(locale === 'zh' ? '请先输入您的问题' : 'Please enter your question first');
      return;
    }

    setIsRevealed(true);
    // 逐张翻牌动画
    selectedCards.forEach((_, index) => {
      setTimeout(() => {
        setCurrentRevealIndex(index);
      }, index * 800);
    });
  };

  const cardPositions = [
    {
      title: locale === 'zh' ? '过去' : 'Past',
      subtitle: locale === 'zh' ? '来自过去的影响' : 'What influences you from the past'
    },
    {
      title: locale === 'zh' ? '现在' : 'Present',
      subtitle: locale === 'zh' ? '您当前的状况' : 'Your current situation'
    },
    {
      title: locale === 'zh' ? '未来' : 'Future',
      subtitle: locale === 'zh' ? '未来的展望' : 'What the future holds'
    }
  ];

  return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 py-12">
      <div className="container mx-auto px-6">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
              {locale === 'zh' ? '您的塔罗占卜' : 'Your Tarot Reading'}
            </h1>
            <p className="text-xl text-purple-200">
              {locale === 'zh' ? '三张牌为您指引道路' : 'Three cards to guide your path'}
            </p>
          </div>

          {/* Question Input */}
          {!isRevealed && (
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-12"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <h2 className="text-2xl font-semibold text-white mb-4 text-center">
                {t('questionPrompt')}
              </h2>
              <div className="flex gap-4">
                <input
                  type="text"
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  placeholder={t('questionPlaceholder')}
                  className="flex-1 px-6 py-4 bg-white/20 border border-purple-300/30 rounded-full text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent"
                  onKeyDown={(e) => e.key === 'Enter' && handleRevealCards()}
                />
                <motion.button
                  onClick={handleRevealCards}
                  className="px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center gap-2"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Send size={20} />
                  {t('reveal')}
                </motion.button>
              </div>
            </motion.div>
          )}

          {/* Cards Display */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {selectedCards.map((card, index) => (
              <motion.div
                key={card.id}
                className="text-center"
                initial={{ opacity: 0, y: 100 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 + index * 0.2 }}
              >
                {/* Position Title */}
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-white mb-2">
                    {cardPositions[index].title}
                  </h3>
                  <p className="text-purple-200 text-sm">
                    {cardPositions[index].subtitle}
                  </p>
                </div>

                {/* Card */}
                <div className="flex justify-center mb-6">
                  <motion.div
                    className="relative"
                    animate={{
                      scale: currentRevealIndex >= index ? 1.1 : 1
                    }}
                    transition={{ duration: 0.8 }}
                  >
                    <TarotCard
                      card={card}
                      isRevealed={isRevealed && currentRevealIndex >= index}
                      isReversed={cardOrientations[index]}
                      size="large"
                    />
                  </motion.div>
                </div>

                {/* Card Info */}
                {isRevealed && currentRevealIndex >= index && (
                  <motion.div
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-6"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    <h4 className="text-xl font-semibold text-white mb-2">
                      {locale === 'zh' ? card.name : card.nameEn}
                      {cardOrientations[index] && (
                        <span className="text-purple-300 text-sm ml-2">({t('reversed')})</span>
                      )}
                    </h4>
                    <p className="text-purple-200 text-sm mb-3">
                      {locale === 'zh' ? card.keywords.join(', ') : card.keywordsEn.join(', ')}
                    </p>
                    <p className="text-white text-sm leading-relaxed">
                      {cardOrientations[index]
                        ? (locale === 'zh' ? card.reversedMeaning : card.reversedMeaningEn)
                        : (locale === 'zh' ? card.meaning : card.meaningEn)
                      }
                    </p>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>

          {/* Question Display */}
          {isRevealed && question && (
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8 text-center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2.5 }}
            >
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center justify-center gap-2">
                <Sparkles size={24} />
                {t('yourQuestion')}
              </h3>
              <p className="text-purple-200 text-lg italic">
                "{question}"
              </p>
            </motion.div>
          )}

          {/* Generate Prompt Button */}
          {isRevealed && currentRevealIndex >= 2 && !showPrompt && (
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 3 }}
            >
              <motion.button
                onClick={handleGeneratePrompt}
                disabled={isGeneratingPrompt}
                className="px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-full hover:from-green-700 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center gap-2 mx-auto disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={{ scale: isGeneratingPrompt ? 1 : 1.05 }}
                whileTap={{ scale: isGeneratingPrompt ? 1 : 0.95 }}
              >
                {isGeneratingPrompt ? (
                  <>
                    <Loader2 size={20} className="animate-spin" />
                    {locale === 'zh' ? '生成中...' : 'Generating...'}
                  </>
                ) : (
                  <>
                    <FileText size={20} />
                    {locale === 'zh' ? '生成提示词' : 'Generate Prompt'}
                  </>
                )}
              </motion.button>
            </motion.div>
          )}

          {/* Prompt Display */}
          {showPrompt && prompt && (
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-semibold text-white flex items-center gap-2">
                  <FileText size={28} />
                  {locale === 'zh' ? '塔罗牌提示词' : 'Tarot Prompt'}
                </h3>
                <motion.button
                  onClick={handleCopyPrompt}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2 ${
                    promptCopied
                      ? 'bg-green-600 text-white'
                      : 'bg-white/20 text-white hover:bg-white/30'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {promptCopied ? (
                    <>
                      <CheckCircle size={16} />
                      {locale === 'zh' ? '已复制' : 'Copied'}
                    </>
                  ) : (
                    <>
                      <Copy size={16} />
                      {locale === 'zh' ? '复制' : 'Copy'}
                    </>
                  )}
                </motion.button>
              </div>

              <div className="bg-black/30 rounded-lg p-4 mb-4">
                <pre className="text-purple-100 text-sm leading-relaxed whitespace-pre-wrap font-mono">
                  {prompt}
                </pre>
              </div>

              <div className="text-center text-purple-200 text-sm">
                {locale === 'zh'
                  ? '您可以复制此提示词，在其他AI工具中使用以获得塔罗牌解读'
                  : 'You can copy this prompt and use it with other AI tools to get tarot readings'
                }
              </div>
            </motion.div>
          )}

          {/* AI Reading Buttons */}
          {showPrompt && !showAIReading && (
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <motion.button
                onClick={handleGetAIReading}
                disabled={isLoading}
                className="px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-full hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center gap-2 mx-auto disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={{ scale: isLoading ? 1 : 1.05 }}
                whileTap={{ scale: isLoading ? 1 : 0.95 }}
              >
                {isLoading ? (
                  <>
                    <Loader2 size={20} className="animate-spin" />
                    {locale === 'zh' ? '解读中...' : 'Reading...'}
                  </>
                ) : (
                  <>
                    <Sparkles size={20} />
                    {locale === 'zh' ? '获取AI解读' : 'Get AI Reading'}
                  </>
                )}
              </motion.button>

              {/* 免费服务说明 */}
              <div className="mt-4 text-center">
                <p className="text-purple-200 text-sm max-w-2xl mx-auto">
                  {locale === 'zh'
                    ? '💡 该服务完全免费，但可能不稳定或较慢。您可以复制上方提示词使用其他大模型工具进行解答。'
                    : '💡 This service is completely free, but may be unstable or slow. You can copy the prompt above and use other AI tools for readings.'
                  }
                </p>
              </div>
            </motion.div>
          )}



          {/* AI Reading Results */}
          {showAIReading && (
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <h3 className="text-2xl font-semibold text-white mb-6 text-center flex items-center justify-center gap-2">
                <Sparkles size={28} />
                {t('aiTarotReading')}
              </h3>

              {error ? (
                <div className="border rounded-lg p-4 mb-4 bg-red-500/20 border-red-500/30">
                  <p className="text-red-200">
                    {locale === 'zh' ? '错误：' : 'Error:'} {error}
                  </p>
                </div>
              ) : (
                <div className="prose prose-invert max-w-none">
                  <div className="text-purple-100 leading-relaxed whitespace-pre-wrap">
                    {reading || (isLoading && (locale === 'zh' ? '正在咨询宇宙智慧...' : 'Consulting the cosmic wisdom...'))}
                  </div>

                  {isLoading && (
                    <div className="flex items-center justify-center mt-4">
                      <Loader2 size={24} className="animate-spin text-purple-400" />
                    </div>
                  )}
                </div>
              )}
            </motion.div>
          )}

          {/* New Reading Button */}
          <div className="text-center">
            <motion.button
              onClick={onNewReading}
              className="px-6 py-3 bg-white/20 text-white font-semibold rounded-full hover:bg-white/30 transition-all duration-300 flex items-center gap-2 mx-auto"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <RotateCcw size={20} />
              {locale === 'zh' ? '新的占卜' : 'New Reading'}
            </motion.button>
          </div>
        </motion.div>
      </div>
      </div>
  );
}
