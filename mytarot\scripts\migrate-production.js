const { execSync } = require('child_process');
const path = require('path');

// 设置生产环境变量
process.env.DATABASE_URL = "prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19XNmY3Q1YxZ1VxZ2FEQm5LNDBEbmkiLCJhcGlfa2V5IjoiMDFLMUpEOFE1MDdOWjNETUVBRFZZUTFXNFYiLCJ0ZW5hbnRfaWQiOiI2NjAwMGJlMGVmMDFlYmQyZDNjMWY1NTVkMGFkODJhZjc3M2EyMzlkMmNkMzIzNWIzMjg3ODYzMDY0NmE3MDBiIiwiaW50ZXJuYWxfc2VjcmV0IjoiMTVlZDcwMmEtN2M2Mi00M2FmLTlmNjYtZmE3ODY2NmFmOGIwIn0.U5cXSRaBwwcO7W2JI1j5mSVa5etgOF1hD95sPh_LwFI";

process.env.DIRECT_URL = "postgres://66000be0ef01ebd2d3c1f555d0ad82af773a239d2cd3235b32878630646a700b:<EMAIL>:5432/?sslmode=require";

try {
  console.log('🚀 开始生产环境数据库迁移...');
  
  console.log('🔧 生成 Prisma 客户端...');
  execSync('npx prisma generate --schema=prisma/schema.production.prisma', { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  
  console.log('🗄️ 推送数据库 schema...');
  execSync('npx prisma db push --schema=prisma/schema.production.prisma --accept-data-loss', { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  
  console.log('✅ 数据库迁移完成！');
  
} catch (error) {
  console.error('❌ 数据库迁移失败:', error.message);
  process.exit(1);
}
