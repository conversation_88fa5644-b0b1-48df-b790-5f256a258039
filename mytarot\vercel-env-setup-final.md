# Vercel 环境变量最终配置

## 必需的环境变量配置

请在 Vercel Dashboard → Settings → Environment Variables 中添加以下变量：

### 1. 数据库配置 (Prisma + Accelerate)
```bash
DATABASE_URL=prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19XNmY3Q1YxZ1VxZ2FEQm5LNDBEbmkiLCJhcGlfa2V5IjoiMDFLMUpEOFE1MDdOWjNETUVBRFZZUTFXNFYiLCJ0ZW5hbnRfaWQiOiI2NjAwMGJlMGVmMDFlYmQyZDNjMWY1NTVkMGFkODJhZjc3M2EyMzlkMmNkMzIzNWIzMjg3ODYzMDY0NmE3MDBiIiwiaW50ZXJuYWxfc2VjcmV0IjoiMTVlZDcwMmEtN2M2Mi00M2FmLTlmNjYtZmE3ODY2NmFmOGIwIn0.U5cXSRaBwwcO7W2JI1j5mSVa5etgOF1hD95sPh_LwFI

DIRECT_URL=postgres://66000be0ef01ebd2d3c1f555d0ad82af773a239d2cd3235b32878630646a700b:<EMAIL>:5432/?sslmode=require
```

### 2. NextAuth 配置
```bash
NEXTAUTH_URL=https://your-app-name.vercel.app
NEXTAUTH_SECRET=mystic-tarot-secret-key-2024
```

### 3. OAuth 配置
```bash
GOOGLE_CLIENT_ID=265729365221-er2q8kuk589k7hln7m8cb5m61okr4eiv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

GITHUB_ID=********************
GITHUB_SECRET=****************************************
```

### 4. 邮件配置
```bash
EMAIL_SERVER_HOST=smtp.163.com
EMAIL_SERVER_PORT=465
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=FDk6ZzSiftf7XZ8K
EMAIL_FROM=<EMAIL>
```

### 5. OpenAI 配置
```bash
OPENAI_API_KEY=sk-IslnGB05pQQBD9y9d8miirlBxBNVovnPNb2rHcg8MlYoYJzF
OPENAI_BASE_URL=https://api.chatanywhere.tech/v1
```

### 6. Creem 支付配置
```bash
CREEM_API_KEY=creem_test_3GlwT5RzjwOF2jCOsQMmGY
CREEM_PRODUCT_ID=prod_5f1A39IxIXP08P1qyLBoPK
NEXT_PUBLIC_CREEM_API_KEY=creem_test_3GlwT5RzjwOF2jCOsQMmGY
NEXT_PUBLIC_CREEM_PRODUCT_ID=prod_5f1A39IxIXP08P1qyLBoPK
```

## 重要提醒

1. **NEXTAUTH_URL** 必须替换为你的实际 Vercel 域名
2. 所有环境变量都要设置为 **Production, Preview, Development**
3. 设置完环境变量后需要重新部署

## OAuth 回调 URL 更新

### Google Cloud Console
- 回调 URL: `https://your-app-name.vercel.app/api/auth/callback/google`

### GitHub OAuth App  
- 回调 URL: `https://your-app-name.vercel.app/api/auth/callback/github`

## 部署步骤

1. 在 Vercel Dashboard 中配置上述所有环境变量
2. 提交代码到 GitHub:
   ```bash
   git add .
   git commit -m "配置 Vercel Postgres 和环境变量"
   git push origin main
   ```
3. Vercel 会自动重新部署
4. 部署完成后测试所有功能
