'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useIPLanguage } from '@/hooks/useIPLanguage';

interface LanguageContextType {
  language: 'zh' | 'en';
  isLoading: boolean;
  setLanguage: (lang: 'zh' | 'en') => void;
  t: (key: string, zhText: string, enText: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const { language, isLoading, setLanguage } = useIPLanguage();

  // 简单的翻译函数
  const t = (key: string, zhText: string, enText: string) => {
    return language === 'zh' ? zhText : enText;
  };

  return (
    <LanguageContext.Provider value={{ language, isLoading, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
