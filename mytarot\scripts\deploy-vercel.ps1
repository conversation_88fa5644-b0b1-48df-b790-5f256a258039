# Vercel 部署脚本
# 使用方法: .\scripts\deploy-vercel.ps1

Write-Host "🚀 开始 Vercel 部署流程..." -ForegroundColor Green

# 检查是否安装了 Vercel CLI
try {
    $vercelVersion = vercel --version
    Write-Host "✅ Vercel CLI 已安装: $vercelVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 未找到 Vercel CLI，正在安装..." -ForegroundColor Yellow
    npm install -g vercel
}

# 检查是否已登录
Write-Host "🔐 检查 Vercel 登录状态..." -ForegroundColor Blue
try {
    $whoami = vercel whoami
    Write-Host "✅ 已登录 Vercel: $whoami" -ForegroundColor Green
} catch {
    Write-Host "❌ 未登录 Vercel，请先登录..." -ForegroundColor Yellow
    vercel login
}

# 构建项目
Write-Host "🔨 构建项目..." -ForegroundColor Blue
npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 构建失败，请检查错误信息" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 构建成功！" -ForegroundColor Green

# 部署到 Vercel
Write-Host "🚀 部署到 Vercel..." -ForegroundColor Blue
vercel --prod

Write-Host "🎉 部署完成！" -ForegroundColor Green
Write-Host "📝 请记住完成以下步骤：" -ForegroundColor Yellow
Write-Host "1. 在 Vercel Dashboard 中配置环境变量" -ForegroundColor White
Write-Host "2. 添加 Vercel Postgres 数据库" -ForegroundColor White
Write-Host "3. 运行数据库迁移" -ForegroundColor White
Write-Host "4. 更新 OAuth 应用的回调 URL" -ForegroundColor White
Write-Host "详细说明请查看 vercel-env-setup.md 文件" -ForegroundColor White
