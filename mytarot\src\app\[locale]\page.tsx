'use client';

import { useState, useEffect } from 'react';
import Navigation from '@/components/Navigation';
import HeroSection from '@/components/HeroSection';
import StarryBackground from '@/components/StarryBackground';
import Footer from '@/components/Footer';

export default function HomePage() {
  const [isMounted, setIsMounted] = useState(false);

  // 防止hydration错误
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 防止hydration错误，在客户端挂载前显示加载状态
  if (!isMounted) {
    return (
      <div className="min-h-screen relative overflow-hidden bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <StarryBackground />
        <div className="text-white text-xl relative z-10">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900">
      <StarryBackground />
      <Navigation />
      <HeroSection />
      <Footer />
    </div>
  );
}
