{"c": ["app/[locale]/layout", "app/[locale]/reading/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shuffle.js", "(app-pages-browser)/./node_modules/next/dist/api/image.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%5C%5Cchuhai%5C%5Ctarot_new%5C%5Cmytarot%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Creading%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/client/image-component.js", "(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-mode.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-blur-svg.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/side-effect.js", "(app-pages-browser)/./src/app/[locale]/reading/page.tsx", "(app-pages-browser)/./src/components/CardResults.tsx", "(app-pages-browser)/./src/components/CardSelection.tsx", "(app-pages-browser)/./src/components/ShuffleAnimation.tsx", "(app-pages-browser)/./src/components/TarotCard.tsx", "(app-pages-browser)/./src/contexts/LanguageContext.tsx", "(app-pages-browser)/./src/data/generate-cards.ts", "(app-pages-browser)/./src/hooks/useIPLanguage.ts", "(app-pages-browser)/./src/hooks/useTarotReading.ts"]}