import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // 从请求头获取客户端IP
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const clientIP = forwarded?.split(',')[0] || realIP || '';
    
    console.log('检测到客户端IP:', clientIP);

    // 如果有Vercel的地理位置头信息，直接使用
    const country = request.headers.get('x-vercel-ip-country');
    const city = request.headers.get('x-vercel-ip-city');
    
    if (country) {
      console.log('使用Vercel地理位置信息:', { country, city });
      
      return NextResponse.json({
        success: true,
        data: {
          ip: clientIP,
          country: country === 'CN' ? '中国' : country,
          countryCode: country,
          city: city || '',
          source: 'vercel-headers'
        }
      });
    }

    // 如果没有Vercel头信息，尝试调用第三方API
    if (clientIP) {
      try {
        // 使用您提供的API接口
        const response = await fetch('https://www.free-api.com/urltask', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ip: clientIP,
            fzsid: 90
          }),
          signal: AbortSignal.timeout(5000)
        });

        if (response.ok) {
          const data = await response.json();
          
          if (data.resultcode === "200" && data.result) {
            return NextResponse.json({
              success: true,
              data: {
                ip: clientIP,
                country: data.result.Country,
                countryCode: data.result.Country === '中国' ? 'CN' : 'OTHER',
                city: data.result.City || '',
                province: data.result.Province || '',
                isp: data.result.Isp || '',
                source: 'free-api'
              }
            });
          }
        }
      } catch (error) {
        console.warn('第三方API调用失败:', error);
      }

      // 备用API服务
      const backupServices = [
        {
          url: `https://ipapi.co/${clientIP}/json/`,
          parser: (data: any) => ({
            ip: clientIP,
            country: data.country_name,
            countryCode: data.country_code,
            city: data.city,
            source: 'ipapi.co'
          })
        },
        {
          url: `https://ipinfo.io/${clientIP}/json`,
          parser: (data: any) => ({
            ip: clientIP,
            country: data.country === 'CN' ? '中国' : data.country,
            countryCode: data.country,
            city: data.city,
            source: 'ipinfo.io'
          })
        }
      ];

      for (const service of backupServices) {
        try {
          const response = await fetch(service.url, {
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'TarotApp/1.0'
            },
            signal: AbortSignal.timeout(3000)
          });

          if (response.ok) {
            const data = await response.json();
            const parsed = service.parser(data);
            
            if (parsed.country) {
              return NextResponse.json({
                success: true,
                data: parsed
              });
            }
          }
        } catch (error) {
          console.warn(`备用服务 ${service.url} 失败:`, error);
          continue;
        }
      }
    }

    // 所有检测方法都失败，返回默认值
    return NextResponse.json({
      success: false,
      data: {
        ip: clientIP || 'unknown',
        country: 'unknown',
        countryCode: 'unknown',
        source: 'fallback'
      },
      message: '无法检测地理位置'
    });

  } catch (error) {
    console.error('地理位置检测错误:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
