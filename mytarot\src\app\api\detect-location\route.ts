import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // 从请求头获取客户端IP
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const clientIP = forwarded?.split(',')[0] || realIP || '';
    
    console.log('检测到客户端IP:', clientIP);

    // 如果有Vercel的地理位置头信息，直接使用
    const country = request.headers.get('x-vercel-ip-country');
    const city = request.headers.get('x-vercel-ip-city');
    
    if (country) {
      console.log('使用Vercel地理位置信息:', { country, city });
      
      return NextResponse.json({
        success: true,
        data: {
          ip: clientIP,
          country: country === 'CN' ? '中国' : country,
          countryCode: country,
          city: city || '',
          source: 'vercel-headers'
        }
      });
    }

    // 如果没有Vercel头信息，尝试调用第三方API
    if (clientIP) {
      try {
        // 使用您提供的API接口
        const response = await fetch('https://www.free-api.com/urltask', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ip: clientIP,
            fzsid: 90
          }),
          signal: AbortSignal.timeout(5000)
        });

        if (response.ok) {
          const data = await response.json();
          
          if (data.resultcode === "200" && data.result) {
            return NextResponse.json({
              success: true,
              data: {
                ip: clientIP,
                country: data.result.Country,
                countryCode: data.result.Country === '中国' ? 'CN' : 'OTHER',
                city: data.result.City || '',
                province: data.result.Province || '',
                isp: data.result.Isp || '',
                source: 'free-api'
              }
            });
          }
        }
      } catch (error) {
        console.warn('第三方API调用失败:', error);
      }

      // 使用统一的API接口
      const apis = [
        {
          url: `https://ipapi.co/${clientIP}/json/`,
          parseCountry: (data: any) => data.country_code,
          parser: (data: any) => ({
            ip: clientIP,
            country: data.country_code === 'CN' ? '中国' : data.country_name,
            countryCode: data.country_code,
            city: data.city,
            source: 'ipapi.co'
          })
        },
        {
          url: `https://ip-api.com/json/${clientIP}`,
          parseCountry: (data: any) => data.countryCode,
          parser: (data: any) => ({
            ip: clientIP,
            country: data.countryCode === 'CN' ? '中国' : data.country,
            countryCode: data.countryCode,
            city: data.city,
            source: 'ip-api.com'
          })
        },
        {
          url: `https://ipinfo.io/${clientIP}/json`,
          parseCountry: (data: any) => data.country,
          parser: (data: any) => ({
            ip: clientIP,
            country: data.country === 'CN' ? '中国' : data.country,
            countryCode: data.country,
            city: data.city,
            source: 'ipinfo.io'
          })
        }
      ];

      for (const api of apis) {
        try {
          console.log(`尝试API: ${api.url}`);

          const response = await fetch(api.url, {
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'TarotApp/1.0'
            },
            signal: AbortSignal.timeout(3000)
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`${api.url} 响应:`, data);

            const countryCode = api.parseCountry(data);

            if (countryCode) {
              const parsed = api.parser(data);

              return NextResponse.json({
                success: true,
                data: parsed
              });
            }
          }
        } catch (error) {
          console.warn(`API ${api.url} 失败:`, error);
          continue;
        }
      }
    }

    // 所有检测方法都失败，返回默认值
    return NextResponse.json({
      success: false,
      data: {
        ip: clientIP || 'unknown',
        country: 'unknown',
        countryCode: 'unknown',
        source: 'fallback'
      },
      message: '无法检测地理位置'
    });

  } catch (error) {
    console.error('地理位置检测错误:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
