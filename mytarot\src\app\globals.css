@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 自定义动画 */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 卡牌动画 */
@keyframes cardFlip {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}

.card-flip {
  animation: cardFlip 0.6s ease-in-out;
}

@keyframes shuffle {
  0%, 100% {
    transform: translateX(0) rotate(0deg);
  }
  25% {
    transform: translateX(-10px) rotate(-5deg);
  }
  75% {
    transform: translateX(10px) rotate(5deg);
  }
}

.card-shuffle {
  animation: shuffle 0.5s ease-in-out infinite;
}

/* 星空背景动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes meteor {
  0% {
    opacity: 0;
    transform: translateX(-100px) translateY(-100px) rotate(45deg);
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateX(100px) translateY(100px) rotate(45deg);
  }
}

@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spin-reverse {
  from { transform: rotate(360deg); }
  to { transform: rotate(0deg); }
}

/* 渐变背景 */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* 动画类 */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-meteor {
  animation: meteor 12s linear infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-spin-reverse {
  animation: spin-reverse 15s linear infinite;
}

/* 更多动画延迟类 */
.animation-delay-500 { animation-delay: 0.5s; }
.animation-delay-1000 { animation-delay: 1s; }
.animation-delay-1200 { animation-delay: 1.2s; }
.animation-delay-1500 { animation-delay: 1.5s; }
.animation-delay-1800 { animation-delay: 1.8s; }
.animation-delay-2200 { animation-delay: 2.2s; }
.animation-delay-2500 { animation-delay: 2.5s; }
.animation-delay-2800 { animation-delay: 2.8s; }
.animation-delay-3200 { animation-delay: 3.2s; }
.animation-delay-3500 { animation-delay: 3.5s; }
.animation-delay-3800 { animation-delay: 3.8s; }
.animation-delay-4200 { animation-delay: 4.2s; }
.animation-delay-4500 { animation-delay: 4.5s; }
.animation-delay-4800 { animation-delay: 4.8s; }
.animation-delay-5200 { animation-delay: 5.2s; }
.animation-delay-5500 { animation-delay: 5.5s; }
.animation-delay-5800 { animation-delay: 5.8s; }
.animation-delay-6000 { animation-delay: 6s; }
.animation-delay-6500 { animation-delay: 6.5s; }
.animation-delay-6800 { animation-delay: 6.8s; }
.animation-delay-8000 { animation-delay: 8s; }
