(()=>{var a={};a.id=465,a.ids=[465],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42582:(a,b,c)=>{Promise.resolve().then(c.bind(c,69254))},59697:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\app\\\\[locale]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\project\\chuhai\\tarot_new\\mytarot\\src\\app\\[locale]\\page.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69254:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s});var d=c(60687),e=c(43210),f=c(90983),g=c(85814),h=c.n(g),i=c(92576),j=c(77618),k=c(71330),l=c(56085),m=c(62688);let n=(0,m.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),o=(0,m.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);function p(){let a=(0,j.c3)("home"),b=(0,k.Ym)();return(0,d.jsx)("main",{className:"relative z-10 flex flex-col items-center justify-center min-h-[80vh] px-6 text-center",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,d.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[(0,d.jsx)(i.P.div,{className:"absolute top-20 left-20 text-purple-300",animate:{y:[0,-20,0],rotate:[0,10,0]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},children:(0,d.jsx)(l.A,{size:24})}),(0,d.jsx)(i.P.div,{className:"absolute top-40 right-32 text-pink-300",animate:{y:[0,15,0],rotate:[0,-15,0]},transition:{duration:3,repeat:1/0,ease:"easeInOut",delay:1},children:(0,d.jsx)(n,{size:20})}),(0,d.jsx)(i.P.div,{className:"absolute bottom-40 left-32 text-indigo-300",animate:{y:[0,-10,0],rotate:[0,20,0]},transition:{duration:5,repeat:1/0,ease:"easeInOut",delay:2},children:(0,d.jsx)(o,{size:28})})]}),(0,d.jsx)(i.P.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:1},children:(0,d.jsx)("h1",{className:"text-6xl md:text-8xl font-bold text-white mb-6 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent",children:a("title")})}),(0,d.jsx)(i.P.p,{className:"text-xl md:text-2xl text-purple-200 mb-4",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:1,delay:.3},children:a("subtitle")}),(0,d.jsx)(i.P.p,{className:"text-lg text-purple-300 mb-12 max-w-2xl mx-auto",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:1,delay:.6},children:a("description")}),(0,d.jsx)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8,delay:.9},children:(0,d.jsxs)(h(),{href:`/${b}/reading`,className:"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-full hover:from-purple-700 hover:to-pink-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl group",children:[(0,d.jsx)("span",{className:"mr-2",children:a("startReading")}),(0,d.jsx)(i.P.span,{animate:{x:[0,5,0]},transition:{duration:1.5,repeat:1/0},children:"✨"})]})}),(0,d.jsxs)(i.P.div,{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16",initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:1,delay:1.2},children:[(0,d.jsxs)(i.P.div,{className:"text-center",whileHover:{scale:1.05},transition:{type:"spring",stiffness:300},children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-purple-600 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD2E"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:a("features.authentic")}),(0,d.jsx)("p",{className:"text-purple-300",children:"zh"===b?"传统塔罗系统，完整78张牌组，专业塔罗牌占卜":"Traditional tarot system with complete 78-card deck for professional tarot reading"})]}),(0,d.jsxs)(i.P.div,{className:"text-center",whileHover:{scale:1.05},transition:{type:"spring",stiffness:300},children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-pink-600 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-2xl",children:"\uD83E\uDD16"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:a("features.aiPowered")}),(0,d.jsx)("p",{className:"text-purple-300",children:"zh"===b?"AI驱动的智能心理塔罗解读系统":"Intelligent psychic tarot interpretation system powered by AI"})]}),(0,d.jsxs)(i.P.div,{className:"text-center",whileHover:{scale:1.05},transition:{type:"spring",stiffness:300},children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-indigo-600 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF0D"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:a("features.multilingual")}),(0,d.jsx)("p",{className:"text-purple-300",children:"zh"===b?"支持中文和英文":"Available in English and Chinese"})]})]})]})})}var q=c(84911);function r(){let a=(0,k.Ym)();return(0,d.jsx)("footer",{className:"relative z-10 bg-black/20 backdrop-blur-sm border-t border-purple-500/20 mt-16",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"text-white text-xl font-bold mb-4",children:["\uD83D\uDD2E ","zh"===a?"神秘塔罗":"Mystic Tarot"]}),(0,d.jsx)("p",{className:"text-gray-300 text-sm",children:"zh"===a?"通过古老的塔罗牌智慧探索你的内心世界":"Explore your inner world through ancient tarot wisdom"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-white font-semibold mb-4",children:"zh"===a?"功能特色":"Features"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h(),{href:`/${a}/reading`,className:"block text-gray-300 hover:text-purple-300 transition-colors text-sm",children:"zh"===a?"塔罗解读":"Tarot Reading"}),(0,d.jsx)("div",{className:"text-gray-300 text-sm",children:"zh"===a?"✨ 完全免费":"✨ Completely Free"}),(0,d.jsx)("div",{className:"text-gray-300 text-sm",children:"zh"===a?"\uD83C\uDF0D 多语言支持":"\uD83C\uDF0D Multi-language"}),(0,d.jsx)("div",{className:"text-gray-300 text-sm",children:"zh"===a?"\uD83D\uDCF1 移动端友好":"\uD83D\uDCF1 Mobile Friendly"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-white font-semibold mb-4",children:"zh"===a?"关于我们":"About"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("p",{className:"text-gray-300 text-sm",children:"zh"===a?"我们致力于为用户提供免费、准确的塔罗牌解读服务。":"We are dedicated to providing free and accurate tarot reading services."}),(0,d.jsx)("div",{className:"text-gray-300 text-sm",children:"zh"===a?"\uD83D\uDD2E 古老智慧":"\uD83D\uDD2E Ancient Wisdom"}),(0,d.jsx)("div",{className:"text-gray-300 text-sm",children:"zh"===a?"\uD83D\uDCAB 现代科技":"\uD83D\uDCAB Modern Technology"})]})]})]}),(0,d.jsxs)("div",{className:"border-t border-purple-500/20 mt-8 pt-6 text-center",children:[(0,d.jsxs)("p",{className:"text-gray-400 text-sm",children:["\xa9 2024 Mystic Tarot. ","zh"===a?"保留所有权利。":"All rights reserved."]}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mt-2",children:"zh"===a?"完全免费，无限制使用":"Completely free, unlimited use"})]})]})})}function s(){let[a,b]=(0,e.useState)(!1);return a?(0,d.jsxs)("div",{className:"min-h-screen relative overflow-hidden bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900",children:[(0,d.jsx)(q.A,{}),(0,d.jsx)(f.A,{}),(0,d.jsx)(p,{}),(0,d.jsx)(r,{})]}):(0,d.jsxs)("div",{className:"min-h-screen relative overflow-hidden bg-gradient-to-b from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:[(0,d.jsx)(q.A,{}),(0,d.jsx)("div",{className:"text-white text-xl relative z-10",children:"Loading..."})]})}},79534:(a,b,c)=>{Promise.resolve().then(c.bind(c,59697))},79695:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["[locale]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,59697)),"D:\\project\\chuhai\\tarot_new\\mytarot\\src\\app\\[locale]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,11434)),"D:\\project\\chuhai\\tarot_new\\mytarot\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\project\\chuhai\\tarot_new\\mytarot\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\project\\chuhai\\tarot_new\\mytarot\\src\\app\\[locale]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/[locale]/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/[locale]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,24,882,939],()=>b(b.s=79695));module.exports=c})();