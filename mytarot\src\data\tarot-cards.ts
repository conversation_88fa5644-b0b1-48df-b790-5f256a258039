import { TarotCard } from '@/types/tarot';
import { generateAllCards } from './generate-cards';

// 获取所有塔罗牌（78张）
export const getAllCards = (): TarotCard[] => {
  return generateAllCards();
};

// 根据ID获取卡牌
export const getCardById = (id: string): TarotCard | undefined => {
  return getAllCards().find(card => card.id === id);
};

// 随机洗牌
export const shuffleCards = (cards: TarotCard[]): TarotCard[] => {
  const shuffled = [...cards];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};
