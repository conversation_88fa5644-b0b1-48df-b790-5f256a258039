'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { TarotCard as TarotCardType } from '@/types/tarot';
import { memo } from 'react';

interface TarotCardProps {
  card?: TarotCardType;
  isRevealed?: boolean;
  isSelected?: boolean;
  isReversed?: boolean;
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
  showBack?: boolean;
}

const TarotCard = memo(function TarotCard({
  card,
  isRevealed = false,
  isSelected = false,
  isReversed = false,
  size = 'large',
  onClick,
  className = '',
  style,
  showBack = true
}: TarotCardProps) {
  const cardBackImage = '/images/card-back.webp';

  // 根据尺寸设置样式
  const sizeClasses = {
    small: 'w-12 h-18',
    medium: 'w-16 h-24',
    large: 'w-24 h-36'
  };
  
  return (
    <motion.div
      className={`relative cursor-pointer ${sizeClasses[size]} ${className}`}
      style={style}
      onClick={onClick}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2 }}
    >
      <div className={`relative w-full h-full ${isSelected ? 'ring-4 ring-purple-400 ring-opacity-75' : ''} rounded-lg overflow-hidden shadow-lg`}>
        {/* Card Back - 只在未翻转时显示 */}
        {!isRevealed && (
          <motion.div
            className="absolute inset-0"
            initial={{ opacity: 1 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
          {showBack && (
            <Image
              src={cardBackImage}
              alt="Mystic tarot card back design - Free AI tarot reading"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority={true}
              placeholder="blur"
              blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Kcp/9k="
            />
          )}
          {!showBack && (
            <div className="w-full h-full bg-gradient-to-br from-purple-600 to-indigo-700 flex items-center justify-center">
              <div className="text-white text-4xl">🔮</div>
            </div>
          )}
          </motion.div>
        )}

        {/* Card Front - 只在需要显示时才渲染 */}
        {card && isRevealed && (
          <motion.div
            className="absolute inset-0 backface-hidden"
            initial={false}
            animate={{ rotateY: 0 }}
            transition={{ duration: 0.6 }}
            style={{
              backfaceVisibility: 'hidden'
            }}
          >
            {/* 图片容器 - 只有图片会旋转 */}
            <div className={`relative w-full h-full ${isReversed ? 'rotate-180' : ''}`}>
              <Image
                src={card.imagePath}
                alt={`${card.nameEn} tarot card - Free AI tarot reading and psychic interpretation`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                priority={isRevealed} // 只有在显示时才设置优先级
                onError={() => {
                  console.error('Image failed to load:', card.imagePath);
                  console.error('Card data:', card);
                }}
              />
            </div>

            {/* Card Info Overlay - 文字始终保持正常方向 */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3">
              <h3 className="text-white font-semibold text-sm">
                {card.nameEn}
                {isReversed && <span className="ml-2 text-yellow-400">↓</span>}
              </h3>
              <p className="text-gray-300 text-xs">
                {isReversed ? 'Reversed' : 'Upright'}
              </p>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
});

export default TarotCard;
