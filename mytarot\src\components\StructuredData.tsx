'use client';

import { useLocale } from 'next-intl';
import { useEffect, useState } from 'react';

interface StructuredDataProps {
  type?: 'website' | 'service' | 'article';
  title?: string;
  description?: string;
  url?: string;
}

export default function StructuredData({
  type = 'website',
  title,
  description,
  url
}: StructuredDataProps) {
  const locale = useLocale();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const isZh = locale === 'zh';
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://your-domain.com';

  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": type === 'website' ? "WebSite" : type === 'service' ? "Service" : "Article",
      "name": title || (isZh ? '神秘塔罗 - 免费AI塔罗牌占卜' : 'Mystic Tarot - Free AI Tarot Reading'),
      "description": description || (isZh
        ? '专业的免费在线AI塔罗牌占卜服务，通过古老的塔罗牌智慧探索你的内心世界。心理塔罗解读，无需注册即可使用。'
        : 'Professional free AI tarot reading online. Explore your inner world through ancient tarot wisdom. Psychic tarot reading with AI-powered insights, no registration required.'),
      "keywords": isZh
        ? "塔罗牌占卜,AI塔罗,免费塔罗,心理塔罗,在线占卜,神秘塔罗"
        : "Mystic Tarot,free ai tarot reading,ai tarot reading,psychic reading,psychic tarot reading,psychic tarot reading online,psychic tarot reading app for browser",
      "url": url || `${baseUrl}/${locale}`,
      "inLanguage": locale === 'zh' ? 'zh-CN' : 'en-US',
      "publisher": {
        "@type": "Organization",
        "name": isZh ? '神秘塔罗' : 'Mystic Tarot',
        "url": baseUrl
      }
    };

    if (type === 'website') {
      return {
        ...baseData,
        "potentialAction": {
          "@type": "SearchAction",
          "target": {
            "@type": "EntryPoint",
            "urlTemplate": `${baseUrl}/${locale}/reading?q={search_term_string}`
          },
          "query-input": "required name=search_term_string"
        },
        "mainEntity": {
          "@type": "Service",
          "name": isZh ? 'AI塔罗牌占卜服务' : 'AI Tarot Reading Service',
          "description": isZh
            ? '提供专业的免费在线AI塔罗牌占卜，帮助用户通过心理塔罗解读探索内心世界，获得人生指引'
            : 'Provides professional free online AI tarot readings to help users explore their inner world through psychic tarot interpretation and gain life guidance',
          "provider": {
            "@type": "Organization",
            "name": isZh ? '神秘塔罗' : 'Mystic Tarot'
          },
          "serviceType": isZh ? '占卜服务' : 'Divination Service',
          "areaServed": "Worldwide",
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": isZh ? '塔罗牌服务' : 'Tarot Services',
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": isZh ? '三张牌塔罗解读' : 'Three Card Tarot Reading',
                  "description": isZh 
                    ? '使用过去、现在、未来三张牌进行塔罗解读'
                    : 'Tarot reading using three cards representing past, present, and future'
                },
                "price": "0",
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
              }
            ]
          }
        }
      };
    }

    if (type === 'service') {
      return {
        ...baseData,
        "@type": "Service",
        "serviceType": isZh ? '塔罗牌占卜' : 'Tarot Reading',
        "provider": {
          "@type": "Organization",
          "name": isZh ? '神秘塔罗' : 'Mystic Tarot',
          "url": baseUrl
        },
        "areaServed": "Worldwide",
        "availableChannel": {
          "@type": "ServiceChannel",
          "serviceUrl": `${baseUrl}/${locale}/reading`,
          "serviceSmsNumber": null,
          "servicePhone": null,
          "serviceLocation": null
        },
        "category": isZh ? '占卜服务' : 'Divination Service',
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "validFrom": new Date().toISOString()
        }
      };
    }

    return baseData;
  };

  const structuredData = getStructuredData();

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2)
      }}
    />
  );
}
