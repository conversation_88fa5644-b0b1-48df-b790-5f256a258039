'use client';

import Head from 'next/head';
import { useLocale } from 'next-intl';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  canonical?: string;
  noindex?: boolean;
}

export default function SEOHead({
  title,
  description,
  keywords,
  canonical,
  noindex = false
}: SEOHeadProps) {
  const locale = useLocale();
  const isZh = locale === 'zh';
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://your-domain.com';

  const defaultTitle = isZh ? '神秘塔罗 - 免费在线塔罗牌占卜' : 'Mystic Tarot - Free Online Tarot Reading';
  const defaultDescription = isZh 
    ? '免费的在线塔罗牌占卜服务，通过古老的塔罗牌智慧探索你的内心世界，获得人生指引。完全免费，无需注册。'
    : 'Free online tarot reading service. Explore your inner world through ancient tarot wisdom and get life guidance. Completely free, no registration required.';
  
  const defaultKeywords = isZh
    ? '塔罗牌,占卜,免费塔罗,在线占卜,塔罗解读,命运,预测,神秘学,灵性,指引'
    : 'tarot,tarot reading,free tarot,online divination,tarot cards,fortune telling,prediction,mysticism,spirituality,guidance';

  const finalTitle = title ? `${title} | ${defaultTitle}` : defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalKeywords = keywords || defaultKeywords;
  const finalCanonical = canonical || `${baseUrl}/${locale}`;

  return (
    <Head>
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords} />
      <link rel="canonical" href={finalCanonical} />
      
      {/* 语言替代链接 */}
      <link rel="alternate" hrefLang="en" href={`${baseUrl}/en`} />
      <link rel="alternate" hrefLang="zh" href={`${baseUrl}/zh`} />
      <link rel="alternate" hrefLang="x-default" href={`${baseUrl}/en`} />
      
      {/* Open Graph */}
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:url" content={finalCanonical} />
      <meta property="og:type" content="website" />
      <meta property="og:locale" content={locale === 'zh' ? 'zh_CN' : 'en_US'} />
      <meta property="og:site_name" content={isZh ? '神秘塔罗' : 'Mystic Tarot'} />
      <meta property="og:image" content={`${baseUrl}/og-image.jpg`} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={finalTitle} />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={`${baseUrl}/og-image.jpg`} />
      
      {/* 移动端优化 */}
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      <meta name="theme-color" content="#6366f1" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content={isZh ? '神秘塔罗' : 'Mystic Tarot'} />
      
      {/* 搜索引擎指令 */}
      {noindex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      )}
      
      {/* 其他SEO标签 */}
      <meta name="author" content="Mystic Tarot" />
      <meta name="generator" content="Next.js" />
      <meta httpEquiv="Content-Language" content={locale} />
      
      {/* 预加载关键资源 */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* Favicon */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="manifest" href="/site.webmanifest" />
    </Head>
  );
}
