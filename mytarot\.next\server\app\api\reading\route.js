(()=>{var a={};a.id=258,a.ids=[258],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66789:(a,b,c)=>{"use strict";let d,e,f,g;c.r(b),c.d(b,{handler:()=>dr,patchFetch:()=>dq,routeModule:()=>dl,serverHooks:()=>dp,workAsyncStorage:()=>dm,workUnitAsyncStorage:()=>dn});var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ab,ac,ad,ae,af,ag,ah,ai,aj,ak,al,am,an,ao,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD={};c.r(aD),c.d(aD,{POST:()=>dk});var aE=c(96559),aF=c(48088),aG=c(37719),aH=c(26191),aI=c(81289),aJ=c(261),aK=c(92603),aL=c(39893),aM=c(14823),aN=c(47220),aO=c(66946),aP=c(47912),aQ=c(99786),aR=c(46143),aS=c(86439),aT=c(43365),aU=c(32190);function aV(a,b,c,d,e){if("m"===d)throw TypeError("Private method is not writable");if("a"===d&&!e)throw TypeError("Private accessor was defined without a setter");if("function"==typeof b?a!==b||!e:!b.has(a))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===d?e.call(a,c):e?e.value=c:b.set(a,c),c}function aW(a,b,c,d){if("a"===c&&!d)throw TypeError("Private accessor was defined without a getter");if("function"==typeof b?a!==b||!d:!b.has(a))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===c?d:"a"===c?d.call(a):d?d.value:b.get(a)}let aX=function(){let{crypto:a}=globalThis;if(a?.randomUUID)return aX=a.randomUUID.bind(a),a.randomUUID();let b=new Uint8Array(1),c=a?()=>a.getRandomValues(b)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,a=>(a^c()&15>>a/4).toString(16))};function aY(a){return"object"==typeof a&&null!==a&&("name"in a&&"AbortError"===a.name||"message"in a&&String(a.message).includes("FetchRequestCanceledException"))}let aZ=a=>{if(a instanceof Error)return a;if("object"==typeof a&&null!==a){try{if("[object Error]"===Object.prototype.toString.call(a)){let b=Error(a.message,a.cause?{cause:a.cause}:{});return a.stack&&(b.stack=a.stack),a.cause&&!b.cause&&(b.cause=a.cause),a.name&&(b.name=a.name),b}}catch{}try{return Error(JSON.stringify(a))}catch{}}return Error(a)};class a$ extends Error{}class a_ extends a${constructor(a,b,c,d){super(`${a_.makeMessage(a,b,c)}`),this.status=a,this.headers=d,this.requestID=d?.get("x-request-id"),this.error=b,this.code=b?.code,this.param=b?.param,this.type=b?.type}static makeMessage(a,b,c){let d=b?.message?"string"==typeof b.message?b.message:JSON.stringify(b.message):b?JSON.stringify(b):c;return a&&d?`${a} ${d}`:a?`${a} status code (no body)`:d||"(no status code or body)"}static generate(a,b,c,d){if(!a||!d)return new a1({message:c,cause:aZ(b)});let e=b?.error;return 400===a?new a3(a,e,c,d):401===a?new a4(a,e,c,d):403===a?new a5(a,e,c,d):404===a?new a6(a,e,c,d):409===a?new a7(a,e,c,d):422===a?new a8(a,e,c,d):429===a?new a9(a,e,c,d):a>=500?new ba(a,e,c,d):new a_(a,e,c,d)}}class a0 extends a_{constructor({message:a}={}){super(void 0,void 0,a||"Request was aborted.",void 0)}}class a1 extends a_{constructor({message:a,cause:b}){super(void 0,void 0,a||"Connection error.",void 0),b&&(this.cause=b)}}class a2 extends a1{constructor({message:a}={}){super({message:a??"Request timed out."})}}class a3 extends a_{}class a4 extends a_{}class a5 extends a_{}class a6 extends a_{}class a7 extends a_{}class a8 extends a_{}class a9 extends a_{}class ba extends a_{}class bb extends a${constructor(){super("Could not parse response content as the length limit was reached")}}class bc extends a${constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class bd extends Error{constructor(a){super(a)}}let be=/^[a-z][a-z0-9+.-]*:/i,bf=a=>(bf=Array.isArray)(a),bg=bf;function bh(a){return null!=a&&"object"==typeof a&&!Array.isArray(a)}let bi=a=>new Promise(b=>setTimeout(b,a)),bj="5.10.2",bk=a=>"x32"===a?"x32":"x86_64"===a||"x64"===a?"x64":"arm"===a?"arm":"aarch64"===a||"arm64"===a?"arm64":a?`other:${a}`:"unknown",bl=a=>(a=a.toLowerCase()).includes("ios")?"iOS":"android"===a?"Android":"darwin"===a?"MacOS":"win32"===a?"Windows":"freebsd"===a?"FreeBSD":"openbsd"===a?"OpenBSD":"linux"===a?"Linux":a?`Other:${a}`:"Unknown";function bm(...a){let b=globalThis.ReadableStream;if(void 0===b)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new b(...a)}function bn(a){let b=Symbol.asyncIterator in a?a[Symbol.asyncIterator]():a[Symbol.iterator]();return bm({start(){},async pull(a){let{done:c,value:d}=await b.next();c?a.close():a.enqueue(d)},async cancel(){await b.return?.()}})}function bo(a){if(a[Symbol.asyncIterator])return a;let b=a.getReader();return{async next(){try{let a=await b.read();return a?.done&&b.releaseLock(),a}catch(a){throw b.releaseLock(),a}},async return(){let a=b.cancel();return b.releaseLock(),await a,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function bp(a){if(null===a||"object"!=typeof a)return;if(a[Symbol.asyncIterator])return void await a[Symbol.asyncIterator]().return?.();let b=a.getReader(),c=b.cancel();b.releaseLock(),await c}let bq=({headers:a,body:b})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(b)}),br="RFC3986",bs=a=>String(a),bt={RFC1738:a=>String(a).replace(/%20/g,"+"),RFC3986:bs},bu=(a,b)=>(bu=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(a,b),bv=(()=>{let a=[];for(let b=0;b<256;++b)a.push("%"+((b<16?"0":"")+b.toString(16)).toUpperCase());return a})();function bw(a,b){if(bf(a)){let c=[];for(let d=0;d<a.length;d+=1)c.push(b(a[d]));return c}return b(a)}let bx={brackets:a=>String(a)+"[]",comma:"comma",indices:(a,b)=>String(a)+"["+b+"]",repeat:a=>String(a)},by=function(a,b){Array.prototype.push.apply(a,bf(b)?b:[b])},bz={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(a,b,c,d,e)=>{if(0===a.length)return a;let f=a;if("symbol"==typeof a?f=Symbol.prototype.toString.call(a):"string"!=typeof a&&(f=String(a)),"iso-8859-1"===c)return escape(f).replace(/%u[0-9a-f]{4}/gi,function(a){return"%26%23"+parseInt(a.slice(2),16)+"%3B"});let g="";for(let a=0;a<f.length;a+=1024){let b=f.length>=1024?f.slice(a,a+1024):f,c=[];for(let a=0;a<b.length;++a){let d=b.charCodeAt(a);if(45===d||46===d||95===d||126===d||d>=48&&d<=57||d>=65&&d<=90||d>=97&&d<=122||"RFC1738"===e&&(40===d||41===d)){c[c.length]=b.charAt(a);continue}if(d<128){c[c.length]=bv[d];continue}if(d<2048){c[c.length]=bv[192|d>>6]+bv[128|63&d];continue}if(d<55296||d>=57344){c[c.length]=bv[224|d>>12]+bv[128|d>>6&63]+bv[128|63&d];continue}a+=1,d=65536+((1023&d)<<10|1023&b.charCodeAt(a)),c[c.length]=bv[240|d>>18]+bv[128|d>>12&63]+bv[128|d>>6&63]+bv[128|63&d]}g+=c.join("")}return g},encodeValuesOnly:!1,format:br,formatter:bs,indices:!1,serializeDate:a=>(e??(e=Function.prototype.call.bind(Date.prototype.toISOString)))(a),skipNulls:!1,strictNullHandling:!1},bA={};function bB(a){let b;return(f??(f=(b=new globalThis.TextEncoder).encode.bind(b)))(a)}function bC(a){let b;return(g??(g=(b=new globalThis.TextDecoder).decode.bind(b)))(a)}class bD{constructor(){h.set(this,void 0),i.set(this,void 0),aV(this,h,new Uint8Array,"f"),aV(this,i,null,"f")}decode(a){let b;if(null==a)return[];let c=a instanceof ArrayBuffer?new Uint8Array(a):"string"==typeof a?bB(a):a;aV(this,h,function(a){let b=0;for(let c of a)b+=c.length;let c=new Uint8Array(b),d=0;for(let b of a)c.set(b,d),d+=b.length;return c}([aW(this,h,"f"),c]),"f");let d=[];for(;null!=(b=function(a,b){for(let c=b??0;c<a.length;c++){if(10===a[c])return{preceding:c,index:c+1,carriage:!1};if(13===a[c])return{preceding:c,index:c+1,carriage:!0}}return null}(aW(this,h,"f"),aW(this,i,"f")));){if(b.carriage&&null==aW(this,i,"f")){aV(this,i,b.index,"f");continue}if(null!=aW(this,i,"f")&&(b.index!==aW(this,i,"f")+1||b.carriage)){d.push(bC(aW(this,h,"f").subarray(0,aW(this,i,"f")-1))),aV(this,h,aW(this,h,"f").subarray(aW(this,i,"f")),"f"),aV(this,i,null,"f");continue}let a=null!==aW(this,i,"f")?b.preceding-1:b.preceding,c=bC(aW(this,h,"f").subarray(0,a));d.push(c),aV(this,h,aW(this,h,"f").subarray(b.index),"f"),aV(this,i,null,"f")}return d}flush(){return aW(this,h,"f").length?this.decode("\n"):[]}}h=new WeakMap,i=new WeakMap,bD.NEWLINE_CHARS=new Set(["\n","\r"]),bD.NEWLINE_REGEXP=/\r\n|[\n\r]/g;let bE={off:0,error:200,warn:300,info:400,debug:500},bF=(a,b,c)=>{if(a){if(Object.prototype.hasOwnProperty.call(bE,a))return a;bK(c).warn(`${b} was set to ${JSON.stringify(a)}, expected one of ${JSON.stringify(Object.keys(bE))}`)}};function bG(){}function bH(a,b,c){return!b||bE[a]>bE[c]?bG:b[a].bind(b)}let bI={error:bG,warn:bG,info:bG,debug:bG},bJ=new WeakMap;function bK(a){let b=a.logger,c=a.logLevel??"off";if(!b)return bI;let d=bJ.get(b);if(d&&d[0]===c)return d[1];let e={error:bH("error",b,c),warn:bH("warn",b,c),info:bH("info",b,c),debug:bH("debug",b,c)};return bJ.set(b,[c,e]),e}let bL=a=>(a.options&&(a.options={...a.options},delete a.options.headers),a.headers&&(a.headers=Object.fromEntries((a.headers instanceof Headers?[...a.headers]:Object.entries(a.headers)).map(([a,b])=>[a,"authorization"===a.toLowerCase()||"cookie"===a.toLowerCase()||"set-cookie"===a.toLowerCase()?"***":b]))),"retryOfRequestLogID"in a&&(a.retryOfRequestLogID&&(a.retryOf=a.retryOfRequestLogID),delete a.retryOfRequestLogID),a);class bM{constructor(a,b,c){this.iterator=a,j.set(this,void 0),this.controller=b,aV(this,j,c,"f")}static fromSSEResponse(a,b,c){let d=!1,e=c?bK(c):console;async function*f(){if(d)throw new a$("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");d=!0;let c=!1;try{for await(let d of bN(a,b))if(!c){if(d.data.startsWith("[DONE]")){c=!0;continue}if(null===d.event||d.event.startsWith("response.")||d.event.startsWith("image_edit.")||d.event.startsWith("image_generation.")||d.event.startsWith("transcript.")){let b;try{b=JSON.parse(d.data)}catch(a){throw e.error("Could not parse message into JSON:",d.data),e.error("From chunk:",d.raw),a}if(b&&b.error)throw new a_(void 0,b.error,void 0,a.headers);yield b}else{let a;try{a=JSON.parse(d.data)}catch(a){throw console.error("Could not parse message into JSON:",d.data),console.error("From chunk:",d.raw),a}if("error"==d.event)throw new a_(void 0,a.error,a.message,void 0);yield{event:d.event,data:a}}}c=!0}catch(a){if(aY(a))return;throw a}finally{c||b.abort()}}return new bM(f,b,c)}static fromReadableStream(a,b,c){let d=!1;async function*e(){let b=new bD;for await(let c of bo(a))for(let a of b.decode(c))yield a;for(let a of b.flush())yield a}return new bM(async function*(){if(d)throw new a$("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");d=!0;let a=!1;try{for await(let b of e())!a&&b&&(yield JSON.parse(b));a=!0}catch(a){if(aY(a))return;throw a}finally{a||b.abort()}},b,c)}[(j=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let a=[],b=[],c=this.iterator(),d=d=>({next:()=>{if(0===d.length){let d=c.next();a.push(d),b.push(d)}return d.shift()}});return[new bM(()=>d(a),this.controller,aW(this,j,"f")),new bM(()=>d(b),this.controller,aW(this,j,"f"))]}toReadableStream(){let a,b=this;return bm({async start(){a=b[Symbol.asyncIterator]()},async pull(b){try{let{value:c,done:d}=await a.next();if(d)return b.close();let e=bB(JSON.stringify(c)+"\n");b.enqueue(e)}catch(a){b.error(a)}},async cancel(){await a.return?.()}})}}async function*bN(a,b){if(!a.body){if(b.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new a$("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new a$("Attempted to iterate over a response with no body")}let c=new bP,d=new bD;for await(let b of bO(bo(a.body)))for(let a of d.decode(b)){let b=c.decode(a);b&&(yield b)}for(let a of d.flush()){let b=c.decode(a);b&&(yield b)}}async function*bO(a){let b=new Uint8Array;for await(let c of a){let a;if(null==c)continue;let d=c instanceof ArrayBuffer?new Uint8Array(c):"string"==typeof c?bB(c):c,e=new Uint8Array(b.length+d.length);for(e.set(b),e.set(d,b.length),b=e;-1!==(a=function(a){for(let b=0;b<a.length-1;b++){if(10===a[b]&&10===a[b+1]||13===a[b]&&13===a[b+1])return b+2;if(13===a[b]&&10===a[b+1]&&b+3<a.length&&13===a[b+2]&&10===a[b+3])return b+4}return -1}(b));)yield b.slice(0,a),b=b.slice(a)}b.length>0&&(yield b)}class bP{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(a){if(a.endsWith("\r")&&(a=a.substring(0,a.length-1)),!a){if(!this.event&&!this.data.length)return null;let a={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],a}if(this.chunks.push(a),a.startsWith(":"))return null;let[b,c,d]=function(a,b){let c=a.indexOf(":");return -1!==c?[a.substring(0,c),b,a.substring(c+b.length)]:[a,"",""]}(a,":");return d.startsWith(" ")&&(d=d.substring(1)),"event"===b?this.event=d:"data"===b&&this.data.push(d),null}}async function bQ(a,b){let{response:c,requestLogID:d,retryOfRequestLogID:e,startTime:f}=b,g=await (async()=>{if(b.options.stream)return(bK(a).debug("response",c.status,c.url,c.headers,c.body),b.options.__streamClass)?b.options.__streamClass.fromSSEResponse(c,b.controller,a):bM.fromSSEResponse(c,b.controller,a);if(204===c.status)return null;if(b.options.__binaryResponse)return c;let d=c.headers.get("content-type"),e=d?.split(";")[0]?.trim();return e?.includes("application/json")||e?.endsWith("+json")?bR(await c.json(),c):await c.text()})();return bK(a).debug(`[${d}] response parsed`,bL({retryOfRequestLogID:e,url:c.url,status:c.status,body:g,durationMs:Date.now()-f})),g}function bR(a,b){return!a||"object"!=typeof a||Array.isArray(a)?a:Object.defineProperty(a,"_request_id",{value:b.headers.get("x-request-id"),enumerable:!1})}class bS extends Promise{constructor(a,b,c=bQ){super(a=>{a(null)}),this.responsePromise=b,this.parseResponse=c,k.set(this,void 0),aV(this,k,a,"f")}_thenUnwrap(a){return new bS(aW(this,k,"f"),this.responsePromise,async(b,c)=>bR(a(await this.parseResponse(b,c),c),c.response))}asResponse(){return this.responsePromise.then(a=>a.response)}async withResponse(){let[a,b]=await Promise.all([this.parse(),this.asResponse()]);return{data:a,response:b,request_id:b.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(a=>this.parseResponse(aW(this,k,"f"),a))),this.parsedPromise}then(a,b){return this.parse().then(a,b)}catch(a){return this.parse().catch(a)}finally(a){return this.parse().finally(a)}}k=new WeakMap;class bT{constructor(a,b,c,d){l.set(this,void 0),aV(this,l,a,"f"),this.options=d,this.response=b,this.body=c}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let a=this.nextPageRequestOptions();if(!a)throw new a$("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await aW(this,l,"f").requestAPIList(this.constructor,a)}async *iterPages(){let a=this;for(yield a;a.hasNextPage();)a=await a.getNextPage(),yield a}async *[(l=new WeakMap,Symbol.asyncIterator)](){for await(let a of this.iterPages())for(let b of a.getPaginatedItems())yield b}}class bU extends bS{constructor(a,b,c){super(a,b,async(a,b)=>new c(a,b.response,await bQ(a,b),b.options))}async *[Symbol.asyncIterator](){for await(let a of(await this))yield a}}class bV extends bT{constructor(a,b,c,d){super(a,b,c,d),this.data=c.data||[],this.object=c.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class bW extends bT{constructor(a,b,c,d){super(a,b,c,d),this.data=c.data||[],this.has_more=c.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var a;let b=this.getPaginatedItems(),c=b[b.length-1]?.id;return c?{...this.options,query:{..."object"!=typeof(a=this.options.query)?{}:a??{},after:c}}:null}}let bX=()=>{if("undefined"==typeof File){let{process:a}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof a?.versions?.node&&20>parseInt(a.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function bY(a,b,c){return bX(),new File(a,b??"unknown_file",c)}function bZ(a){return("object"==typeof a&&null!==a&&("name"in a&&a.name&&String(a.name)||"url"in a&&a.url&&String(a.url)||"filename"in a&&a.filename&&String(a.filename)||"path"in a&&a.path&&String(a.path))||"").split(/[\\/]/).pop()||void 0}let b$=a=>null!=a&&"object"==typeof a&&"function"==typeof a[Symbol.asyncIterator],b_=async(a,b)=>({...a,body:await b1(a.body,b)}),b0=new WeakMap,b1=async(a,b)=>{if(!await function(a){let b="function"==typeof a?a:a.fetch,c=b0.get(b);if(c)return c;let d=(async()=>{try{let a="Response"in b?b.Response:(await b("data:,")).constructor,c=new FormData;if(c.toString()===await new a(c).text())return!1;return!0}catch{return!0}})();return b0.set(b,d),d}(b))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let c=new FormData;return await Promise.all(Object.entries(a||{}).map(([a,b])=>b4(c,a,b))),c},b2=a=>a instanceof Blob&&"name"in a,b3=a=>{if((a=>"object"==typeof a&&null!==a&&(a instanceof Response||b$(a)||b2(a)))(a))return!0;if(Array.isArray(a))return a.some(b3);if(a&&"object"==typeof a){for(let b in a)if(b3(a[b]))return!0}return!1},b4=async(a,b,c)=>{if(void 0!==c){if(null==c)throw TypeError(`Received null for "${b}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof c||"number"==typeof c||"boolean"==typeof c)a.append(b,String(c));else if(c instanceof Response)a.append(b,bY([await c.blob()],bZ(c)));else if(b$(c))a.append(b,bY([await new Response(bn(c)).blob()],bZ(c)));else if(b2(c))a.append(b,c,bZ(c));else if(Array.isArray(c))await Promise.all(c.map(c=>b4(a,b+"[]",c)));else if("object"==typeof c)await Promise.all(Object.entries(c).map(([c,d])=>b4(a,`${b}[${c}]`,d)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${c} instead`)}},b5=a=>null!=a&&"object"==typeof a&&"number"==typeof a.size&&"string"==typeof a.type&&"function"==typeof a.text&&"function"==typeof a.slice&&"function"==typeof a.arrayBuffer;async function b6(a,b,c){let d,e;if(bX(),null!=(d=a=await a)&&"object"==typeof d&&"string"==typeof d.name&&"number"==typeof d.lastModified&&b5(d))return a instanceof File?a:bY([await a.arrayBuffer()],a.name);if(null!=(e=a)&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob){let d=await a.blob();return b||(b=new URL(a.url).pathname.split(/[\\/]/).pop()),bY(await b7(d),b,c)}let f=await b7(a);if(b||(b=bZ(a)),!c?.type){let a=f.find(a=>"object"==typeof a&&"type"in a&&a.type);"string"==typeof a&&(c={...c,type:a})}return bY(f,b,c)}async function b7(a){let b=[];if("string"==typeof a||ArrayBuffer.isView(a)||a instanceof ArrayBuffer)b.push(a);else if(b5(a))b.push(a instanceof Blob?a:await a.arrayBuffer());else if(b$(a))for await(let c of a)b.push(...await b7(c));else{let b=a?.constructor?.name;throw Error(`Unexpected data type: ${typeof a}${b?`; constructor: ${b}`:""}${function(a){if("object"!=typeof a||null===a)return"";let b=Object.getOwnPropertyNames(a);return`; props: [${b.map(a=>`"${a}"`).join(", ")}]`}(a)}`)}return b}class b8{constructor(a){this._client=a}}function b9(a){return a.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let ca=Object.freeze(Object.create(null)),cb=((a=b9)=>function(b,...c){let d;if(1===b.length)return b[0];let e=!1,f=[],g=b.reduce((b,d,g)=>{/[?#]/.test(d)&&(e=!0);let h=c[g],i=(e?encodeURIComponent:a)(""+h);return g!==c.length&&(null==h||"object"==typeof h&&h.toString===Object.getPrototypeOf(Object.getPrototypeOf(h.hasOwnProperty??ca)??ca)?.toString)&&(i=h+"",f.push({start:b.length+d.length,length:i.length,error:`Value of type ${Object.prototype.toString.call(h).slice(8,-1)} is not a valid path parameter`})),b+d+(g===c.length?"":i)},""),h=g.split(/[?#]/,1)[0],i=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(d=i.exec(h));)f.push({start:d.index,length:d[0].length,error:`Value "${d[0]}" can't be safely passed as a path parameter`});if(f.sort((a,b)=>a.start-b.start),f.length>0){let a=0,b=f.reduce((b,c)=>{let d=" ".repeat(c.start-a),e="^".repeat(c.length);return a=c.start+c.length,b+d+e},"");throw new a$(`Path parameters result in path with invalid segments:
${f.map(a=>a.error).join("\n")}
${g}
${b}`)}return g})(b9);class cc extends b8{list(a,b={},c){return this._client.getAPIList(cb`/chat/completions/${a}/messages`,bW,{query:b,...c})}}let cd=a=>a?.role==="assistant",ce=a=>a?.role==="tool";class cf{constructor(){m.add(this),this.controller=new AbortController,n.set(this,void 0),o.set(this,()=>{}),p.set(this,()=>{}),q.set(this,void 0),r.set(this,()=>{}),s.set(this,()=>{}),t.set(this,{}),u.set(this,!1),v.set(this,!1),w.set(this,!1),x.set(this,!1),aV(this,n,new Promise((a,b)=>{aV(this,o,a,"f"),aV(this,p,b,"f")}),"f"),aV(this,q,new Promise((a,b)=>{aV(this,r,a,"f"),aV(this,s,b,"f")}),"f"),aW(this,n,"f").catch(()=>{}),aW(this,q,"f").catch(()=>{})}_run(a){setTimeout(()=>{a().then(()=>{this._emitFinal(),this._emit("end")},aW(this,m,"m",y).bind(this))},0)}_connected(){this.ended||(aW(this,o,"f").call(this),this._emit("connect"))}get ended(){return aW(this,u,"f")}get errored(){return aW(this,v,"f")}get aborted(){return aW(this,w,"f")}abort(){this.controller.abort()}on(a,b){return(aW(this,t,"f")[a]||(aW(this,t,"f")[a]=[])).push({listener:b}),this}off(a,b){let c=aW(this,t,"f")[a];if(!c)return this;let d=c.findIndex(a=>a.listener===b);return d>=0&&c.splice(d,1),this}once(a,b){return(aW(this,t,"f")[a]||(aW(this,t,"f")[a]=[])).push({listener:b,once:!0}),this}emitted(a){return new Promise((b,c)=>{aV(this,x,!0,"f"),"error"!==a&&this.once("error",c),this.once(a,b)})}async done(){aV(this,x,!0,"f"),await aW(this,q,"f")}_emit(a,...b){if(aW(this,u,"f"))return;"end"===a&&(aV(this,u,!0,"f"),aW(this,r,"f").call(this));let c=aW(this,t,"f")[a];if(c&&(aW(this,t,"f")[a]=c.filter(a=>!a.once),c.forEach(({listener:a})=>a(...b))),"abort"===a){let a=b[0];aW(this,x,"f")||c?.length||Promise.reject(a),aW(this,p,"f").call(this,a),aW(this,s,"f").call(this,a),this._emit("end");return}if("error"===a){let a=b[0];aW(this,x,"f")||c?.length||Promise.reject(a),aW(this,p,"f").call(this,a),aW(this,s,"f").call(this,a),this._emit("end")}}_emitFinal(){}}function cg(a){return a?.$brand==="auto-parseable-response-format"}function ch(a){return a?.$brand==="auto-parseable-tool"}function ci(a,b){let c=a.choices.map(a=>{var c,d;if("length"===a.finish_reason)throw new bb;if("content_filter"===a.finish_reason)throw new bc;return{...a,message:{...a.message,...a.message.tool_calls?{tool_calls:a.message.tool_calls?.map(a=>(function(a,b){let c=a.tools?.find(a=>a.function?.name===b.function.name);return{...b,function:{...b.function,parsed_arguments:ch(c)?c.$parseRaw(b.function.arguments):c?.function.strict?JSON.parse(b.function.arguments):null}}})(b,a))??void 0}:void 0,parsed:a.message.content&&!a.message.refusal?(c=b,d=a.message.content,c.response_format?.type!=="json_schema"?null:c.response_format?.type==="json_schema"?"$parseRaw"in c.response_format?c.response_format.$parseRaw(d):JSON.parse(d):null):null}}});return{...a,choices:c}}function cj(a){return!!cg(a.response_format)||(a.tools?.some(a=>ch(a)||"function"===a.type&&!0===a.function.strict)??!1)}n=new WeakMap,o=new WeakMap,p=new WeakMap,q=new WeakMap,r=new WeakMap,s=new WeakMap,t=new WeakMap,u=new WeakMap,v=new WeakMap,w=new WeakMap,x=new WeakMap,m=new WeakSet,y=function(a){if(aV(this,v,!0,"f"),a instanceof Error&&"AbortError"===a.name&&(a=new a0),a instanceof a0)return aV(this,w,!0,"f"),this._emit("abort",a);if(a instanceof a$)return this._emit("error",a);if(a instanceof Error){let b=new a$(a.message);return b.cause=a,this._emit("error",b)}return this._emit("error",new a$(String(a)))};class ck extends cf{constructor(){super(...arguments),z.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(a){this._chatCompletions.push(a),this._emit("chatCompletion",a);let b=a.choices[0]?.message;return b&&this._addMessage(b),a}_addMessage(a,b=!0){if("content"in a||(a.content=null),this.messages.push(a),b){if(this._emit("message",a),ce(a)&&a.content)this._emit("functionToolCallResult",a.content);else if(cd(a)&&a.tool_calls)for(let b of a.tool_calls)"function"===b.type&&this._emit("functionToolCall",b.function)}}async finalChatCompletion(){await this.done();let a=this._chatCompletions[this._chatCompletions.length-1];if(!a)throw new a$("stream ended without producing a ChatCompletion");return a}async finalContent(){return await this.done(),aW(this,z,"m",A).call(this)}async finalMessage(){return await this.done(),aW(this,z,"m",B).call(this)}async finalFunctionToolCall(){return await this.done(),aW(this,z,"m",C).call(this)}async finalFunctionToolCallResult(){return await this.done(),aW(this,z,"m",D).call(this)}async totalUsage(){return await this.done(),aW(this,z,"m",E).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let a=this._chatCompletions[this._chatCompletions.length-1];a&&this._emit("finalChatCompletion",a);let b=aW(this,z,"m",B).call(this);b&&this._emit("finalMessage",b);let c=aW(this,z,"m",A).call(this);c&&this._emit("finalContent",c);let d=aW(this,z,"m",C).call(this);d&&this._emit("finalFunctionToolCall",d);let e=aW(this,z,"m",D).call(this);null!=e&&this._emit("finalFunctionToolCallResult",e),this._chatCompletions.some(a=>a.usage)&&this._emit("totalUsage",aW(this,z,"m",E).call(this))}async _createChatCompletion(a,b,c){let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aW(this,z,"m",F).call(this,b);let e=await a.chat.completions.create({...b,stream:!1},{...c,signal:this.controller.signal});return this._connected(),this._addChatCompletion(ci(e,b))}async _runChatCompletion(a,b,c){for(let a of b.messages)this._addMessage(a,!1);return await this._createChatCompletion(a,b,c)}async _runTools(a,b,c){let d="tool",{tool_choice:e="auto",stream:f,...g}=b,h="string"!=typeof e&&e?.function?.name,{maxChatCompletions:i=10}=c||{},j=b.tools.map(a=>{if(ch(a)){if(!a.$callback)throw new a$("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:a.$callback,name:a.function.name,description:a.function.description||"",parameters:a.function.parameters,parse:a.$parseRaw,strict:!0}}}return a}),k={};for(let a of j)"function"===a.type&&(k[a.function.name||a.function.function.name]=a.function);let l="tools"in b?j.map(a=>"function"===a.type?{type:"function",function:{name:a.function.name||a.function.function.name,parameters:a.function.parameters,description:a.function.description,strict:a.function.strict}}:a):void 0;for(let a of b.messages)this._addMessage(a,!1);for(let b=0;b<i;++b){let b=await this._createChatCompletion(a,{...g,tool_choice:e,tools:l,messages:[...this.messages]},c),f=b.choices[0]?.message;if(!f)throw new a$("missing message in ChatCompletion response");if(!f.tool_calls?.length)break;for(let a of f.tool_calls){let b;if("function"!==a.type)continue;let c=a.id,{name:e,arguments:f}=a.function,g=k[e];if(g){if(h&&h!==e){let a=`Invalid tool_call: ${JSON.stringify(e)}. ${JSON.stringify(h)} requested. Please try again`;this._addMessage({role:d,tool_call_id:c,content:a});continue}}else{let a=`Invalid tool_call: ${JSON.stringify(e)}. Available options are: ${Object.keys(k).map(a=>JSON.stringify(a)).join(", ")}. Please try again`;this._addMessage({role:d,tool_call_id:c,content:a});continue}try{b="function"==typeof g.parse?await g.parse(f):f}catch(b){let a=b instanceof Error?b.message:String(b);this._addMessage({role:d,tool_call_id:c,content:a});continue}let i=await g.function(b,this),j=aW(this,z,"m",G).call(this,i);if(this._addMessage({role:d,tool_call_id:c,content:j}),h)return}}}}z=new WeakSet,A=function(){return aW(this,z,"m",B).call(this).content??null},B=function(){let a=this.messages.length;for(;a-- >0;){let b=this.messages[a];if(cd(b))return{...b,content:b.content??null,refusal:b.refusal??null}}throw new a$("stream ended without producing a ChatCompletionMessage with role=assistant")},C=function(){for(let a=this.messages.length-1;a>=0;a--){let b=this.messages[a];if(cd(b)&&b?.tool_calls?.length)return b.tool_calls.at(-1)?.function}},D=function(){for(let a=this.messages.length-1;a>=0;a--){let b=this.messages[a];if(ce(b)&&null!=b.content&&"string"==typeof b.content&&this.messages.some(a=>"assistant"===a.role&&a.tool_calls?.some(a=>"function"===a.type&&a.id===b.tool_call_id)))return b.content}},E=function(){let a={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:b}of this._chatCompletions)b&&(a.completion_tokens+=b.completion_tokens,a.prompt_tokens+=b.prompt_tokens,a.total_tokens+=b.total_tokens);return a},F=function(a){if(null!=a.n&&a.n>1)throw new a$("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},G=function(a){return"string"==typeof a?a:void 0===a?"undefined":JSON.stringify(a)};class cl extends ck{static runTools(a,b,c){let d=new cl,e={...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"runTools"}};return d._run(()=>d._runTools(a,b,e)),d}_addMessage(a,b=!0){super._addMessage(a,b),cd(a)&&a.content&&this._emit("content",a.content)}}let cm={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,INF:384,ALL:511};class cn extends Error{}class co extends Error{}let cp=a=>(function(a,b=cm.ALL){if("string"!=typeof a)throw TypeError(`expecting str, got ${typeof a}`);if(!a.trim())throw Error(`${a} is empty`);return((a,b)=>{let c=a.length,d=0,e=a=>{throw new cn(`${a} at position ${d}`)},f=a=>{throw new co(`${a} at position ${d}`)},g=()=>(l(),d>=c&&e("Unexpected end of input"),'"'===a[d])?h():"{"===a[d]?i():"["===a[d]?j():"null"===a.substring(d,d+4)||cm.NULL&b&&c-d<4&&"null".startsWith(a.substring(d))?(d+=4,null):"true"===a.substring(d,d+4)||cm.BOOL&b&&c-d<4&&"true".startsWith(a.substring(d))?(d+=4,!0):"false"===a.substring(d,d+5)||cm.BOOL&b&&c-d<5&&"false".startsWith(a.substring(d))?(d+=5,!1):"Infinity"===a.substring(d,d+8)||cm.INFINITY&b&&c-d<8&&"Infinity".startsWith(a.substring(d))?(d+=8,1/0):"-Infinity"===a.substring(d,d+9)||cm.MINUS_INFINITY&b&&1<c-d&&c-d<9&&"-Infinity".startsWith(a.substring(d))?(d+=9,-1/0):"NaN"===a.substring(d,d+3)||cm.NAN&b&&c-d<3&&"NaN".startsWith(a.substring(d))?(d+=3,NaN):k(),h=()=>{let g=d,h=!1;for(d++;d<c&&('"'!==a[d]||h&&"\\"===a[d-1]);)h="\\"===a[d]&&!h,d++;if('"'==a.charAt(d))try{return JSON.parse(a.substring(g,++d-Number(h)))}catch(a){f(String(a))}else if(cm.STR&b)try{return JSON.parse(a.substring(g,d-Number(h))+'"')}catch(b){return JSON.parse(a.substring(g,a.lastIndexOf("\\"))+'"')}e("Unterminated string literal")},i=()=>{d++,l();let f={};try{for(;"}"!==a[d];){if(l(),d>=c&&cm.OBJ&b)return f;let e=h();l(),d++;try{let a=g();Object.defineProperty(f,e,{value:a,writable:!0,enumerable:!0,configurable:!0})}catch(a){if(cm.OBJ&b)return f;throw a}l(),","===a[d]&&d++}}catch(a){if(cm.OBJ&b)return f;e("Expected '}' at end of object")}return d++,f},j=()=>{d++;let c=[];try{for(;"]"!==a[d];)c.push(g()),l(),","===a[d]&&d++}catch(a){if(cm.ARR&b)return c;e("Expected ']' at end of array")}return d++,c},k=()=>{if(0===d){"-"===a&&cm.NUM&b&&e("Not sure what '-' is");try{return JSON.parse(a)}catch(c){if(cm.NUM&b)try{if("."===a[a.length-1])return JSON.parse(a.substring(0,a.lastIndexOf(".")));return JSON.parse(a.substring(0,a.lastIndexOf("e")))}catch(a){}f(String(c))}}let g=d;for("-"===a[d]&&d++;a[d]&&!",]}".includes(a[d]);)d++;d!=c||cm.NUM&b||e("Unterminated number literal");try{return JSON.parse(a.substring(g,d))}catch(c){"-"===a.substring(g,d)&&cm.NUM&b&&e("Not sure what '-' is");try{return JSON.parse(a.substring(g,a.lastIndexOf("e")))}catch(a){f(String(a))}}},l=()=>{for(;d<c&&" \n\r	".includes(a[d]);)d++};return g()})(a.trim(),b)})(a,cm.ALL^cm.NUM);class cq extends ck{constructor(a){super(),H.add(this),I.set(this,void 0),J.set(this,void 0),K.set(this,void 0),aV(this,I,a,"f"),aV(this,J,[],"f")}get currentChatCompletionSnapshot(){return aW(this,K,"f")}static fromReadableStream(a){let b=new cq(null);return b._run(()=>b._fromReadableStream(a)),b}static createChatCompletion(a,b,c){let d=new cq(b);return d._run(()=>d._runChatCompletion(a,{...b,stream:!0},{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createChatCompletion(a,b,c){super._createChatCompletion;let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aW(this,H,"m",L).call(this);let e=await a.chat.completions.create({...b,stream:!0},{...c,signal:this.controller.signal});for await(let a of(this._connected(),e))aW(this,H,"m",N).call(this,a);if(e.controller.signal?.aborted)throw new a0;return this._addChatCompletion(aW(this,H,"m",Q).call(this))}async _fromReadableStream(a,b){let c,d=b?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aW(this,H,"m",L).call(this),this._connected();let e=bM.fromReadableStream(a,this.controller);for await(let a of e)c&&c!==a.id&&this._addChatCompletion(aW(this,H,"m",Q).call(this)),aW(this,H,"m",N).call(this,a),c=a.id;if(e.controller.signal?.aborted)throw new a0;return this._addChatCompletion(aW(this,H,"m",Q).call(this))}[(I=new WeakMap,J=new WeakMap,K=new WeakMap,H=new WeakSet,L=function(){this.ended||aV(this,K,void 0,"f")},M=function(a){let b=aW(this,J,"f")[a.index];return b||(b={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},aW(this,J,"f")[a.index]=b),b},N=function(a){if(this.ended)return;let b=aW(this,H,"m",S).call(this,a);for(let c of(this._emit("chunk",a,b),a.choices)){let a=b.choices[c.index];null!=c.delta.content&&a.message?.role==="assistant"&&a.message?.content&&(this._emit("content",c.delta.content,a.message.content),this._emit("content.delta",{delta:c.delta.content,snapshot:a.message.content,parsed:a.message.parsed})),null!=c.delta.refusal&&a.message?.role==="assistant"&&a.message?.refusal&&this._emit("refusal.delta",{delta:c.delta.refusal,snapshot:a.message.refusal}),c.logprobs?.content!=null&&a.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:c.logprobs?.content,snapshot:a.logprobs?.content??[]}),c.logprobs?.refusal!=null&&a.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:c.logprobs?.refusal,snapshot:a.logprobs?.refusal??[]});let d=aW(this,H,"m",M).call(this,a);for(let b of(a.finish_reason&&(aW(this,H,"m",P).call(this,a),null!=d.current_tool_call_index&&aW(this,H,"m",O).call(this,a,d.current_tool_call_index)),c.delta.tool_calls??[]))d.current_tool_call_index!==b.index&&(aW(this,H,"m",P).call(this,a),null!=d.current_tool_call_index&&aW(this,H,"m",O).call(this,a,d.current_tool_call_index)),d.current_tool_call_index=b.index;for(let b of c.delta.tool_calls??[]){let c=a.message.tool_calls?.[b.index];c?.type&&(c?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:c.function?.name,index:b.index,arguments:c.function.arguments,parsed_arguments:c.function.parsed_arguments,arguments_delta:b.function?.arguments??""}):c?.type)}}},O=function(a,b){if(aW(this,H,"m",M).call(this,a).done_tool_calls.has(b))return;let c=a.message.tool_calls?.[b];if(!c)throw Error("no tool call snapshot");if(!c.type)throw Error("tool call snapshot missing `type`");if("function"===c.type){let a=aW(this,I,"f")?.tools?.find(a=>"function"===a.type&&a.function.name===c.function.name);this._emit("tool_calls.function.arguments.done",{name:c.function.name,index:b,arguments:c.function.arguments,parsed_arguments:ch(a)?a.$parseRaw(c.function.arguments):a?.function.strict?JSON.parse(c.function.arguments):null})}else c.type},P=function(a){let b=aW(this,H,"m",M).call(this,a);if(a.message.content&&!b.content_done){b.content_done=!0;let c=aW(this,H,"m",R).call(this);this._emit("content.done",{content:a.message.content,parsed:c?c.$parseRaw(a.message.content):null})}a.message.refusal&&!b.refusal_done&&(b.refusal_done=!0,this._emit("refusal.done",{refusal:a.message.refusal})),a.logprobs?.content&&!b.logprobs_content_done&&(b.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:a.logprobs.content})),a.logprobs?.refusal&&!b.logprobs_refusal_done&&(b.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:a.logprobs.refusal}))},Q=function(){if(this.ended)throw new a$("stream has ended, this shouldn't happen");let a=aW(this,K,"f");if(!a)throw new a$("request ended without sending any chunks");return aV(this,K,void 0,"f"),aV(this,J,[],"f"),function(a,b){var c;let{id:d,choices:e,created:f,model:g,system_fingerprint:h,...i}=a;return c={...i,id:d,choices:e.map(({message:b,finish_reason:c,index:d,logprobs:e,...f})=>{if(!c)throw new a$(`missing finish_reason for choice ${d}`);let{content:g=null,function_call:h,tool_calls:i,...j}=b,k=b.role;if(!k)throw new a$(`missing role for choice ${d}`);if(h){let{arguments:a,name:i}=h;if(null==a)throw new a$(`missing function_call.arguments for choice ${d}`);if(!i)throw new a$(`missing function_call.name for choice ${d}`);return{...f,message:{content:g,function_call:{arguments:a,name:i},role:k,refusal:b.refusal??null},finish_reason:c,index:d,logprobs:e}}return i?{...f,index:d,finish_reason:c,logprobs:e,message:{...j,role:k,content:g,refusal:b.refusal??null,tool_calls:i.map((b,c)=>{let{function:e,type:f,id:g,...h}=b,{arguments:i,name:j,...k}=e||{};if(null==g)throw new a$(`missing choices[${d}].tool_calls[${c}].id
${cr(a)}`);if(null==f)throw new a$(`missing choices[${d}].tool_calls[${c}].type
${cr(a)}`);if(null==j)throw new a$(`missing choices[${d}].tool_calls[${c}].function.name
${cr(a)}`);if(null==i)throw new a$(`missing choices[${d}].tool_calls[${c}].function.arguments
${cr(a)}`);return{...h,id:g,type:f,function:{...k,name:j,arguments:i}}})}}:{...f,message:{...j,content:g,role:k,refusal:b.refusal??null},finish_reason:c,index:d,logprobs:e}}),created:f,model:g,object:"chat.completion",...h?{system_fingerprint:h}:{}},b&&cj(b)?ci(c,b):{...c,choices:c.choices.map(a=>({...a,message:{...a.message,parsed:null,...a.message.tool_calls?{tool_calls:a.message.tool_calls}:void 0}}))}}(a,aW(this,I,"f"))},R=function(){let a=aW(this,I,"f")?.response_format;return cg(a)?a:null},S=function(a){var b,c,d,e;let f=aW(this,K,"f"),{choices:g,...h}=a;for(let{delta:g,finish_reason:i,index:j,logprobs:k=null,...l}of(f?Object.assign(f,h):f=aV(this,K,{...h,choices:[]},"f"),a.choices)){let a=f.choices[j];if(a||(a=f.choices[j]={finish_reason:i,index:j,message:{},logprobs:k,...l}),k)if(a.logprobs){let{content:d,refusal:e,...f}=k;Object.assign(a.logprobs,f),d&&((b=a.logprobs).content??(b.content=[]),a.logprobs.content.push(...d)),e&&((c=a.logprobs).refusal??(c.refusal=[]),a.logprobs.refusal.push(...e))}else a.logprobs=Object.assign({},k);if(i&&(a.finish_reason=i,aW(this,I,"f")&&cj(aW(this,I,"f")))){if("length"===i)throw new bb;if("content_filter"===i)throw new bc}if(Object.assign(a,l),!g)continue;let{content:h,refusal:m,function_call:n,role:o,tool_calls:p,...q}=g;if(Object.assign(a.message,q),m&&(a.message.refusal=(a.message.refusal||"")+m),o&&(a.message.role=o),n&&(a.message.function_call?(n.name&&(a.message.function_call.name=n.name),n.arguments&&((d=a.message.function_call).arguments??(d.arguments=""),a.message.function_call.arguments+=n.arguments)):a.message.function_call=n),h&&(a.message.content=(a.message.content||"")+h,!a.message.refusal&&aW(this,H,"m",R).call(this)&&(a.message.parsed=cp(a.message.content))),p)for(let{index:b,id:c,type:d,function:f,...g}of(a.message.tool_calls||(a.message.tool_calls=[]),p)){let h=(e=a.message.tool_calls)[b]??(e[b]={});Object.assign(h,g),c&&(h.id=c),d&&(h.type=d),f&&(h.function??(h.function={name:f.name??"",arguments:""})),f?.name&&(h.function.name=f.name),f?.arguments&&(h.function.arguments+=f.arguments,function(a,b){if(!a)return!1;let c=a.tools?.find(a=>a.function?.name===b.function.name);return ch(c)||c?.function.strict||!1}(aW(this,I,"f"),h)&&(h.function.parsed_arguments=cp(h.function.arguments)))}}return f},Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("chunk",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new bM(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function cr(a){return JSON.stringify(a)}class cs extends cq{static fromReadableStream(a){let b=new cs(null);return b._run(()=>b._fromReadableStream(a)),b}static runTools(a,b,c){let d=new cs(b),e={...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"runTools"}};return d._run(()=>d._runTools(a,b,e)),d}}class ct extends b8{constructor(){super(...arguments),this.messages=new cc(this._client)}create(a,b){return this._client.post("/chat/completions",{body:a,...b,stream:a.stream??!1})}retrieve(a,b){return this._client.get(cb`/chat/completions/${a}`,b)}update(a,b,c){return this._client.post(cb`/chat/completions/${a}`,{body:b,...c})}list(a={},b){return this._client.getAPIList("/chat/completions",bW,{query:a,...b})}delete(a,b){return this._client.delete(cb`/chat/completions/${a}`,b)}parse(a,b){for(let b of a.tools??[]){if("function"!==b.type)throw new a$(`Currently only \`function\` tool types support auto-parsing; Received \`${b.type}\``);if(!0!==b.function.strict)throw new a$(`The \`${b.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(a,{...b,headers:{...b?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(b=>ci(b,a))}runTools(a,b){return a.stream?cs.runTools(this._client,a,b):cl.runTools(this._client,a,b)}stream(a,b){return cq.createChatCompletion(this._client,a,b)}}ct.Messages=cc;class cu extends b8{constructor(){super(...arguments),this.completions=new ct(this._client)}}cu.Completions=ct;let cv=Symbol("brand.privateNullableHeaders"),cw=a=>{let b=new Headers,c=new Set;for(let d of a){let a=new Set;for(let[e,f]of function*(a){let b;if(!a)return;if(cv in a){let{values:b,nulls:c}=a;for(let a of(yield*b.entries(),c))yield[a,null];return}let c=!1;for(let d of(a instanceof Headers?b=a.entries():bg(a)?b=a:(c=!0,b=Object.entries(a??{})),b)){let a=d[0];if("string"!=typeof a)throw TypeError("expected header name to be a string");let b=bg(d[1])?d[1]:[d[1]],e=!1;for(let d of b)void 0!==d&&(c&&!e&&(e=!0,yield[a,null]),yield[a,d])}}(d)){let d=e.toLowerCase();a.has(d)||(b.delete(e),a.add(d)),null===f?(b.delete(e),c.add(d)):(b.append(e,f),c.delete(d))}}return{[cv]:!0,values:b,nulls:c}};class cx extends b8{create(a,b){return this._client.post("/audio/speech",{body:a,...b,headers:cw([{Accept:"application/octet-stream"},b?.headers]),__binaryResponse:!0})}}class cy extends b8{create(a,b){return this._client.post("/audio/transcriptions",b_({body:a,...b,stream:a.stream??!1,__metadata:{model:a.model}},this._client))}}class cz extends b8{create(a,b){return this._client.post("/audio/translations",b_({body:a,...b,__metadata:{model:a.model}},this._client))}}class cA extends b8{constructor(){super(...arguments),this.transcriptions=new cy(this._client),this.translations=new cz(this._client),this.speech=new cx(this._client)}}cA.Transcriptions=cy,cA.Translations=cz,cA.Speech=cx;class cB extends b8{create(a,b){return this._client.post("/batches",{body:a,...b})}retrieve(a,b){return this._client.get(cb`/batches/${a}`,b)}list(a={},b){return this._client.getAPIList("/batches",bW,{query:a,...b})}cancel(a,b){return this._client.post(cb`/batches/${a}/cancel`,b)}}class cC extends b8{create(a,b){return this._client.post("/assistants",{body:a,...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(cb`/assistants/${a}`,{...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(cb`/assistants/${a}`,{body:b,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a={},b){return this._client.getAPIList("/assistants",bW,{query:a,...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}delete(a,b){return this._client.delete(cb`/assistants/${a}`,{...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cD extends b8{create(a,b){return this._client.post("/realtime/sessions",{body:a,...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cE extends b8{create(a,b){return this._client.post("/realtime/transcription_sessions",{body:a,...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cF extends b8{constructor(){super(...arguments),this.sessions=new cD(this._client),this.transcriptionSessions=new cE(this._client)}}cF.Sessions=cD,cF.TranscriptionSessions=cE;class cG extends b8{create(a,b,c){return this._client.post(cb`/threads/${a}/messages`,{body:b,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{thread_id:d}=b;return this._client.get(cb`/threads/${d}/messages/${a}`,{...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{thread_id:d,...e}=b;return this._client.post(cb`/threads/${d}/messages/${a}`,{body:e,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(cb`/threads/${a}/messages`,bW,{query:b,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b,c){let{thread_id:d}=b;return this._client.delete(cb`/threads/${d}/messages/${a}`,{...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}class cH extends b8{retrieve(a,b,c){let{thread_id:d,run_id:e,...f}=b;return this._client.get(cb`/threads/${d}/runs/${e}/steps/${a}`,{query:f,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b,c){let{thread_id:d,...e}=b;return this._client.getAPIList(cb`/threads/${d}/runs/${a}/steps`,bW,{query:e,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}let cI=a=>void 0!==globalThis.process?globalThis.process.env?.[a]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(a)?.trim():void 0;class cJ extends cf{constructor(){super(...arguments),T.add(this),V.set(this,[]),W.set(this,{}),X.set(this,{}),Y.set(this,void 0),Z.set(this,void 0),$.set(this,void 0),_.set(this,void 0),aa.set(this,void 0),ab.set(this,void 0),ac.set(this,void 0),ad.set(this,void 0),ae.set(this,void 0)}[(V=new WeakMap,W=new WeakMap,X=new WeakMap,Y=new WeakMap,Z=new WeakMap,$=new WeakMap,_=new WeakMap,aa=new WeakMap,ab=new WeakMap,ac=new WeakMap,ad=new WeakMap,ae=new WeakMap,T=new WeakSet,Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("event",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(a){let b=new U;return b._run(()=>b._fromReadableStream(a)),b}async _fromReadableStream(a,b){let c=b?.signal;c&&(c.aborted&&this.controller.abort(),c.addEventListener("abort",()=>this.controller.abort())),this._connected();let d=bM.fromReadableStream(a,this.controller);for await(let a of d)aW(this,T,"m",af).call(this,a);if(d.controller.signal?.aborted)throw new a0;return this._addRun(aW(this,T,"m",ag).call(this))}toReadableStream(){return new bM(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(a,b,c,d){let e=new U;return e._run(()=>e._runToolAssistantStream(a,b,c,{...d,headers:{...d?.headers,"X-Stainless-Helper-Method":"stream"}})),e}async _createToolAssistantStream(a,b,c,d){let e=d?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort()));let f={...c,stream:!0},g=await a.submitToolOutputs(b,f,{...d,signal:this.controller.signal});for await(let a of(this._connected(),g))aW(this,T,"m",af).call(this,a);if(g.controller.signal?.aborted)throw new a0;return this._addRun(aW(this,T,"m",ag).call(this))}static createThreadAssistantStream(a,b,c){let d=new U;return d._run(()=>d._threadAssistantStream(a,b,{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}static createAssistantStream(a,b,c,d){let e=new U;return e._run(()=>e._runAssistantStream(a,b,c,{...d,headers:{...d?.headers,"X-Stainless-Helper-Method":"stream"}})),e}currentEvent(){return aW(this,ac,"f")}currentRun(){return aW(this,ad,"f")}currentMessageSnapshot(){return aW(this,Y,"f")}currentRunStepSnapshot(){return aW(this,ae,"f")}async finalRunSteps(){return await this.done(),Object.values(aW(this,W,"f"))}async finalMessages(){return await this.done(),Object.values(aW(this,X,"f"))}async finalRun(){if(await this.done(),!aW(this,Z,"f"))throw Error("Final run was not received.");return aW(this,Z,"f")}async _createThreadAssistantStream(a,b,c){let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort()));let e={...b,stream:!0},f=await a.createAndRun(e,{...c,signal:this.controller.signal});for await(let a of(this._connected(),f))aW(this,T,"m",af).call(this,a);if(f.controller.signal?.aborted)throw new a0;return this._addRun(aW(this,T,"m",ag).call(this))}async _createAssistantStream(a,b,c,d){let e=d?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort()));let f={...c,stream:!0},g=await a.create(b,f,{...d,signal:this.controller.signal});for await(let a of(this._connected(),g))aW(this,T,"m",af).call(this,a);if(g.controller.signal?.aborted)throw new a0;return this._addRun(aW(this,T,"m",ag).call(this))}static accumulateDelta(a,b){for(let[c,d]of Object.entries(b)){if(!a.hasOwnProperty(c)){a[c]=d;continue}let b=a[c];if(null==b||"index"===c||"type"===c){a[c]=d;continue}if("string"==typeof b&&"string"==typeof d)b+=d;else if("number"==typeof b&&"number"==typeof d)b+=d;else if(bh(b)&&bh(d))b=this.accumulateDelta(b,d);else if(Array.isArray(b)&&Array.isArray(d)){if(b.every(a=>"string"==typeof a||"number"==typeof a)){b.push(...d);continue}for(let a of d){if(!bh(a))throw Error(`Expected array delta entry to be an object but got: ${a}`);let c=a.index;if(null==c)throw console.error(a),Error("Expected array delta entry to have an `index` property");if("number"!=typeof c)throw Error(`Expected array delta entry \`index\` property to be a number but got ${c}`);let d=b[c];null==d?b.push(a):b[c]=this.accumulateDelta(d,a)}continue}else throw Error(`Unhandled record type: ${c}, deltaValue: ${d}, accValue: ${b}`);a[c]=b}return a}_addRun(a){return a}async _threadAssistantStream(a,b,c){return await this._createThreadAssistantStream(b,a,c)}async _runAssistantStream(a,b,c,d){return await this._createAssistantStream(b,a,c,d)}async _runToolAssistantStream(a,b,c,d){return await this._createToolAssistantStream(b,a,c,d)}}U=cJ,af=function(a){if(!this.ended)switch(aV(this,ac,a,"f"),aW(this,T,"m",aj).call(this,a),a.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":aW(this,T,"m",an).call(this,a);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":aW(this,T,"m",ai).call(this,a);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":aW(this,T,"m",ah).call(this,a);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},ag=function(){if(this.ended)throw new a$("stream has ended, this shouldn't happen");if(!aW(this,Z,"f"))throw Error("Final run has not been received");return aW(this,Z,"f")},ah=function(a){let[b,c]=aW(this,T,"m",al).call(this,a,aW(this,Y,"f"));for(let a of(aV(this,Y,b,"f"),aW(this,X,"f")[b.id]=b,c)){let c=b.content[a.index];c?.type=="text"&&this._emit("textCreated",c.text)}switch(a.event){case"thread.message.created":this._emit("messageCreated",a.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",a.data.delta,b),a.data.delta.content)for(let c of a.data.delta.content){if("text"==c.type&&c.text){let a=c.text,d=b.content[c.index];if(d&&"text"==d.type)this._emit("textDelta",a,d.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(c.index!=aW(this,$,"f")){if(aW(this,_,"f"))switch(aW(this,_,"f").type){case"text":this._emit("textDone",aW(this,_,"f").text,aW(this,Y,"f"));break;case"image_file":this._emit("imageFileDone",aW(this,_,"f").image_file,aW(this,Y,"f"))}aV(this,$,c.index,"f")}aV(this,_,b.content[c.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==aW(this,$,"f")){let b=a.data.content[aW(this,$,"f")];if(b)switch(b.type){case"image_file":this._emit("imageFileDone",b.image_file,aW(this,Y,"f"));break;case"text":this._emit("textDone",b.text,aW(this,Y,"f"))}}aW(this,Y,"f")&&this._emit("messageDone",a.data),aV(this,Y,void 0,"f")}},ai=function(a){let b=aW(this,T,"m",ak).call(this,a);switch(aV(this,ae,b,"f"),a.event){case"thread.run.step.created":this._emit("runStepCreated",a.data);break;case"thread.run.step.delta":let c=a.data.delta;if(c.step_details&&"tool_calls"==c.step_details.type&&c.step_details.tool_calls&&"tool_calls"==b.step_details.type)for(let a of c.step_details.tool_calls)a.index==aW(this,aa,"f")?this._emit("toolCallDelta",a,b.step_details.tool_calls[a.index]):(aW(this,ab,"f")&&this._emit("toolCallDone",aW(this,ab,"f")),aV(this,aa,a.index,"f"),aV(this,ab,b.step_details.tool_calls[a.index],"f"),aW(this,ab,"f")&&this._emit("toolCallCreated",aW(this,ab,"f")));this._emit("runStepDelta",a.data.delta,b);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":aV(this,ae,void 0,"f"),"tool_calls"==a.data.step_details.type&&aW(this,ab,"f")&&(this._emit("toolCallDone",aW(this,ab,"f")),aV(this,ab,void 0,"f")),this._emit("runStepDone",a.data,b)}},aj=function(a){aW(this,V,"f").push(a),this._emit("event",a)},ak=function(a){switch(a.event){case"thread.run.step.created":return aW(this,W,"f")[a.data.id]=a.data,a.data;case"thread.run.step.delta":let b=aW(this,W,"f")[a.data.id];if(!b)throw Error("Received a RunStepDelta before creation of a snapshot");let c=a.data;if(c.delta){let d=U.accumulateDelta(b,c.delta);aW(this,W,"f")[a.data.id]=d}return aW(this,W,"f")[a.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":aW(this,W,"f")[a.data.id]=a.data}if(aW(this,W,"f")[a.data.id])return aW(this,W,"f")[a.data.id];throw Error("No snapshot available")},al=function(a,b){let c=[];switch(a.event){case"thread.message.created":return[a.data,c];case"thread.message.delta":if(!b)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let d=a.data;if(d.delta.content)for(let a of d.delta.content)if(a.index in b.content){let c=b.content[a.index];b.content[a.index]=aW(this,T,"m",am).call(this,a,c)}else b.content[a.index]=a,c.push(a);return[b,c];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(b)return[b,c];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},am=function(a,b){return U.accumulateDelta(b,a)},an=function(a){switch(aV(this,ad,a.data,"f"),a.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":aV(this,Z,a.data,"f"),aW(this,ab,"f")&&(this._emit("toolCallDone",aW(this,ab,"f")),aV(this,ab,void 0,"f"))}};class cK extends b8{constructor(){super(...arguments),this.steps=new cH(this._client)}create(a,b,c){let{include:d,...e}=b;return this._client.post(cb`/threads/${a}/runs`,{query:{include:d},body:e,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers]),stream:b.stream??!1})}retrieve(a,b,c){let{thread_id:d}=b;return this._client.get(cb`/threads/${d}/runs/${a}`,{...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{thread_id:d,...e}=b;return this._client.post(cb`/threads/${d}/runs/${a}`,{body:e,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(cb`/threads/${a}/runs`,bW,{query:b,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}cancel(a,b,c){let{thread_id:d}=b;return this._client.post(cb`/threads/${d}/runs/${a}/cancel`,{...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b,c);return await this.poll(d.id,{thread_id:a},c)}createAndStream(a,b,c){return cJ.createAssistantStream(a,this._client.beta.threads.runs,b,c)}async poll(a,b,c){let d=cw([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:e,response:f}=await this.retrieve(a,b,{...c,headers:{...c?.headers,...d}}).withResponse();switch(e.status){case"queued":case"in_progress":case"cancelling":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=f.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await bi(g);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return e}}}stream(a,b,c){return cJ.createAssistantStream(a,this._client.beta.threads.runs,b,c)}submitToolOutputs(a,b,c){let{thread_id:d,...e}=b;return this._client.post(cb`/threads/${d}/runs/${a}/submit_tool_outputs`,{body:e,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers]),stream:b.stream??!1})}async submitToolOutputsAndPoll(a,b,c){let d=await this.submitToolOutputs(a,b,c);return await this.poll(d.id,b,c)}submitToolOutputsStream(a,b,c){return cJ.createToolAssistantStream(a,this._client.beta.threads.runs,b,c)}}cK.Steps=cH;class cL extends b8{constructor(){super(...arguments),this.runs=new cK(this._client),this.messages=new cG(this._client)}create(a={},b){return this._client.post("/threads",{body:a,...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(cb`/threads/${a}`,{...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(cb`/threads/${a}`,{body:b,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b){return this._client.delete(cb`/threads/${a}`,{...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}createAndRun(a,b){return this._client.post("/threads/runs",{body:a,...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers]),stream:a.stream??!1})}async createAndRunPoll(a,b){let c=await this.createAndRun(a,b);return await this.runs.poll(c.id,{thread_id:c.thread_id},b)}createAndRunStream(a,b){return cJ.createThreadAssistantStream(a,this._client.beta.threads,b)}}cL.Runs=cK,cL.Messages=cG;class cM extends b8{constructor(){super(...arguments),this.realtime=new cF(this._client),this.assistants=new cC(this._client),this.threads=new cL(this._client)}}cM.Realtime=cF,cM.Assistants=cC,cM.Threads=cL;class cN extends b8{create(a,b){return this._client.post("/completions",{body:a,...b,stream:a.stream??!1})}}class cO extends b8{retrieve(a,b,c){let{container_id:d}=b;return this._client.get(cb`/containers/${d}/files/${a}/content`,{...c,headers:cw([{Accept:"application/binary"},c?.headers]),__binaryResponse:!0})}}class cP extends b8{constructor(){super(...arguments),this.content=new cO(this._client)}create(a,b,c){return this._client.post(cb`/containers/${a}/files`,b_({body:b,...c},this._client))}retrieve(a,b,c){let{container_id:d}=b;return this._client.get(cb`/containers/${d}/files/${a}`,c)}list(a,b={},c){return this._client.getAPIList(cb`/containers/${a}/files`,bW,{query:b,...c})}delete(a,b,c){let{container_id:d}=b;return this._client.delete(cb`/containers/${d}/files/${a}`,{...c,headers:cw([{Accept:"*/*"},c?.headers])})}}cP.Content=cO;class cQ extends b8{constructor(){super(...arguments),this.files=new cP(this._client)}create(a,b){return this._client.post("/containers",{body:a,...b})}retrieve(a,b){return this._client.get(cb`/containers/${a}`,b)}list(a={},b){return this._client.getAPIList("/containers",bW,{query:a,...b})}delete(a,b){return this._client.delete(cb`/containers/${a}`,{...b,headers:cw([{Accept:"*/*"},b?.headers])})}}cQ.Files=cP;class cR extends b8{create(a,b){let c=!!a.encoding_format,d=c?a.encoding_format:"base64";c&&bK(this._client).debug("embeddings/user defined encoding_format:",a.encoding_format);let e=this._client.post("/embeddings",{body:{...a,encoding_format:d},...b});return c?e:(bK(this._client).debug("embeddings/decoding base64 embeddings from base64"),e._thenUnwrap(a=>(a&&a.data&&a.data.forEach(a=>{let b=a.embedding;a.embedding=(a=>{if("undefined"!=typeof Buffer){let b=Buffer.from(a,"base64");return Array.from(new Float32Array(b.buffer,b.byteOffset,b.length/Float32Array.BYTES_PER_ELEMENT))}{let b=atob(a),c=b.length,d=new Uint8Array(c);for(let a=0;a<c;a++)d[a]=b.charCodeAt(a);return Array.from(new Float32Array(d.buffer))}})(b)}),a)))}}class cS extends b8{retrieve(a,b,c){let{eval_id:d,run_id:e}=b;return this._client.get(cb`/evals/${d}/runs/${e}/output_items/${a}`,c)}list(a,b,c){let{eval_id:d,...e}=b;return this._client.getAPIList(cb`/evals/${d}/runs/${a}/output_items`,bW,{query:e,...c})}}class cT extends b8{constructor(){super(...arguments),this.outputItems=new cS(this._client)}create(a,b,c){return this._client.post(cb`/evals/${a}/runs`,{body:b,...c})}retrieve(a,b,c){let{eval_id:d}=b;return this._client.get(cb`/evals/${d}/runs/${a}`,c)}list(a,b={},c){return this._client.getAPIList(cb`/evals/${a}/runs`,bW,{query:b,...c})}delete(a,b,c){let{eval_id:d}=b;return this._client.delete(cb`/evals/${d}/runs/${a}`,c)}cancel(a,b,c){let{eval_id:d}=b;return this._client.post(cb`/evals/${d}/runs/${a}`,c)}}cT.OutputItems=cS;class cU extends b8{constructor(){super(...arguments),this.runs=new cT(this._client)}create(a,b){return this._client.post("/evals",{body:a,...b})}retrieve(a,b){return this._client.get(cb`/evals/${a}`,b)}update(a,b,c){return this._client.post(cb`/evals/${a}`,{body:b,...c})}list(a={},b){return this._client.getAPIList("/evals",bW,{query:a,...b})}delete(a,b){return this._client.delete(cb`/evals/${a}`,b)}}cU.Runs=cT;class cV extends b8{create(a,b){return this._client.post("/files",b_({body:a,...b},this._client))}retrieve(a,b){return this._client.get(cb`/files/${a}`,b)}list(a={},b){return this._client.getAPIList("/files",bW,{query:a,...b})}delete(a,b){return this._client.delete(cb`/files/${a}`,b)}content(a,b){return this._client.get(cb`/files/${a}/content`,{...b,headers:cw([{Accept:"application/binary"},b?.headers]),__binaryResponse:!0})}async waitForProcessing(a,{pollInterval:b=5e3,maxWait:c=18e5}={}){let d=new Set(["processed","error","deleted"]),e=Date.now(),f=await this.retrieve(a);for(;!f.status||!d.has(f.status);)if(await bi(b),f=await this.retrieve(a),Date.now()-e>c)throw new a2({message:`Giving up on waiting for file ${a} to finish processing after ${c} milliseconds.`});return f}}class cW extends b8{}class cX extends b8{run(a,b){return this._client.post("/fine_tuning/alpha/graders/run",{body:a,...b})}validate(a,b){return this._client.post("/fine_tuning/alpha/graders/validate",{body:a,...b})}}class cY extends b8{constructor(){super(...arguments),this.graders=new cX(this._client)}}cY.Graders=cX;class cZ extends b8{create(a,b,c){return this._client.getAPIList(cb`/fine_tuning/checkpoints/${a}/permissions`,bV,{body:b,method:"post",...c})}retrieve(a,b={},c){return this._client.get(cb`/fine_tuning/checkpoints/${a}/permissions`,{query:b,...c})}delete(a,b,c){let{fine_tuned_model_checkpoint:d}=b;return this._client.delete(cb`/fine_tuning/checkpoints/${d}/permissions/${a}`,c)}}class c$ extends b8{constructor(){super(...arguments),this.permissions=new cZ(this._client)}}c$.Permissions=cZ;class c_ extends b8{list(a,b={},c){return this._client.getAPIList(cb`/fine_tuning/jobs/${a}/checkpoints`,bW,{query:b,...c})}}class c0 extends b8{constructor(){super(...arguments),this.checkpoints=new c_(this._client)}create(a,b){return this._client.post("/fine_tuning/jobs",{body:a,...b})}retrieve(a,b){return this._client.get(cb`/fine_tuning/jobs/${a}`,b)}list(a={},b){return this._client.getAPIList("/fine_tuning/jobs",bW,{query:a,...b})}cancel(a,b){return this._client.post(cb`/fine_tuning/jobs/${a}/cancel`,b)}listEvents(a,b={},c){return this._client.getAPIList(cb`/fine_tuning/jobs/${a}/events`,bW,{query:b,...c})}pause(a,b){return this._client.post(cb`/fine_tuning/jobs/${a}/pause`,b)}resume(a,b){return this._client.post(cb`/fine_tuning/jobs/${a}/resume`,b)}}c0.Checkpoints=c_;class c1 extends b8{constructor(){super(...arguments),this.methods=new cW(this._client),this.jobs=new c0(this._client),this.checkpoints=new c$(this._client),this.alpha=new cY(this._client)}}c1.Methods=cW,c1.Jobs=c0,c1.Checkpoints=c$,c1.Alpha=cY;class c2 extends b8{}class c3 extends b8{constructor(){super(...arguments),this.graderModels=new c2(this._client)}}c3.GraderModels=c2;class c4 extends b8{createVariation(a,b){return this._client.post("/images/variations",b_({body:a,...b},this._client))}edit(a,b){return this._client.post("/images/edits",b_({body:a,...b,stream:a.stream??!1},this._client))}generate(a,b){return this._client.post("/images/generations",{body:a,...b,stream:a.stream??!1})}}class c5 extends b8{retrieve(a,b){return this._client.get(cb`/models/${a}`,b)}list(a){return this._client.getAPIList("/models",bV,a)}delete(a,b){return this._client.delete(cb`/models/${a}`,b)}}class c6 extends b8{create(a,b){return this._client.post("/moderations",{body:a,...b})}}function c7(a,b){let c=a.output.map(a=>{if("function_call"===a.type)return{...a,parsed_arguments:function(a,b){var c,d;let e=(c=a.tools??[],d=b.name,c.find(a=>"function"===a.type&&a.name===d));return{...b,...b,parsed_arguments:e?.$brand==="auto-parseable-tool"?e.$parseRaw(b.arguments):e?.strict?JSON.parse(b.arguments):null}}(b,a)};if("message"===a.type){let c=a.content.map(a=>{var c,d;return"output_text"===a.type?{...a,parsed:(c=b,d=a.text,c.text?.format?.type!=="json_schema"?null:"$parseRaw"in c.text?.format?(c.text?.format).$parseRaw(d):JSON.parse(d))}:a});return{...a,content:c}}return a}),d=Object.assign({},a,{output:c});return Object.getOwnPropertyDescriptor(a,"output_text")||c8(d),Object.defineProperty(d,"output_parsed",{enumerable:!0,get(){for(let a of d.output)if("message"===a.type){for(let b of a.content)if("output_text"===b.type&&null!==b.parsed)return b.parsed}return null}}),d}function c8(a){let b=[];for(let c of a.output)if("message"===c.type)for(let a of c.content)"output_text"===a.type&&b.push(a.text);a.output_text=b.join("")}class c9 extends cf{constructor(a){super(),ao.add(this),ap.set(this,void 0),aq.set(this,void 0),ar.set(this,void 0),aV(this,ap,a,"f")}static createResponse(a,b,c){let d=new c9(b);return d._run(()=>d._createOrRetrieveResponse(a,b,{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createOrRetrieveResponse(a,b,c){let d,e=c?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort())),aW(this,ao,"m",as).call(this);let f=null;for await(let e of("response_id"in b?(d=await a.responses.retrieve(b.response_id,{stream:!0},{...c,signal:this.controller.signal,stream:!0}),f=b.starting_after??null):d=await a.responses.create({...b,stream:!0},{...c,signal:this.controller.signal}),this._connected(),d))aW(this,ao,"m",at).call(this,e,f);if(d.controller.signal?.aborted)throw new a0;return aW(this,ao,"m",au).call(this)}[(ap=new WeakMap,aq=new WeakMap,ar=new WeakMap,ao=new WeakSet,as=function(){this.ended||aV(this,aq,void 0,"f")},at=function(a,b){if(this.ended)return;let c=(a,c)=>{(null==b||c.sequence_number>b)&&this._emit(a,c)},d=aW(this,ao,"m",av).call(this,a);switch(c("event",a),a.type){case"response.output_text.delta":{let b=d.output[a.output_index];if(!b)throw new a$(`missing output at index ${a.output_index}`);if("message"===b.type){let d=b.content[a.content_index];if(!d)throw new a$(`missing content at index ${a.content_index}`);if("output_text"!==d.type)throw new a$(`expected content to be 'output_text', got ${d.type}`);c("response.output_text.delta",{...a,snapshot:d.text})}break}case"response.function_call_arguments.delta":{let b=d.output[a.output_index];if(!b)throw new a$(`missing output at index ${a.output_index}`);"function_call"===b.type&&c("response.function_call_arguments.delta",{...a,snapshot:b.arguments});break}default:c(a.type,a)}},au=function(){if(this.ended)throw new a$("stream has ended, this shouldn't happen");let a=aW(this,aq,"f");if(!a)throw new a$("request ended without sending any events");aV(this,aq,void 0,"f");let b=function(a,b){var c;return b&&(c=b,cg(c.text?.format))?c7(a,b):{...a,output_parsed:null,output:a.output.map(a=>"function_call"===a.type?{...a,parsed_arguments:null}:"message"===a.type?{...a,content:a.content.map(a=>({...a,parsed:null}))}:a)}}(a,aW(this,ap,"f"));return aV(this,ar,b,"f"),b},av=function(a){let b=aW(this,aq,"f");if(!b){if("response.created"!==a.type)throw new a$(`When snapshot hasn't been set yet, expected 'response.created' event, got ${a.type}`);return aV(this,aq,a.response,"f")}switch(a.type){case"response.output_item.added":b.output.push(a.item);break;case"response.content_part.added":{let c=b.output[a.output_index];if(!c)throw new a$(`missing output at index ${a.output_index}`);"message"===c.type&&c.content.push(a.part);break}case"response.output_text.delta":{let c=b.output[a.output_index];if(!c)throw new a$(`missing output at index ${a.output_index}`);if("message"===c.type){let b=c.content[a.content_index];if(!b)throw new a$(`missing content at index ${a.content_index}`);if("output_text"!==b.type)throw new a$(`expected content to be 'output_text', got ${b.type}`);b.text+=a.delta}break}case"response.function_call_arguments.delta":{let c=b.output[a.output_index];if(!c)throw new a$(`missing output at index ${a.output_index}`);"function_call"===c.type&&(c.arguments+=a.delta);break}case"response.completed":aV(this,aq,a.response,"f")}return b},Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("event",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let a=aW(this,ar,"f");if(!a)throw new a$("stream ended without producing a ChatCompletion");return a}}class da extends b8{list(a,b={},c){return this._client.getAPIList(cb`/responses/${a}/input_items`,bW,{query:b,...c})}}class db extends b8{constructor(){super(...arguments),this.inputItems=new da(this._client)}create(a,b){return this._client.post("/responses",{body:a,...b,stream:a.stream??!1})._thenUnwrap(a=>("object"in a&&"response"===a.object&&c8(a),a))}retrieve(a,b={},c){return this._client.get(cb`/responses/${a}`,{query:b,...c,stream:b?.stream??!1})._thenUnwrap(a=>("object"in a&&"response"===a.object&&c8(a),a))}delete(a,b){return this._client.delete(cb`/responses/${a}`,{...b,headers:cw([{Accept:"*/*"},b?.headers])})}parse(a,b){return this._client.responses.create(a,b)._thenUnwrap(b=>c7(b,a))}stream(a,b){return c9.createResponse(this._client,a,b)}cancel(a,b){return this._client.post(cb`/responses/${a}/cancel`,b)}}db.InputItems=da;class dc extends b8{create(a,b,c){return this._client.post(cb`/uploads/${a}/parts`,b_({body:b,...c},this._client))}}class dd extends b8{constructor(){super(...arguments),this.parts=new dc(this._client)}create(a,b){return this._client.post("/uploads",{body:a,...b})}cancel(a,b){return this._client.post(cb`/uploads/${a}/cancel`,b)}complete(a,b,c){return this._client.post(cb`/uploads/${a}/complete`,{body:b,...c})}}dd.Parts=dc;let de=async a=>{let b=await Promise.allSettled(a),c=b.filter(a=>"rejected"===a.status);if(c.length){for(let a of c)console.error(a.reason);throw Error(`${c.length} promise(s) failed - see the above errors`)}let d=[];for(let a of b)"fulfilled"===a.status&&d.push(a.value);return d};class df extends b8{create(a,b,c){return this._client.post(cb`/vector_stores/${a}/file_batches`,{body:b,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{vector_store_id:d}=b;return this._client.get(cb`/vector_stores/${d}/file_batches/${a}`,{...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}cancel(a,b,c){let{vector_store_id:d}=b;return this._client.post(cb`/vector_stores/${d}/file_batches/${a}/cancel`,{...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b);return await this.poll(a,d.id,c)}listFiles(a,b,c){let{vector_store_id:d,...e}=b;return this._client.getAPIList(cb`/vector_stores/${d}/file_batches/${a}/files`,bW,{query:e,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async poll(a,b,c){let d=cw([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:e,response:f}=await this.retrieve(b,{vector_store_id:a},{...c,headers:d}).withResponse();switch(e.status){case"in_progress":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=f.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await bi(g);break;case"failed":case"cancelled":case"completed":return e}}}async uploadAndPoll(a,{files:b,fileIds:c=[]},d){if(null==b||0==b.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let e=Math.min(d?.maxConcurrency??5,b.length),f=this._client,g=b.values(),h=[...c];async function i(a){for(let b of a){let a=await f.files.create({file:b,purpose:"assistants"},d);h.push(a.id)}}let j=Array(e).fill(g).map(i);return await de(j),await this.createAndPoll(a,{file_ids:h})}}class dg extends b8{create(a,b,c){return this._client.post(cb`/vector_stores/${a}/files`,{body:b,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{vector_store_id:d}=b;return this._client.get(cb`/vector_stores/${d}/files/${a}`,{...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{vector_store_id:d,...e}=b;return this._client.post(cb`/vector_stores/${d}/files/${a}`,{body:e,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(cb`/vector_stores/${a}/files`,bW,{query:b,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b,c){let{vector_store_id:d}=b;return this._client.delete(cb`/vector_stores/${d}/files/${a}`,{...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b,c);return await this.poll(a,d.id,c)}async poll(a,b,c){let d=cw([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let e=await this.retrieve(b,{vector_store_id:a},{...c,headers:d}).withResponse(),f=e.data;switch(f.status){case"in_progress":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=e.response.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await bi(g);break;case"failed":case"completed":return f}}}async upload(a,b,c){let d=await this._client.files.create({file:b,purpose:"assistants"},c);return this.create(a,{file_id:d.id},c)}async uploadAndPoll(a,b,c){let d=await this.upload(a,b,c);return await this.poll(a,d.id,c)}content(a,b,c){let{vector_store_id:d}=b;return this._client.getAPIList(cb`/vector_stores/${d}/files/${a}/content`,bV,{...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}class dh extends b8{constructor(){super(...arguments),this.files=new dg(this._client),this.fileBatches=new df(this._client)}create(a,b){return this._client.post("/vector_stores",{body:a,...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(cb`/vector_stores/${a}`,{...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(cb`/vector_stores/${a}`,{body:b,...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a={},b){return this._client.getAPIList("/vector_stores",bW,{query:a,...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}delete(a,b){return this._client.delete(cb`/vector_stores/${a}`,{...b,headers:cw([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}search(a,b,c){return this._client.getAPIList(cb`/vector_stores/${a}/search`,bV,{body:b,method:"post",...c,headers:cw([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}dh.Files=dg,dh.FileBatches=df;class di extends b8{constructor(){super(...arguments),aw.add(this)}async unwrap(a,b,c=this._client.webhookSecret,d=300){return await this.verifySignature(a,b,c,d),JSON.parse(a)}async verifySignature(a,b,c=this._client.webhookSecret,d=300){if("undefined"==typeof crypto||"function"!=typeof crypto.subtle.importKey||"function"!=typeof crypto.subtle.verify)throw Error("Webhook signature verification is only supported when the `crypto` global is defined");aW(this,aw,"m",ax).call(this,c);let e=cw([b]).values,f=aW(this,aw,"m",ay).call(this,e,"webhook-signature"),g=aW(this,aw,"m",ay).call(this,e,"webhook-timestamp"),h=aW(this,aw,"m",ay).call(this,e,"webhook-id"),i=parseInt(g,10);if(isNaN(i))throw new bd("Invalid webhook timestamp format");let j=Math.floor(Date.now()/1e3);if(j-i>d)throw new bd("Webhook timestamp is too old");if(i>j+d)throw new bd("Webhook timestamp is too new");let k=f.split(" ").map(a=>a.startsWith("v1,")?a.substring(3):a),l=c.startsWith("whsec_")?Buffer.from(c.replace("whsec_",""),"base64"):Buffer.from(c,"utf-8"),m=h?`${h}.${g}.${a}`:`${g}.${a}`,n=await crypto.subtle.importKey("raw",l,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(let a of k)try{let b=Buffer.from(a,"base64");if(await crypto.subtle.verify("HMAC",n,b,new TextEncoder().encode(m)))return}catch{continue}throw new bd("The given webhook signature does not match the expected signature")}}aw=new WeakSet,ax=function(a){if("string"!=typeof a||0===a.length)throw Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},ay=function(a,b){if(!a)throw Error("Headers are required");let c=a.get(b);if(null==c)throw Error(`Missing required header: ${b}`);return c};class dj{constructor({baseURL:a=cI("OPENAI_BASE_URL"),apiKey:b=cI("OPENAI_API_KEY"),organization:c=cI("OPENAI_ORG_ID")??null,project:d=cI("OPENAI_PROJECT_ID")??null,webhookSecret:e=cI("OPENAI_WEBHOOK_SECRET")??null,...f}={}){if(az.add(this),aB.set(this,void 0),this.completions=new cN(this),this.chat=new cu(this),this.embeddings=new cR(this),this.files=new cV(this),this.images=new c4(this),this.audio=new cA(this),this.moderations=new c6(this),this.models=new c5(this),this.fineTuning=new c1(this),this.graders=new c3(this),this.vectorStores=new dh(this),this.webhooks=new di(this),this.beta=new cM(this),this.batches=new cB(this),this.uploads=new dd(this),this.responses=new db(this),this.evals=new cU(this),this.containers=new cQ(this),void 0===b)throw new a$("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let g={apiKey:b,organization:c,project:d,webhookSecret:e,...f,baseURL:a||"https://api.openai.com/v1"};if(!g.dangerouslyAllowBrowser&&"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator)throw new a$("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=g.baseURL,this.timeout=g.timeout??aA.DEFAULT_TIMEOUT,this.logger=g.logger??console;let h="warn";this.logLevel=h,this.logLevel=bF(g.logLevel,"ClientOptions.logLevel",this)??bF(cI("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??h,this.fetchOptions=g.fetchOptions,this.maxRetries=g.maxRetries??2,this.fetch=g.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),aV(this,aB,bq,"f"),this._options=g,this.apiKey=b,this.organization=c,this.project=d,this.webhookSecret=e}withOptions(a){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...a})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:a,nulls:b}){}async authHeaders(a){return cw([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(a){return function(a,b={}){let c,d=a,e=function(a=bz){let b;if(void 0!==a.allowEmptyArrays&&"boolean"!=typeof a.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==a.encodeDotInKeys&&"boolean"!=typeof a.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==a.encoder&&void 0!==a.encoder&&"function"!=typeof a.encoder)throw TypeError("Encoder has to be a function.");let c=a.charset||bz.charset;if(void 0!==a.charset&&"utf-8"!==a.charset&&"iso-8859-1"!==a.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let d=br;if(void 0!==a.format){if(!bu(bt,a.format))throw TypeError("Unknown format option provided.");d=a.format}let e=bt[d],f=bz.filter;if(("function"==typeof a.filter||bf(a.filter))&&(f=a.filter),b=a.arrayFormat&&a.arrayFormat in bx?a.arrayFormat:"indices"in a?a.indices?"indices":"repeat":bz.arrayFormat,"commaRoundTrip"in a&&"boolean"!=typeof a.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let g=void 0===a.allowDots?!0==!!a.encodeDotInKeys||bz.allowDots:!!a.allowDots;return{addQueryPrefix:"boolean"==typeof a.addQueryPrefix?a.addQueryPrefix:bz.addQueryPrefix,allowDots:g,allowEmptyArrays:"boolean"==typeof a.allowEmptyArrays?!!a.allowEmptyArrays:bz.allowEmptyArrays,arrayFormat:b,charset:c,charsetSentinel:"boolean"==typeof a.charsetSentinel?a.charsetSentinel:bz.charsetSentinel,commaRoundTrip:!!a.commaRoundTrip,delimiter:void 0===a.delimiter?bz.delimiter:a.delimiter,encode:"boolean"==typeof a.encode?a.encode:bz.encode,encodeDotInKeys:"boolean"==typeof a.encodeDotInKeys?a.encodeDotInKeys:bz.encodeDotInKeys,encoder:"function"==typeof a.encoder?a.encoder:bz.encoder,encodeValuesOnly:"boolean"==typeof a.encodeValuesOnly?a.encodeValuesOnly:bz.encodeValuesOnly,filter:f,format:d,formatter:e,serializeDate:"function"==typeof a.serializeDate?a.serializeDate:bz.serializeDate,skipNulls:"boolean"==typeof a.skipNulls?a.skipNulls:bz.skipNulls,sort:"function"==typeof a.sort?a.sort:null,strictNullHandling:"boolean"==typeof a.strictNullHandling?a.strictNullHandling:bz.strictNullHandling}}(b);"function"==typeof e.filter?d=(0,e.filter)("",d):bf(e.filter)&&(c=e.filter);let f=[];if("object"!=typeof d||null===d)return"";let g=bx[e.arrayFormat],h="comma"===g&&e.commaRoundTrip;c||(c=Object.keys(d)),e.sort&&c.sort(e.sort);let i=new WeakMap;for(let a=0;a<c.length;++a){let b=c[a];e.skipNulls&&null===d[b]||by(f,function a(b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s){var t,u;let v,w=b,x=s,y=0,z=!1;for(;void 0!==(x=x.get(bA))&&!z;){let a=x.get(b);if(y+=1,void 0!==a)if(a===y)throw RangeError("Cyclic object value");else z=!0;void 0===x.get(bA)&&(y=0)}if("function"==typeof k?w=k(c,w):w instanceof Date?w=n?.(w):"comma"===d&&bf(w)&&(w=bw(w,function(a){return a instanceof Date?n?.(a):a})),null===w){if(g)return j&&!q?j(c,bz.encoder,r,"key",o):c;w=""}if("string"==typeof(t=w)||"number"==typeof t||"boolean"==typeof t||"symbol"==typeof t||"bigint"==typeof t||(u=w)&&"object"==typeof u&&u.constructor&&u.constructor.isBuffer&&u.constructor.isBuffer(u)){if(j){let a=q?c:j(c,bz.encoder,r,"key",o);return[p?.(a)+"="+p?.(j(w,bz.encoder,r,"value",o))]}return[p?.(c)+"="+p?.(String(w))]}let A=[];if(void 0===w)return A;if("comma"===d&&bf(w))q&&j&&(w=bw(w,j)),v=[{value:w.length>0?w.join(",")||null:void 0}];else if(bf(k))v=k;else{let a=Object.keys(w);v=l?a.sort(l):a}let B=i?String(c).replace(/\./g,"%2E"):String(c),C=e&&bf(w)&&1===w.length?B+"[]":B;if(f&&bf(w)&&0===w.length)return C+"[]";for(let c=0;c<v.length;++c){let t=v[c],u="object"==typeof t&&void 0!==t.value?t.value:w[t];if(h&&null===u)continue;let x=m&&i?t.replace(/\./g,"%2E"):t,z=bf(w)?"function"==typeof d?d(C,x):C:C+(m?"."+x:"["+x+"]");s.set(b,y);let B=new WeakMap;B.set(bA,s),by(A,a(u,z,d,e,f,g,h,i,"comma"===d&&q&&bf(w)?null:j,k,l,m,n,o,p,q,r,B))}return A}(d[b],b,g,h,e.allowEmptyArrays,e.strictNullHandling,e.skipNulls,e.encodeDotInKeys,e.encode?e.encoder:null,e.filter,e.sort,e.allowDots,e.serializeDate,e.format,e.formatter,e.encodeValuesOnly,e.charset,i))}let j=f.join(e.delimiter),k=!0===e.addQueryPrefix?"?":"";return e.charsetSentinel&&("iso-8859-1"===e.charset?k+="utf8=%26%2310003%3B&":k+="utf8=%E2%9C%93&"),j.length>0?k+j:""}(a,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${bj}`}defaultIdempotencyKey(){return`stainless-node-retry-${aX()}`}makeStatusError(a,b,c,d){return a_.generate(a,b,c,d)}buildURL(a,b,c){let d=!aW(this,az,"m",aC).call(this)&&c||this.baseURL,e=new URL(be.test(a)?a:d+(d.endsWith("/")&&a.startsWith("/")?a.slice(1):a)),f=this.defaultQuery();return!function(a){if(!a)return!0;for(let b in a)return!1;return!0}(f)&&(b={...f,...b}),"object"==typeof b&&b&&!Array.isArray(b)&&(e.search=this.stringifyQuery(b)),e.toString()}async prepareOptions(a){}async prepareRequest(a,{url:b,options:c}){}get(a,b){return this.methodRequest("get",a,b)}post(a,b){return this.methodRequest("post",a,b)}patch(a,b){return this.methodRequest("patch",a,b)}put(a,b){return this.methodRequest("put",a,b)}delete(a,b){return this.methodRequest("delete",a,b)}methodRequest(a,b,c){return this.request(Promise.resolve(c).then(c=>({method:a,path:b,...c})))}request(a,b=null){return new bS(this,this.makeRequest(a,b,void 0))}async makeRequest(a,b,c){let d=await a,e=d.maxRetries??this.maxRetries;null==b&&(b=e),await this.prepareOptions(d);let{req:f,url:g,timeout:h}=await this.buildRequest(d,{retryCount:e-b});await this.prepareRequest(f,{url:g,options:d});let i="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),j=void 0===c?"":`, retryOf: ${c}`,k=Date.now();if(bK(this).debug(`[${i}] sending request`,bL({retryOfRequestLogID:c,method:d.method,url:g,options:d,headers:f.headers})),d.signal?.aborted)throw new a0;let l=new AbortController,m=await this.fetchWithTimeout(g,f,h,l).catch(aZ),n=Date.now();if(m instanceof Error){let a=`retrying, ${b} attempts remaining`;if(d.signal?.aborted)throw new a0;let e=aY(m)||/timed? ?out/i.test(String(m)+("cause"in m?String(m.cause):""));if(b)return bK(this).info(`[${i}] connection ${e?"timed out":"failed"} - ${a}`),bK(this).debug(`[${i}] connection ${e?"timed out":"failed"} (${a})`,bL({retryOfRequestLogID:c,url:g,durationMs:n-k,message:m.message})),this.retryRequest(d,b,c??i);if(bK(this).info(`[${i}] connection ${e?"timed out":"failed"} - error; no more retries left`),bK(this).debug(`[${i}] connection ${e?"timed out":"failed"} (error; no more retries left)`,bL({retryOfRequestLogID:c,url:g,durationMs:n-k,message:m.message})),e)throw new a2;throw new a1({cause:m})}let o=[...m.headers.entries()].filter(([a])=>"x-request-id"===a).map(([a,b])=>", "+a+": "+JSON.stringify(b)).join(""),p=`[${i}${j}${o}] ${f.method} ${g} ${m.ok?"succeeded":"failed"} with status ${m.status} in ${n-k}ms`;if(!m.ok){let a=await this.shouldRetry(m);if(b&&a){let a=`retrying, ${b} attempts remaining`;return await bp(m.body),bK(this).info(`${p} - ${a}`),bK(this).debug(`[${i}] response error (${a})`,bL({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,durationMs:n-k})),this.retryRequest(d,b,c??i,m.headers)}let e=a?"error; no more retries left":"error; not retryable";bK(this).info(`${p} - ${e}`);let f=await m.text().catch(a=>aZ(a).message),g=(a=>{try{return JSON.parse(a)}catch(a){return}})(f),h=g?void 0:f;throw bK(this).debug(`[${i}] response error (${e})`,bL({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,message:h,durationMs:Date.now()-k})),this.makeStatusError(m.status,g,h,m.headers)}return bK(this).info(p),bK(this).debug(`[${i}] response start`,bL({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,durationMs:n-k})),{response:m,options:d,controller:l,requestLogID:i,retryOfRequestLogID:c,startTime:k}}getAPIList(a,b,c){return this.requestAPIList(b,{method:"get",path:a,...c})}requestAPIList(a,b){return new bU(this,this.makeRequest(b,null,void 0),a)}async fetchWithTimeout(a,b,c,d){let{signal:e,method:f,...g}=b||{};e&&e.addEventListener("abort",()=>d.abort());let h=setTimeout(()=>d.abort(),c),i=globalThis.ReadableStream&&g.body instanceof globalThis.ReadableStream||"object"==typeof g.body&&null!==g.body&&Symbol.asyncIterator in g.body,j={signal:d.signal,...i?{duplex:"half"}:{},method:"GET",...g};f&&(j.method=f.toUpperCase());try{return await this.fetch.call(void 0,a,j)}finally{clearTimeout(h)}}async shouldRetry(a){let b=a.headers.get("x-should-retry");return"true"===b||"false"!==b&&(408===a.status||409===a.status||429===a.status||!!(a.status>=500))}async retryRequest(a,b,c,d){let e,f=d?.get("retry-after-ms");if(f){let a=parseFloat(f);Number.isNaN(a)||(e=a)}let g=d?.get("retry-after");if(g&&!e){let a=parseFloat(g);e=Number.isNaN(a)?Date.parse(g)-Date.now():1e3*a}if(!(e&&0<=e&&e<6e4)){let c=a.maxRetries??this.maxRetries;e=this.calculateDefaultRetryTimeoutMillis(b,c)}return await bi(e),this.makeRequest(a,b-1,c)}calculateDefaultRetryTimeoutMillis(a,b){return Math.min(.5*Math.pow(2,b-a),8)*(1-.25*Math.random())*1e3}async buildRequest(a,{retryCount:b=0}={}){let c={...a},{method:d,path:e,query:f,defaultBaseURL:g}=c,h=this.buildURL(e,f,g);"timeout"in c&&((a,b)=>{if("number"!=typeof b||!Number.isInteger(b))throw new a$(`${a} must be an integer`);if(b<0)throw new a$(`${a} must be a positive integer`)})("timeout",c.timeout),c.timeout=c.timeout??this.timeout;let{bodyHeaders:i,body:j}=this.buildBody({options:c}),k=await this.buildHeaders({options:a,method:d,bodyHeaders:i,retryCount:b});return{req:{method:d,headers:k,...c.signal&&{signal:c.signal},...globalThis.ReadableStream&&j instanceof globalThis.ReadableStream&&{duplex:"half"},...j&&{body:j},...this.fetchOptions??{},...c.fetchOptions??{}},url:h,timeout:c.timeout}}async buildHeaders({options:a,method:b,bodyHeaders:c,retryCount:e}){let f={};this.idempotencyHeader&&"get"!==b&&(a.idempotencyKey||(a.idempotencyKey=this.defaultIdempotencyKey()),f[this.idempotencyHeader]=a.idempotencyKey);let g=cw([f,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(e),...a.timeout?{"X-Stainless-Timeout":String(Math.trunc(a.timeout/1e3))}:{},...d??(d=(()=>{let a="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===a)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":bj,"X-Stainless-OS":bl(Deno.build.os),"X-Stainless-Arch":bk(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":bj,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===a)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":bj,"X-Stainless-OS":bl(globalThis.process.platform??"unknown"),"X-Stainless-Arch":bk(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let b=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:a,pattern:b}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let c=b.exec(navigator.userAgent);if(c){let b=c[1]||0,d=c[2]||0,e=c[3]||0;return{browser:a,version:`${b}.${d}.${e}`}}}return null}();return b?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":bj,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${b.browser}`,"X-Stainless-Runtime-Version":b.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":bj,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}})()),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(a),this._options.defaultHeaders,c,a.headers]);return this.validateHeaders(g),g.values}buildBody({options:{body:a,headers:b}}){if(!a)return{bodyHeaders:void 0,body:void 0};let c=cw([b]);return ArrayBuffer.isView(a)||a instanceof ArrayBuffer||a instanceof DataView||"string"==typeof a&&c.values.has("content-type")||a instanceof Blob||a instanceof FormData||a instanceof URLSearchParams||globalThis.ReadableStream&&a instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:a}:"object"==typeof a&&(Symbol.asyncIterator in a||Symbol.iterator in a&&"next"in a&&"function"==typeof a.next)?{bodyHeaders:void 0,body:bn(a)}:aW(this,aB,"f").call(this,{body:a,headers:c})}}async function dk(a){try{var b;let{cards:c,question:d,locale:e="en",stream:f=!1}=await a.json(),g=function(a){let b=a.headers.get("x-forwarded-for"),c=a.headers.get("x-real-ip");return(b?.split(",")[0]||c||a.ip,"CN"===(a.headers.get("x-vercel-ip-country")||a.geo?.country||""))?process.env.OPENAI_BASE_URL_CN:process.env.OPENAI_BASE_URL_OTHER}(a),h=new dj({apiKey:process.env.OPENAI_API_KEY,baseURL:g}),i=process.env.OPENAI_MODEL||"gpt-4o",j=function(a,b,c){if("zh"===c){let c=a.map((a,b)=>{let c=["过去","现在","未来"][b],d=a.isReversed?"逆位":"正位",e=a.isReversed?a.reversedMeaning:a.meaning;return`${c}：${a.name}（${d}）
    关键词：${a.keywords.join("、")}
    含义：${e}`}).join("\n\n");return`请为以下问题和卡牌提供全面的塔罗牌解读：

问题："${b}"

抽到的卡牌：
${c}

请提供：
1. 对三张牌整体布局的解读
2. 卡牌之间的联系和故事线
3. 基于解读的实用指导和建议

请用温暖、富有洞察力和鼓励性的语调书写,总体解读内容应约为80-100字。`}{let c=a.map((a,b)=>{let c=["Past","Present","Future"][b],d=a.isReversed?"Reversed":"Upright",e=a.isReversed?a.reversedMeaningEn:a.meaningEn;return`${c}: ${a.nameEn} (${d})
    Keywords: ${a.keywordsEn.join(", ")}
    Meaning: ${e}`}).join("\n\n");return`Please provide a comprehensive tarot reading for the following question and cards:

Question: "${b}"

Cards drawn:
${c}

Please provide:
1. An overall interpretation of the three-card spread
2. The connections and story between the cards
3. Practical guidance and advice based on the reading


Write in a warm, insightful, and encouraging tone. The reading should be approximately 80-100 words.`}}(c,d,e),k=(b=e,"zh"===b?"你是一位智慧而直觉敏锐的塔罗牌解读师，对塔罗牌的象征意义和含义有着深刻的理解。请提供富有洞察力、深思熟虑的解读，帮助人们获得清晰的认知和视角。请用中文回答。":"You are a wise and intuitive tarot reader with deep knowledge of tarot symbolism and meanings. Provide insightful, thoughtful readings that help people gain clarity and perspective. Please respond in English.");if(f){let a=await h.chat.completions.create({model:i,messages:[{role:"system",content:k},{role:"user",content:j}],stream:!0,temperature:.7,max_tokens:1500}),b=new TextEncoder,c=new ReadableStream({async start(c){try{for await(let d of a){let a=d.choices[0]?.delta?.content||"";a&&c.enqueue(b.encode(`data: ${JSON.stringify({content:a})}

`))}c.enqueue(b.encode("data: [DONE]\n\n")),c.close()}catch(a){c.error(a)}}});return new Response(c,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"}})}{let a=await h.chat.completions.create({model:i,messages:[{role:"system",content:k},{role:"user",content:j}],temperature:.7,max_tokens:1500}),b=a.choices[0]?.message?.content||"";return aU.NextResponse.json({reading:b,cards:c.map((a,b)=>({...a,position:["Past","Present","Future"][b]})),question:d,timestamp:new Date().toISOString()})}}catch(a){if(a instanceof Error&&a.message.includes("API key"))return aU.NextResponse.json({error:"OpenAI API key not configured"},{status:500});return aU.NextResponse.json({error:"Failed to generate reading"},{status:500})}}aA=dj,aB=new WeakMap,az=new WeakSet,aC=function(){return"https://api.openai.com/v1"!==this.baseURL},dj.OpenAI=aA,dj.DEFAULT_TIMEOUT=6e5,dj.OpenAIError=a$,dj.APIError=a_,dj.APIConnectionError=a1,dj.APIConnectionTimeoutError=a2,dj.APIUserAbortError=a0,dj.NotFoundError=a6,dj.ConflictError=a7,dj.RateLimitError=a9,dj.BadRequestError=a3,dj.AuthenticationError=a4,dj.InternalServerError=ba,dj.PermissionDeniedError=a5,dj.UnprocessableEntityError=a8,dj.InvalidWebhookSignatureError=bd,dj.toFile=b6,dj.Completions=cN,dj.Chat=cu,dj.Embeddings=cR,dj.Files=cV,dj.Images=c4,dj.Audio=cA,dj.Moderations=c6,dj.Models=c5,dj.FineTuning=c1,dj.Graders=c3,dj.VectorStores=dh,dj.Webhooks=di,dj.Beta=cM,dj.Batches=cB,dj.Uploads=dd,dj.Responses=db,dj.Evals=cU,dj.Containers=cQ;let dl=new aE.AppRouteRouteModule({definition:{kind:aF.RouteKind.APP_ROUTE,page:"/api/reading/route",pathname:"/api/reading",filename:"route",bundlePath:"app/api/reading/route"},distDir:".next",projectDir:"",resolvedPagePath:"D:\\project\\chuhai\\tarot_new\\mytarot\\src\\app\\api\\reading\\route.ts",nextConfigOutput:"",userland:aD}),{workAsyncStorage:dm,workUnitAsyncStorage:dn,serverHooks:dp}=dl;function dq(){return(0,aG.patchFetch)({workAsyncStorage:dm,workUnitAsyncStorage:dn})}async function dr(a,b,c){var d;let e="/api/reading/route";"/index"===e&&(e="/");let f=await dl.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!f)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:g,params:h,nextConfig:i,isDraftMode:j,prerenderManifest:k,routerServerContext:l,isOnDemandRevalidate:m,revalidateOnlyGenerated:n,resolvedPathname:o}=f,p=(0,aJ.normalizeAppPath)(e),q=!!(k.dynamicRoutes[p]||k.routes[o]);if(q&&!j){let a=!!k.routes[o],b=k.dynamicRoutes[p];if(b&&!1===b.fallback&&!a)throw new aS.NoFallbackError}let r=null;!q||dl.isDev||j||(r="/index"===(r=o)?"/":r);let s=!0===dl.isDev||!q,t=q&&!s,u=a.method||"GET",v=(0,aI.getTracer)(),w=v.getActiveScopeSpan(),x={params:h,prerenderManifest:k,renderOpts:{experimental:{dynamicIO:!!i.experimental.dynamicIO,authInterrupts:!!i.experimental.authInterrupts},supportsDynamicResponse:s,incrementalCache:(0,aH.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=i.experimental)?void 0:d.cacheLife,isRevalidate:t,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>dl.onRequestError(a,b,d,l)},sharedContext:{buildId:g}},y=new aK.NodeNextRequest(a),z=new aK.NodeNextResponse(b),A=aL.NextRequestAdapter.fromNodeNextRequest(y,(0,aL.signalFromNodeResponse)(b));try{let d=async c=>dl.handle(A,x).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=v.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==aM.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${u} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${u} ${a.url}`)}),f=async f=>{var g,h;let o=async({previousCacheEntry:g})=>{try{if(!(0,aH.getRequestMeta)(a,"minimalMode")&&m&&n&&!g)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(f);a.fetchMetrics=x.renderOpts.fetchMetrics;let h=x.renderOpts.pendingWaitUntil;h&&c.waitUntil&&(c.waitUntil(h),h=void 0);let i=x.renderOpts.collectedTags;if(!q)return await (0,aO.I)(y,z,e,x.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,aP.toNodeOutgoingHttpHeaders)(e.headers);i&&(b[aR.NEXT_CACHE_TAGS_HEADER]=i),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==x.renderOpts.collectedRevalidate&&!(x.renderOpts.collectedRevalidate>=aR.INFINITE_CACHE)&&x.renderOpts.collectedRevalidate,d=void 0===x.renderOpts.collectedExpire||x.renderOpts.collectedExpire>=aR.INFINITE_CACHE?void 0:x.renderOpts.collectedExpire;return{value:{kind:aT.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==g?void 0:g.isStale)&&await dl.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,aN.c)({isRevalidate:t,isOnDemandRevalidate:m})},l),b}},p=await dl.handleResponse({req:a,nextConfig:i,cacheKey:r,routeKind:aF.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:k,isRoutePPREnabled:!1,isOnDemandRevalidate:m,revalidateOnlyGenerated:n,responseGenerator:o,waitUntil:c.waitUntil});if(!q)return null;if((null==p||null==(g=p.value)?void 0:g.kind)!==aT.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==p||null==(h=p.value)?void 0:h.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,aH.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",m?"REVALIDATED":p.isMiss?"MISS":p.isStale?"STALE":"HIT"),j&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let s=(0,aP.fromNodeOutgoingHttpHeaders)(p.value.headers);return(0,aH.getRequestMeta)(a,"minimalMode")&&q||s.delete(aR.NEXT_CACHE_TAGS_HEADER),!p.cacheControl||b.getHeader("Cache-Control")||s.get("Cache-Control")||s.set("Cache-Control",(0,aQ.getCacheControlHeader)(p.cacheControl)),await (0,aO.I)(y,z,new Response(p.value.body,{headers:s,status:p.value.status||200})),null};w?await f(w):await v.withPropagatedContext(a.headers,()=>v.trace(aM.BaseServerSpan.handleRequest,{spanName:`${u} ${a.url}`,kind:aI.SpanKind.SERVER,attributes:{"http.method":u,"http.target":a.url}},f))}catch(b){if(w||b instanceof aS.NoFallbackError||await dl.onRequestError(a,b,{routerKind:"App Router",routePath:p,routeType:"route",revalidateReason:(0,aN.c)({isRevalidate:t,isOnDemandRevalidate:m})}),q)throw b;return await (0,aO.I)(y,z,new Response(null,{status:500})),null}}},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=66789));module.exports=c})();