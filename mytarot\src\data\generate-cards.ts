import { TarotCard } from '@/types/tarot';

// 大阿卡尔牌的基本信息
const majorArcanaData = [
  { name: '愚者', nameEn: 'The Fool', keywords: ['新开始', '冒险', '纯真', '自由'], keywordsEn: ['New beginnings', 'Adventure', 'Innocence', 'Freedom'] },
  { name: '魔术师', nameEn: 'The Magician', keywords: ['意志力', '创造力', '技能', '专注'], keywordsEn: ['Willpower', 'Creativity', 'Skill', 'Focus'] },
  { name: '女祭司', nameEn: 'The High Priestess', keywords: ['直觉', '神秘', '潜意识', '智慧'], keywordsEn: ['Intuition', 'Mystery', 'Subconscious', 'Wisdom'] },
  { name: '皇后', nameEn: 'The Empress', keywords: ['丰饶', '母性', '创造力', '自然'], keywordsEn: ['Abundance', 'Motherhood', 'Creativity', 'Nature'] },
  { name: '皇帝', nameEn: 'The Emperor', keywords: ['权威', '结构', '控制', '稳定'], keywordsEn: ['Authority', 'Structure', 'Control', 'Stability'] },
  { name: '教皇', nameEn: 'The Hierophant', keywords: ['传统', '精神指导', '教育', '信仰'], keywordsEn: ['Tradition', 'Spiritual guidance', 'Education', 'Faith'] },
  { name: '恋人', nameEn: 'The Lovers', keywords: ['爱情', '选择', '和谐', '关系'], keywordsEn: ['Love', 'Choice', 'Harmony', 'Relationships'] },
  { name: '战车', nameEn: 'The Chariot', keywords: ['胜利', '意志力', '控制', '决心'], keywordsEn: ['Victory', 'Willpower', 'Control', 'Determination'] },
  { name: '力量', nameEn: 'Strength', keywords: ['勇气', '耐心', '自控', '内在力量'], keywordsEn: ['Courage', 'Patience', 'Self-control', 'Inner strength'] },
  { name: '隐者', nameEn: 'The Hermit', keywords: ['内省', '寻找', '指导', '智慧'], keywordsEn: ['Introspection', 'Seeking', 'Guidance', 'Wisdom'] },
  { name: '命运之轮', nameEn: 'Wheel of Fortune', keywords: ['命运', '变化', '循环', '机会'], keywordsEn: ['Destiny', 'Change', 'Cycles', 'Opportunity'] },
  { name: '正义', nameEn: 'Justice', keywords: ['公正', '平衡', '真理', '法律'], keywordsEn: ['Justice', 'Balance', 'Truth', 'Law'] },
  { name: '倒吊人', nameEn: 'The Hanged Man', keywords: ['牺牲', '等待', '新视角', '放手'], keywordsEn: ['Sacrifice', 'Waiting', 'New perspective', 'Letting go'] },
  { name: '死神', nameEn: 'Death', keywords: ['转变', '结束', '重生', '释放'], keywordsEn: ['Transformation', 'Endings', 'Rebirth', 'Release'] },
  { name: '节制', nameEn: 'Temperance', keywords: ['平衡', '耐心', '调和', '治愈'], keywordsEn: ['Balance', 'Patience', 'Moderation', 'Healing'] },
  { name: '恶魔', nameEn: 'The Devil', keywords: ['束缚', '诱惑', '物质主义', '依赖'], keywordsEn: ['Bondage', 'Temptation', 'Materialism', 'Addiction'] },
  { name: '塔', nameEn: 'The Tower', keywords: ['突然变化', '破坏', '启示', '解放'], keywordsEn: ['Sudden change', 'Destruction', 'Revelation', 'Liberation'] },
  { name: '星星', nameEn: 'The Star', keywords: ['希望', '灵感', '治愈', '指导'], keywordsEn: ['Hope', 'Inspiration', 'Healing', 'Guidance'] },
  { name: '月亮', nameEn: 'The Moon', keywords: ['幻象', '恐惧', '潜意识', '直觉'], keywordsEn: ['Illusion', 'Fear', 'Subconscious', 'Intuition'] },
  { name: '太阳', nameEn: 'The Sun', keywords: ['快乐', '成功', '活力', '乐观'], keywordsEn: ['Joy', 'Success', 'Vitality', 'Optimism'] },
  { name: '审判', nameEn: 'Judgement', keywords: ['重生', '觉醒', '宽恕', '救赎'], keywordsEn: ['Rebirth', 'Awakening', 'Forgiveness', 'Redemption'] },
  { name: '世界', nameEn: 'The World', keywords: ['完成', '成就', '旅程结束', '满足'], keywordsEn: ['Completion', 'Achievement', 'Journey\'s end', 'Fulfillment'] }
];

// 小阿卡尔牌的花色信息
const suits = [
  { suit: 'cups', name: '圣杯', nameEn: 'Cups', element: '水', elementEn: 'Water' },
  { suit: 'pentacles', name: '金币', nameEn: 'Pentacles', element: '土', elementEn: 'Earth' },
  { suit: 'swords', name: '宝剑', nameEn: 'Swords', element: '风', elementEn: 'Air' },
  { suit: 'wands', name: '权杖', nameEn: 'Wands', element: '火', elementEn: 'Fire' }
];

// 小阿卡尔牌的数字牌名称
const minorNumbers = [
  { num: 1, name: '王牌', nameEn: 'Ace' },
  { num: 2, name: '二', nameEn: 'Two' },
  { num: 3, name: '三', nameEn: 'Three' },
  { num: 4, name: '四', nameEn: 'Four' },
  { num: 5, name: '五', nameEn: 'Five' },
  { num: 6, name: '六', nameEn: 'Six' },
  { num: 7, name: '七', nameEn: 'Seven' },
  { num: 8, name: '八', nameEn: 'Eight' },
  { num: 9, name: '九', nameEn: 'Nine' },
  { num: 10, name: '十', nameEn: 'Ten' },
  { num: 11, name: '侍从', nameEn: 'Page' },
  { num: 12, name: '骑士', nameEn: 'Knight' },
  { num: 13, name: '王后', nameEn: 'Queen' },
  { num: 14, name: '国王', nameEn: 'King' }
];

// 生成所有塔罗牌
export const generateAllCards = (): TarotCard[] => {
  const cards: TarotCard[] = [];

  // 生成大阿卡尔牌
  majorArcanaData.forEach((data, index) => {
    const paddedIndex = index.toString().padStart(2, '0');
    // 移除"The "前缀并转换为小写，用连字符连接
    let fileName = data.nameEn.toLowerCase()
      .replace(/^the\s+/, '') // 移除开头的"The "
      .replace(/\s+/g, '-')   // 空格替换为连字符
      .replace(/'/g, '');     // 移除撇号

    cards.push({
      id: `major-${paddedIndex}`,
      name: data.name,
      nameEn: data.nameEn,
      type: 'major',
      number: index,
      imagePath: `/images/major/${paddedIndex}-${fileName}.webp`,
      keywords: data.keywords,
      keywordsEn: data.keywordsEn,
      meaning: `${data.name}代表${data.keywords.join('、')}。`,
      meaningEn: `${data.nameEn} represents ${data.keywordsEn.join(', ').toLowerCase()}.`,
      reversedMeaning: `逆位的${data.name}可能表示这些品质的缺失或过度。`,
      reversedMeaningEn: `Reversed ${data.nameEn} may indicate the absence or excess of these qualities.`
    });
  });

  // 生成小阿卡尔牌
  suits.forEach(suit => {
    minorNumbers.forEach((numData, index) => {
      const paddedIndex = index.toString().padStart(2, '0');
      let fileName: string;

      if (index === 0) {
        fileName = 'ace';
      } else {
        fileName = `${numData.nameEn.toLowerCase()}-of-${suit.nameEn.toLowerCase()}`;
      }

      cards.push({
        id: `${suit.suit}-${paddedIndex}`,
        name: `${suit.name}${numData.name}`,
        nameEn: `${numData.nameEn} of ${suit.nameEn}`,
        type: 'minor',
        suit: suit.suit as 'cups' | 'pentacles' | 'swords' | 'wands',
        number: numData.num,
        imagePath: `/images/minor/${suit.suit}/${paddedIndex}-${fileName}.webp`,
        keywords: [`${suit.element}元素`, `${numData.name}的能量`],
        keywordsEn: [`${suit.elementEn} element`, `${numData.nameEn} energy`],
        meaning: `${suit.name}${numData.name}代表${suit.element}元素的${numData.name}阶段能量。`,
        meaningEn: `${numData.nameEn} of ${suit.nameEn} represents the ${numData.nameEn.toLowerCase()} stage of ${suit.elementEn.toLowerCase()} element energy.`,
        reversedMeaning: `逆位时可能表示这种能量的阻塞或过度。`,
        reversedMeaningEn: `When reversed, it may indicate blockage or excess of this energy.`
      });
    });
  });

  return cards;
};
