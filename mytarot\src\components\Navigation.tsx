'use client';

import Link from 'next/link';
import { useLocale } from 'next-intl';
import LanguageToggle from './LanguageToggle';

export default function Navigation() {
  const locale = useLocale();

  return (
    <header className="relative z-50">
      <div className="flex justify-between items-center p-4 md:p-6">
        <Link href={`/${locale}`} className="text-white text-xl md:text-2xl font-bold hover:text-purple-200 transition-colors">
          🔮 {locale === 'zh' ? '神秘塔罗' : 'Mystic Tarot'}
        </Link>

        {/* Language Toggle - 在所有设备上都可见 */}
        <div className="flex items-center">
          <LanguageToggle />
        </div>
      </div>
    </header>
  );
}
