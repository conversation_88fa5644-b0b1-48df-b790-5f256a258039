"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/reading/page",{

/***/ "(app-pages-browser)/./src/components/TarotCard.tsx":
/*!**************************************!*\
  !*** ./src/components/TarotCard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst TarotCard = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.memo)(_c = function TarotCard(param) {\n    let { card, isRevealed = false, isSelected = false, isReversed = false, size = 'large', onClick, className = '', style, showBack = true } = param;\n    const cardBackImage = '/images/card-back.webp';\n    // 根据尺寸设置样式\n    const sizeClasses = {\n        small: 'w-12 h-18',\n        medium: 'w-16 h-24',\n        large: 'w-24 h-36'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"relative cursor-pointer \".concat(sizeClasses[size], \" \").concat(className),\n        style: style,\n        onClick: onClick,\n        whileHover: {\n            scale: 1.02\n        },\n        whileTap: {\n            scale: 0.98\n        },\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        transition: {\n            duration: 0.2\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-full \".concat(isSelected ? 'ring-4 ring-purple-400 ring-opacity-75' : '', \" rounded-lg overflow-hidden shadow-lg\"),\n            children: [\n                !isRevealed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"absolute inset-0\",\n                    initial: {\n                        opacity: 1\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: [\n                        showBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: cardBackImage,\n                            alt: \"Mystic tarot card back design - Free AI tarot reading\",\n                            fill: true,\n                            className: \"object-cover\",\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\",\n                            priority: true,\n                            placeholder: \"blur\",\n                            blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Kcp/9k=\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this),\n                        !showBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-indigo-700 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white text-4xl\",\n                                children: \"\\uD83D\\uDD2E\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, this),\n                card && isRevealed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"absolute inset-0 backface-hidden\",\n                    initial: false,\n                    animate: {\n                        rotateY: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    style: {\n                        backfaceVisibility: 'hidden'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full h-full \".concat(isReversed ? 'rotate-180' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: card.imagePath,\n                                alt: \"\".concat(card.nameEn, \" tarot card - Free AI tarot reading and psychic interpretation\"),\n                                fill: true,\n                                className: \"object-cover\",\n                                sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\",\n                                priority: isRevealed,\n                                onError: ()=>{\n                                    console.error('Image failed to load:', card.imagePath);\n                                    console.error('Card data:', card);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold text-sm\",\n                                    children: [\n                                        card.nameEn,\n                                        isReversed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-yellow-400\",\n                                            children: \"↓\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-xs\",\n                                    children: isReversed ? 'Reversed' : 'Upright'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project\\\\chuhai\\\\tarot_new\\\\mytarot\\\\src\\\\components\\\\TarotCard.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n});\n_c1 = TarotCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TarotCard);\nvar _c, _c1;\n$RefreshReg$(_c, \"TarotCard$memo\");\n$RefreshReg$(_c1, \"TarotCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TarotCard.tsx\n"));

/***/ })

});